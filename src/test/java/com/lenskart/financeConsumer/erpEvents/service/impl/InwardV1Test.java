package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.grn.Grn;
import com.lenskart.financeConsumer.erpEvents.dto.po.PurchaseOrder;
import com.lenskart.financeConsumer.erpEvents.factory.InwardEventFactory;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class InwardV1Test {
    @Mock
    private InwardEventFactory inwardEventFactory;
    @Mock
    private NexsClient nexsClient;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;
    @InjectMocks
    private InwardV1 inwardV1;
    private PoEventCreationRequest poEventCreationRequest;
    private GrnEventCreationRequest grnEventCreationRequest;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(inwardV1, "isOldFlowActive", true);
        ReflectionTestUtils.setField(inwardV1, "oldPoConsumerTopic", "oldPoConsumerTopic");
        ReflectionTestUtils.setField(inwardV1, "oldGrnConsumerTopic", "oldGrnConsumerTopic");
        poEventCreationRequest = PoEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.SCM)
                .eventName(FinanceServiceEventTypes.PO)
                .version(Version.V1)
                .build();

        grnEventCreationRequest = GrnEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.SCM)
                .eventName(FinanceServiceEventTypes.GRN)
                .version(Version.V1)
                .build();
    }

    @Test
    void getVersion() {
        Assertions.assertEquals(Version.V1,inwardV1.getVersion());
    }

    @Test
    void getPoPayload() {
        Mockito.when(nexsClient.getPoPayload(ArgumentMatchers.any())).thenReturn(new PurchaseOrder());
        inwardV1.getPoPayload("ABC-123");
    }

    @Test
    void getGrnPayload() {
        Mockito.when(nexsClient.getGrnPayload(ArgumentMatchers.any())).thenReturn(new Grn());
        inwardV1.getGrnPayload("ABC-123");
    }

    @Test
    void publishPoToKafka() {
        inwardV1.publishPoToKafka(poEventCreationRequest);
    }

    @Test
    void publishGrnToKafka() {
        inwardV1.publishGrnToKafka(grnEventCreationRequest);
    }
}