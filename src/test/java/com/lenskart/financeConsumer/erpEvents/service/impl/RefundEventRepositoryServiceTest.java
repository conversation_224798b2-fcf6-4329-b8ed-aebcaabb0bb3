package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.erpEvents.dao.write.RefundEventWriteRepository;
import com.lenskart.financeConsumer.erpEvents.model.RefundEvent;
import com.lenskart.financeConsumer.exceptions.DatabaseException;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class RefundEventRepositoryServiceTest {

    @Mock
    RefundEventWriteRepository refundEventRepository;

    @InjectMocks
    RefundEventRepositoryService refundEventRepositoryService;

    RefundEvent refundEvent;

    @BeforeEach
    public void setUp() {
        refundEvent = RefundEvent.builder()
                .entityId(123)
                .retryCount(0)
                .instanceType("V1")
                .build();
    }


    @Test
    void getRefundByRefundId() {
        Mockito.when(refundEventRepository.findByEntityIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        RefundEvent refundEventResponse = refundEventRepositoryService.getRefundByRefundIdAndInstanceType(123, "V1");
        Assertions.assertEquals(refundEventResponse.getEntityId(), refundEvent.getEntityId());
        Assertions.assertEquals(refundEventResponse.getRetryCount(), refundEvent.getRetryCount());
    }

    @Test
    void getRefundByRefundIdException() {
        Mockito.when(refundEventRepository.findByEntityIdAndInstanceType(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException("Test exception"));
        DatabaseException exception = assertThrows(DatabaseException.class,
                                                   () -> refundEventRepositoryService.getRefundByRefundIdAndInstanceType(123, "V1"));
        assertEquals("Test exception", exception.getMessage());
    }

    @Test
    void saveRefund() {
        Mockito.when(refundEventRepository.save(Mockito.any())).thenReturn(refundEvent);
        RefundEvent refundEventResponse = refundEventRepositoryService.saveRefund(refundEvent);
        Assertions.assertEquals(refundEventResponse.getEntityId(), refundEvent.getEntityId());
        Assertions.assertEquals(refundEventResponse.getRetryCount(), refundEvent.getRetryCount());
    }

    @Test
    void saveRefundException() {
        Mockito.when(refundEventRepository.save(Mockito.any())).thenThrow(new RuntimeException("Test exception"));
        DatabaseException exception = assertThrows(DatabaseException.class,
                                                   () -> refundEventRepositoryService.saveRefund(refundEvent));
        assertEquals("Test exception", exception.getMessage());
    }

    @Test
    void findRefundFailureRecords() {
        List<RefundEvent> refundEventList = new ArrayList<>();
        refundEventList.add(refundEvent);
        Mockito.when(refundEventRepository.findRefundFailureRecords(Mockito.anyInt(),
                                                                    Mockito.anyInt(),
                                                                    Mockito.any())).thenReturn((refundEventList));
        List<RefundEvent> refundEventResponse = refundEventRepositoryService.findRefundFailureRecords(1, 2, LocalDateTime.now());
        Assertions.assertEquals(refundEventResponse.get(0).getEntityId(), refundEvent.getEntityId());
        Assertions.assertEquals(refundEventResponse.get(0).getRetryCount(), refundEvent.getRetryCount());
    }

    @Test
    void findRefundFailureRecordsException() {
        List<RefundEvent> refundEventList = new ArrayList<>();
        refundEventList.add(refundEvent);
        Mockito.when(refundEventRepository.findRefundFailureRecords(Mockito.anyInt(),
                                                                    Mockito.anyInt(),
                                                                    Mockito.any())).thenThrow(new RuntimeException("Test exception"));
        DatabaseException exception = assertThrows(DatabaseException.class,
                                                   () -> refundEventRepositoryService.findRefundFailureRecords(1, 2, LocalDateTime.now()));
        assertEquals("Test exception", exception.getMessage());
    }

    @Test
    void findByRefundIdInAndStatus() {
        List<RefundEvent> refundEventList = new ArrayList<>();
        refundEventList.add(refundEvent);
        Mockito.when(refundEventRepository.findByIdInAndStatusNot(Mockito.any(),
                                                                  Mockito.any())).thenReturn((refundEventList));
        List<RefundEvent> refundEventResponse = refundEventRepositoryService.findRetryEligibleRefundEvent(new ArrayList<>(), D365ResponseType.FAILED);
        Assertions.assertEquals(refundEventResponse.get(0).getEntityId(), refundEvent.getEntityId());
        Assertions.assertEquals(refundEventResponse.get(0).getRetryCount(), refundEvent.getRetryCount());
    }

    @Test
    void findByRefundIdInAndStatusException() {
        List<RefundEvent> refundEventList = new ArrayList<>();
        refundEventList.add(refundEvent);
        Mockito.when(refundEventRepository.findByIdInAndStatusNot(Mockito.any(),
                                                                  Mockito.any())).thenThrow(new RuntimeException("Test exception"));
        DatabaseException exception = assertThrows(DatabaseException.class,
                                                   () -> refundEventRepositoryService.findRetryEligibleRefundEvent(new ArrayList<>(), D365ResponseType.FAILED));
        assertEquals("Test exception", exception.getMessage());
    }
}