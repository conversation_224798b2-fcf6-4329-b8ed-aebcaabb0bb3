package com.lenskart.financeConsumer.erpEvents.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.erpEvents.clients.FinanceAdaptorClient;
import com.lenskart.financeConsumer.erpEvents.dao.write.PaymentD365SyncWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dao.write.PaymentHeaderRepository;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentCreationRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentPayloadResponse;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentPersistResponse;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.PaymentCreation;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.PaymentCreationDto;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.kafka.JunoPaymentD365ResponseData;
import com.lenskart.financeConsumer.erpEvents.model.PaymentD365Sync;
import com.lenskart.financeConsumer.erpEvents.model.PaymentHeader;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PaymentD365SyncServiceV2Test {

    @InjectMocks
    PaymentD365SyncServiceV2 paymentD365SyncServiceV2;

    @Mock
    private PaymentD365SyncWriteRepository paymentD365SyncWriteRepository;

    @Mock
    PaymentHeaderRepository paymentHeaderRepository;

    @Mock
    private  ObjectMapper objectMapper;

    @Mock
    private RestTemplate restTemplate ;

    @Mock
    private KafkaTemplate kafkaTemplate;

    @Mock
    private FinanceAdaptorClient financeAdaptorClientV2;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"cronPageSize",10);
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"junoOrderBaseUrl","http://url.com");
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"paymentTopic","topic");
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"financeAdapterUrl","fa.com");
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"isPaymentCronEnabled",true);
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"hoursBeforeCurrentTime",5);
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"subListBatchSize",2000);
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"restTemplate",restTemplate);
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"kafkaProducerTemplate",kafkaTemplate);



    }



    @Test
    void syncListOfPaymentsToD365() {

        List<PaymentD365Sync> paymentD365SyncList =new ArrayList<>();
        ReflectionTestUtils.setField(paymentD365SyncServiceV2,"subListBatchSize",0);
        PaymentD365Sync paymentD365Sync = new PaymentD365Sync();
        paymentD365Sync.setOrderId(101);
        Mockito.when(paymentD365SyncWriteRepository.findAllByIdIn(any())).thenReturn(Arrays.asList(paymentD365Sync)).thenReturn(null);
        paymentD365SyncServiceV2.syncListOfPaymentsToD365(Arrays.asList(1L));
        verify(kafkaTemplate,times(1)).send(any(),any(),any());

    }

    @Test
    void persistPayment() {
        JunoPaymentCreationRequestDto junoPaymentCreationRequestDto =  new JunoPaymentCreationRequestDto();
        PaymentCreation paymentCreation =new PaymentCreation();
        PaymentCreationDto paymentCreationDto =new PaymentCreationDto();
        paymentCreationDto.setDocumentNumber("ABBA");
        paymentCreation.setPaymentHeader(paymentCreationDto);
        junoPaymentCreationRequestDto.setPaymentCreation(paymentCreation);
        Mockito.when(paymentD365SyncWriteRepository.findByOrderIdAndIsExchangeAndInstanceType(any(),any(),any())).thenReturn(new PaymentD365Sync());

        JunoPaymentPersistResponse junoPaymentPersistResponse = paymentD365SyncServiceV2.persistPayment(junoPaymentCreationRequestDto);
        assertEquals("payment event successfully persisted!",junoPaymentPersistResponse.getResponse());

    }
    @Test
    void persistPaymentNotExistingAlready() {
        JunoPaymentCreationRequestDto junoPaymentCreationRequestDto =  new JunoPaymentCreationRequestDto();
        PaymentCreation paymentCreation =new PaymentCreation();
        PaymentCreationDto paymentCreationDto =new PaymentCreationDto();
        paymentCreationDto.setDocumentNumber("ABBA");
        paymentCreation.setPaymentHeader(paymentCreationDto);
        junoPaymentCreationRequestDto.setOrderId(1);
        junoPaymentCreationRequestDto.setIsExchange(true);
        junoPaymentCreationRequestDto.setPaymentCreation(paymentCreation);
        Mockito.when(paymentD365SyncWriteRepository.findByOrderIdAndIsExchangeAndInstanceType(any(),any(),any())).thenReturn(null);
        Mockito.when(paymentHeaderRepository.findByDocumentNumberAndIsExchange(any(),any())).thenReturn(new PaymentHeader());
        JunoPaymentPersistResponse junoPaymentPersistResponse = paymentD365SyncServiceV2.persistPayment(junoPaymentCreationRequestDto);
        assertEquals("payment event successfully persisted!",junoPaymentPersistResponse.getResponse());

    }





    @Test
    void syncJunoPaymentToD365() throws JsonProcessingException {
        JunoPaymentCreationRequestDto junoPaymentCreationRequestDto =  new JunoPaymentCreationRequestDto();
        PaymentCreation paymentCreation =new PaymentCreation();
        PaymentCreationDto paymentCreationDto =new PaymentCreationDto();
        paymentCreationDto.setDocumentNumber("ABBA");
        paymentCreation.setPaymentHeader(paymentCreationDto);
        junoPaymentCreationRequestDto.setPaymentCreation(paymentCreation);

        PaymentD365Sync paymentD365Sync = new PaymentD365Sync();
        paymentD365Sync.setOrderId(101);
        paymentD365Sync.setIsExchange(true);

        Mockito.when(paymentD365SyncWriteRepository.findByOrderIdAndIsExchangeAndInstanceType(any(),any(),any())).thenReturn(paymentD365Sync);
        ResponseEntity<String> responseEntity =  new ResponseEntity<>("responseBody", HttpStatus.OK);
        Mockito.when(financeAdaptorClientV2.genericApiCall(any(),any(),any())).thenReturn(responseEntity);

        Mockito.when(objectMapper.readValue(responseEntity.getBody(),ErpResponseDto.class)).thenReturn(ErpResponseDto.builder().response("posted success").build());
        paymentD365SyncServiceV2.syncJunoPaymentToD365(junoPaymentCreationRequestDto);

        responseEntity =  new ResponseEntity<>("responseBody", HttpStatus.BAD_GATEWAY);
        Mockito.when(financeAdaptorClientV2.genericApiCall(any(),any(),any())).thenReturn(responseEntity);
        paymentD365SyncServiceV2.syncJunoPaymentToD365(junoPaymentCreationRequestDto);

        verify(paymentD365SyncWriteRepository,times(2)).save(any());


    }


    @Test
    void syncFailedAndUnsyncedPaymentRecords() {
        PaymentD365Sync paymentD365Sync = new PaymentD365Sync();
        paymentD365Sync.setOrderId(101);
        Mockito.when(paymentD365SyncWriteRepository.findNonSyncedPaymentRecords(any(),any(),any())).thenReturn(Arrays.asList(paymentD365Sync)).thenReturn(null);
        paymentD365SyncServiceV2.syncFailedAndUnsyncedPaymentRecords();
        verify(kafkaTemplate,times(1)).send(any(),any(),any());


    }
}