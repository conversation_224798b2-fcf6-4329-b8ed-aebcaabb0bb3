package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.TestUtils.TestUtils;
import com.lenskart.financeConsumer.clients.JunoClient;
import com.lenskart.financeConsumer.erpEvents.dao.write.CustomerHeaderWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dao.write.CustomerWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dto.ErpRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.customer.CustomerContract;
import com.lenskart.financeConsumer.erpEvents.dto.customer.CustomerDto;
import com.lenskart.financeConsumer.erpEvents.dto.customer.request.CustomerCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.customer.response.CustomerPayloadResponse;
import com.lenskart.financeConsumer.erpEvents.dto.customer.response.CustomerResponseDto;
import com.lenskart.financeConsumer.erpEvents.dto.customer.response.JunoCustomerPayloadResponse;
import com.lenskart.financeConsumer.erpEvents.model.Customer;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CustomerServiceImplTest {

    @InjectMocks
    private CustomerD365ServiceV1 customerService;
    @Mock
    private  CustomerWriteRepository customerWriteRepository;
    @Mock
    private  JunoClient junoClient;
    @Mock
    private  GenericClientService genericClientService;
    @Mock
    private  KafkaTemplate kafkaProducerTemplate;
    @Mock
    private  CommonUtils commonUtils;
    @Mock
    private DateUtils dateUtils;
    @Mock
    private CustomerHeaderWriteRepository customerHeaderWriteRepository;

    private ErpResponseDto erpResponseDto;
    private ErpResponseDto failederpResponseDto;

    @BeforeEach
    public void init(){
        erpResponseDto = ErpResponseDto.builder()
                .entityId("DKIN_91703506_1302117185")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Successfully synced")
                .build();
        failederpResponseDto = ErpResponseDto.builder()
                .response("Connection time out")
                .d365SyncStatus(D365ResponseType.FAILED)
                .id(1L)
                .build();


    }

    @Test
     void  updateAndPublishToJunoSuccessTest(){
       CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);
       Customer customer= TestUtils.getObjectFromCustomerFile("Customer.json",Customer.class);

       when(customerWriteRepository.findById(customerDto.getId())).thenReturn(Optional.of(customer));

       customerService.updateAndPublishToJuno(customerDto);
       verify(customerWriteRepository,times(1)).findById(customerDto.getId());
       verify(customerWriteRepository,times(1)).save(customer);
    }
    @Test
    void  updateAndPublishToJunoFailedTest(){
        CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);
        Customer customer= TestUtils.getObjectFromCustomerFile("Customer.json",Customer.class);
        when(customerWriteRepository.findById(customerDto.getId())).thenReturn(Optional.empty());
        customerService.updateAndPublishToJuno(customerDto);
        verify(customerWriteRepository,times(1)).findById(customerDto.getId());
        verify(customerWriteRepository,times(0)).save(customer);
    }
    @Test
    void getKeySuccessTest(){
        CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);
        String expectedKey="DKIN_91703506_1302117185";
        String key=customerService.getEntityId(customerDto);
        assertNotNull(key);
        assertEquals(expectedKey,key);
    }
    @Test
    void retrySuccessTestCase(){
        Customer customer= TestUtils.getObjectFromCustomerFile("Customer.json",Customer.class);
        List<Customer> customerList= new ArrayList<>();
        customerList.add(customer);
        customerService.processFailedRecords(customerList);
        verify(customerWriteRepository,times(1)).save(customer);
    }
    @Test
    void retryFailedTestCase(){
        Customer customer= TestUtils.getObjectFromCustomerFile("Customer.json",Customer.class);
        List<Customer> customerList= new ArrayList<>();
        customerList.add(customer);
        doThrow(new RuntimeException("Unknown Error"))
                .when(customerWriteRepository).save(any());

        customerService.processFailedRecords(customerList);
        verify(customerWriteRepository,times(1)).save(customer);
    }
    @Test
    void processEventSuccessTest(){
        CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);

        CustomerContract customerContract=TestUtils.getObjectFromCustomerFile("CustomerContract.json",CustomerContract.class);

        ErpRequestDto erpRequestDto = ErpRequestDto.builder()
                .id(customerDto.getId())
                .entityId(customerDto.getEntityId())
                .requestPayload(customerContract)
                .event(FinanceServiceEventTypes.CUSTOMER)
                .build();

        when(commonUtils.buildAndGetErpRequestDto(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any()))
                .thenReturn(erpRequestDto);

        when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any()))
                .thenReturn(erpResponseDto);


       ErpResponseDto erpResponseDTO= customerService.processEvent(customerDto);
       assertNotNull(erpResponseDTO);
       assertEquals("DKIN_91703506_1302117185",commonUtils.buildAndGetErpRequestDto(customerDto.getId(),customerDto.getEntityId(),customerContract, FinanceServiceEventTypes.CUSTOMER).getEntityId());
       assertEquals(erpResponseDto.getEntityId(),erpResponseDTO.getEntityId());
    }
    @Test
    void processEvent5xxSuccessTest(){
        CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);
        CustomerContract customerContract=TestUtils.getObjectFromCustomerFile("CustomerContract.json",CustomerContract.class);

        ErpRequestDto erpRequestDto = ErpRequestDto.builder()
                .id(customerDto.getId())
                .entityId(customerDto.getEntityId())
                .requestPayload(customerContract)
                .event(FinanceServiceEventTypes.CUSTOMER)
                .build();

        when(commonUtils.buildAndGetErpRequestDto(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any()))
                .thenReturn(erpRequestDto);

        when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any()))
                .thenReturn(failederpResponseDto);

        ErpResponseDto erpResponseDTO= customerService.processEvent(customerDto);
        assertNotNull(erpResponseDTO);
        assertEquals("DKIN_91703506_1302117185",commonUtils.buildAndGetErpRequestDto(customerDto.getId(),customerDto.getEntityId(),customerContract, FinanceServiceEventTypes.CUSTOMER).getEntityId());
        assertEquals("Connection time out",erpResponseDTO.getResponse());
    }
    @Test
    void processEventHandledExceptionSuccessfullyTest(){
        CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);
        CustomerContract customerContract=TestUtils.getObjectFromCustomerFile("CustomerContract.json",CustomerContract.class);

        ErpRequestDto erpRequestDto = ErpRequestDto.builder()
                .id(customerDto.getId())
                .entityId(customerDto.getEntityId())
                .requestPayload(customerContract)
                .event(FinanceServiceEventTypes.CUSTOMER)
                .build();

        when(commonUtils.buildAndGetErpRequestDto(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any()))
                .thenReturn(erpRequestDto);

        doThrow(new RuntimeException("Redis connection timeout"))
                .when(commonUtils).callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any());

        when(genericClientService.getErrorMessage(any(Exception.class)))
                .thenAnswer(invocation -> {
                    Exception exception = invocation.getArgument(0);
                    return "Captured Error: " + exception.getMessage();
                });

        ErpResponseDto erpResponseDTO= customerService.processEvent(customerDto);
        assertNotNull(erpResponseDTO);
        assertEquals("DKIN_91703506_1302117185",commonUtils.buildAndGetErpRequestDto(customerDto.getId(),customerDto.getEntityId(),customerContract, FinanceServiceEventTypes.CUSTOMER).getEntityId());
        assertTrue(erpResponseDTO.getResponse().contains("Captured Error: Redis connection timeout"));
    }

    @Test
    @SneakyThrows
    void getPayloadSuccessTest() {
        CustomerDto customerDto= TestUtils.getObjectFromCustomerFile("CustomerDto.json",CustomerDto.class);

        CustomerCreationRequest customerPayload=TestUtils.getObjectFromCustomerFile("CustomerCreationRequest.json",CustomerCreationRequest.class);

        JunoCustomerPayloadResponse junoCustomerPayloadResponse=TestUtils.getObjectFromCustomerFile("JunoCustomerPayloadResponse.json", JunoCustomerPayloadResponse.class);

        CustomerPayloadResponse customerPayloadResponse=TestUtils.getObjectFromCustomerFile("CustomerPayloadResponse.json",CustomerPayloadResponse.class);

        when(junoClient.getCustomerPayload(customerDto.getCustomerId(),customerDto.getOrderId(),customerDto.getLegalEntity(),customerDto.getInstanceType())).
                thenReturn(junoCustomerPayloadResponse);


        CustomerPayloadResponse payloadResponse= customerService.getPayload(customerDto.getOrderId(),customerDto.getCustomerId(),customerDto.getLegalEntity(),customerDto.getInstanceType());

        assertNotNull(payloadResponse);
        assertEquals(customerPayload.getCustomerId(),customerDto.getCustomerId());

        assertEquals(customerPayload.getPayload().getCustAccount(),String.valueOf(customerDto.getCustomerId()));
        assertEquals(customerPayloadResponse.getPayload().getCustomerName(),customerPayload.getPayload().getCustomerName());
        assertEquals(customerPayloadResponse.getPayload().getCustomerGroup(),customerPayload.getPayload().getCustomerGroup());

    }
    @SneakyThrows
    @Test
    void getPayloadSuccessWithAnotherLegalEntityTest() {
        CustomerDto customerDto = TestUtils.getObjectFromCustomerFile("CustomerDto.json", CustomerDto.class);

        doThrow(new RuntimeException("Payload not found for legalEntity " + customerDto.getLegalEntity()))
                .when(junoClient).getCustomerPayload(customerDto.getCustomerId(), customerDto.getOrderId(), customerDto.getLegalEntity(),customerDto.getInstanceType());

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            junoClient.getCustomerPayload(customerDto.getCustomerId(), customerDto.getOrderId(), customerDto.getLegalEntity(),customerDto.getInstanceType());
        });

        when(genericClientService.getErrorMessage(any(Exception.class)))
                .thenReturn(exception.getMessage());

        CustomerPayloadResponse customerPayload = customerService.getPayload(customerDto.getOrderId(), customerDto.getCustomerId(), customerDto.getLegalEntity(),customerDto.getInstanceType());

        assertEquals("Payload not found for legalEntity " + customerDto.getLegalEntity(), exception.getMessage());

        assertFalse(customerPayload.getStatus());
        assertTrue(exception.getMessage().contains( customerPayload.getMessage()));
    }


    @Test
    void createCustomerSuccessTest(){
        CustomerCreationRequest customerCreationRequest=TestUtils.getObjectFromCustomerFile("CustomerCreationRequest.json",CustomerCreationRequest.class);
        Customer customer=TestUtils.getObjectFromCustomerFile("Customer.json",Customer.class);

        CustomerResponseDto customerResponseDTO= CustomerResponseDto.builder()
                .isSuccess(true)
                .build();

        Mockito.when(customerWriteRepository.findByCustomerIdAndOrderIdAndLegalEntity(customerCreationRequest.getCustomerId()
                ,customerCreationRequest.getOrderId(),customerCreationRequest.getLegalEntity()))
                .thenReturn(Optional.empty());

        Mockito.when(customerWriteRepository.save(ArgumentMatchers.any()))
                .thenReturn(customer);

        CustomerResponseDto customerResponseDto=customerService.createCustomer(customerCreationRequest);
        assertNotNull(customerResponseDto);
        assertEquals(customerResponseDTO.getIsSuccess(),customerResponseDto.getIsSuccess());
        Mockito.verify(customerWriteRepository,times(1)).save(ArgumentMatchers.any());
        Mockito.verify(customerWriteRepository,times(1)).findByCustomerIdAndOrderIdAndLegalEntity(customerCreationRequest.getCustomerId()
                ,customerCreationRequest.getOrderId(),customerCreationRequest.getLegalEntity());

    }
}
