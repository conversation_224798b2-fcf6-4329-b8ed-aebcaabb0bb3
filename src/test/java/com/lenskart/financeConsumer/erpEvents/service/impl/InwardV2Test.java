package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.grn.Grn;
import com.lenskart.financeConsumer.erpEvents.dto.grn.ReceiptHeader;
import com.lenskart.financeConsumer.erpEvents.dto.grn.ReceiptLines;
import com.lenskart.financeConsumer.erpEvents.dto.po.PurchaseOrder;
import com.lenskart.financeConsumer.erpEvents.factory.InwardEventFactory;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;

@ExtendWith(MockitoExtension.class)
class InwardV2Test {
    @Mock
    private InwardEventFactory inwardEventFactory;
    @Mock
    private NexsClient nexsClient;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;
    @InjectMocks
    private InwardV2 inwardV2;
    private PoEventCreationRequest poEventCreationRequest;
    private GrnEventCreationRequest grnEventCreationRequest;
    private PurchaseOrder purchaseOrder;
    private Grn grn;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(inwardV2, "isNewFlowActive", true);
        ReflectionTestUtils.setField(inwardV2, "newPoConsumerTopic", "newPoConsumerTopic");
        ReflectionTestUtils.setField(inwardV2, "newGrnConsumerTopic", "newGrnConsumerTopic");
        poEventCreationRequest = PoEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.SCM)
                .eventName(FinanceServiceEventTypes.PO)
                .version(Version.V2)
                .build();

        grnEventCreationRequest = GrnEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.SCM)
                .eventName(FinanceServiceEventTypes.GRN)
                .version(Version.V2)
                .build();

        ArrayList<ReceiptLines> receiptLines = new ArrayList<>();
        receiptLines.add(ReceiptLines.builder().serialNumber("124").itemNumber("")
                                                                .lineNumber(1).quantity(2).rejectedQty(0).build());
        ReceiptHeader receiptHeader= ReceiptHeader.builder().receiptLines(receiptLines)
                .legalEntity("LKIN")
                .purchId("123")
                .productReceipt("abc")
                .created_by("test")
                .updated_by("test").build();
        grn= Grn.builder().ReceiptHeader(receiptHeader).build();
    }

    @Test
    void getVersion() {
        Assertions.assertEquals(Version.V2, inwardV2.getVersion());
    }

    @Test
    void getPoPayload() {
        Mockito.when(nexsClient.getPoPayload(ArgumentMatchers.any())).thenReturn(new PurchaseOrder());
        inwardV2.getPoPayload("ABC-123");
    }

    @Test
    void getGrnPayload() {
        Mockito.when(nexsClient.getGrnPayload(ArgumentMatchers.any())).thenReturn(grn);
        inwardV2.getGrnPayload("ABC-123");
    }

    @Test
    void publishPoToKafka() {
        inwardV2.publishPoToKafka(poEventCreationRequest);
    }

    @Test
    void publishGrnToKafka() {
        inwardV2.publishGrnToKafka(grnEventCreationRequest);
    }
}