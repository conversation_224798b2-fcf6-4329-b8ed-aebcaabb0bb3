package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.erpEvents.constant.enums.MovementType;
import com.lenskart.financeConsumer.erpEvents.dto.BaseResponseDto;
import com.lenskart.financeConsumer.erpEvents.dto.InventoryAdjustmentEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.movementJournal.InventoryAdjustmentLineItem;
import com.lenskart.financeConsumer.erpEvents.factory.InventoryAdjustVersionFactory;
import com.lenskart.financeConsumer.erpEvents.model.InventoryAdjust;
import com.lenskart.financeConsumer.erpEvents.service.InventoryAdjustVersionService;
import com.lenskart.financeConsumer.erpEvents.service.InventoryAdjustWriteRepositoryService;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class InventoryAdjustmentServiceImplTest {
    @Mock
    private InventoryAdjustWriteRepositoryService inventoryAdjustWriteRepositoryService;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;
    @Mock
    private CommonUtils commonUtils;
    @Mock
    private InventoryAdjustVersionService inventoryAdjustVersionService = new InventoryAdjustV1();
    @Mock
    private InventoryAdjustVersionFactory inventoryAdjustVersionFactory;
    @InjectMocks
    private InventoryAdjustmentServiceImpl inventoryAdjustmentService;

    private InventoryAdjustmentEventCreationRequest inventoryAdjustmentEventCreationRequest;
    private ErpResponseDto erpResponseDto;
    private InventoryAdjust inventoryAdjust;

    @BeforeEach
    public void setUp() {
        inventoryAdjustmentEventCreationRequest = InventoryAdjustmentEventCreationRequest.builder()
                .inventoryAdjustmentList(Collections.singletonList(InventoryAdjustmentLineItem.builder()
                                                                           .barcode("YYYS00269696")
                                                                           .event("ADD_INVENTORY")
                                                                           .facility("LKS18")
                                                                           .legalEntity("LKIN")
                                                                           .productId(131315)
                                                                           .transactionDate("2025-02-04 07:57:31")
                                                                           .transactionId(1129)
                                                                           .transactionType("POS")
                                                                           .unitPriceWithTax(1135.61f)
                                                                           .build()))
                .source(Source.NEXS.name())
                .build();
        erpResponseDto = ErpResponseDto.builder()
                .entityId("ADD_INVENTORY/YYYS00269696/POS")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Successfully synced")
                .build();
        inventoryAdjust = InventoryAdjust.builder()
                .id(1L)
                .barcode("YYYS00269696")
                .event(FinanceServiceEventTypes.ADD_INVENTORY)
                .facility("LKS18")
                .legalEntity("LKIN")
                .productId(131315)
                .transactionDate("2025-02-04 07:57:31")
                .transactionId(1129)
                .transactionType(MovementType.POS)
                .unitPriceWithTax(1135.61f)
                .retryCount(0).build();

        erpResponseDto = ErpResponseDto.builder()
                .entityId("ADD_INVENTORY/YYYS00269696/POS")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Successfully synced")
                .id(1L)
                .build();
        ReflectionTestUtils.setField(inventoryAdjustmentService, "instanceTypeV1Enable", true);
        ReflectionTestUtils.setField(inventoryAdjustmentService, "instanceTypeV2Enable", false);

    }

    @Test
    void adjustInventory() {
        Mockito.when(inventoryAdjustWriteRepositoryService.findByTransactionTypeAndBarcodeAndProductId(
                ArgumentMatchers.any(), ArgumentMatchers.anyString(),ArgumentMatchers.anyInt(),ArgumentMatchers.any() )).thenReturn(null);
        Mockito.when(inventoryAdjustVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inventoryAdjustVersionService);
        BaseResponseDto baseResponseDto =inventoryAdjustmentService.createAdjustInventoryEvent(inventoryAdjustmentEventCreationRequest);
        Assertions.assertEquals("Entry Successfully created",baseResponseDto.getResponse());
    }

    @Test
    void processAdjustmentAddInventory() {
        Mockito.when(inventoryAdjustWriteRepositoryService.findByDocumentNumber("ADD_INVENTORY/YYYS00269696/POS")).thenReturn(
                inventoryAdjust);
        Mockito.when(inventoryAdjustVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inventoryAdjustVersionService);
        Mockito.when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.anyLong())).thenReturn(erpResponseDto);
        inventoryAdjustmentService.processAdjustInventoryEvent("ADD_INVENTORY/YYYS00269696/POS");
    }

    @Test
    void processAdjustmentStartRetireNegative() {
        inventoryAdjust.setEvent(FinanceServiceEventTypes.START_RETIRE);
        inventoryAdjust.setTransactionType(MovementType.NEG);
        Mockito.when(inventoryAdjustVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inventoryAdjustVersionService);
        Mockito.when(inventoryAdjustWriteRepositoryService.findByDocumentNumber(ArgumentMatchers.any())).thenReturn(inventoryAdjust);
        Mockito.when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.anyLong())).thenReturn(erpResponseDto);
        Mockito.when(inventoryAdjustWriteRepositoryService.findByDocumentNumber(ArgumentMatchers.any())).thenReturn(inventoryAdjust);
        inventoryAdjustmentService.processAdjustInventoryEvent("START_RETIRE/YYYS00269674/NEG");
    }
    @Test
    void processAdjustmentStartRetirePositive() {
        inventoryAdjust.setEvent(FinanceServiceEventTypes.START_RETIRE);
        inventoryAdjust.setTransactionType(MovementType.POS);
        Mockito.when(inventoryAdjustWriteRepositoryService.findByDocumentNumber(ArgumentMatchers.any())).thenReturn(inventoryAdjust);
        Mockito.when(inventoryAdjustWriteRepositoryService.findByTransactionTypeAndBarcodeAndInstanceType(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(null);
        inventoryAdjustmentService.processAdjustInventoryEvent("START_RETIRE/YYYS00269674/POS");
    }

    @Test
    void processAdjustmentAddInventoryException() {
        Mockito.when(inventoryAdjustWriteRepositoryService.findByDocumentNumber("ADD_INVENTORY/YYYS00269696/POS")).thenReturn(
                inventoryAdjust);
        Mockito.when(inventoryAdjustVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inventoryAdjustVersionService);
        Mockito.when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.anyLong())).thenThrow(new RuntimeException("Test Exception"));
        inventoryAdjustmentService.processAdjustInventoryEvent("ADD_INVENTORY/YYYS00269696/POS");
    }

    @Test
    void retryInventoryAdjustment() {
        List<InventoryAdjust> inventoryAdjustList = new ArrayList<>();
        inventoryAdjustList.add(inventoryAdjust);

        InventoryAdjust inventoryAdjustStartRetirePos = new InventoryAdjust();
        BeanUtils.copyProperties(inventoryAdjust,inventoryAdjustStartRetirePos);
        inventoryAdjustStartRetirePos.setTransactionType(MovementType.POS);
        inventoryAdjustStartRetirePos.setEvent(FinanceServiceEventTypes.START_RETIRE);
        inventoryAdjustList.add(inventoryAdjustStartRetirePos);

        InventoryAdjust inventoryAdjustStartRetireNeg = new InventoryAdjust();
        BeanUtils.copyProperties(inventoryAdjust,inventoryAdjustStartRetireNeg);
        inventoryAdjustStartRetireNeg.setTransactionType(MovementType.NEG);
        inventoryAdjustStartRetireNeg.setEvent(FinanceServiceEventTypes.START_RETIRE);
        inventoryAdjustList.add(inventoryAdjustStartRetireNeg);

        Mockito.when(inventoryAdjustVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inventoryAdjustVersionService);
        Mockito.when(inventoryAdjustWriteRepositoryService.findByIdsInAndStatusNot(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(inventoryAdjustList);
        inventoryAdjustmentService.retryInventoryAdjustment(Arrays.asList(1L,2L,3L));
    }

    @Test
    void syncFailureEntries() {
    }
}