package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.factory.InwardVersionFactory;
import com.lenskart.financeConsumer.erpEvents.model.InwardEvent;
import com.lenskart.financeConsumer.erpEvents.service.InwardRepositoryService;
import com.lenskart.financeConsumer.erpEvents.service.InwardVersionService;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;

@ExtendWith(MockitoExtension.class)
class InwardScmImplTest {

    @Mock
    private InwardRepositoryService inwardRepositoryService;
    @Mock
    private InwardVersionFactory inwardVersionFactory;
    @Mock
    private CommonUtils commonUtils;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;

    @Mock
    private ObjectHelper objectHelper;

    @InjectMocks
    private InwardScmImpl inwardScm;
    @Mock
    private InwardVersionService inwardVersionService = new InwardV1();

    private InwardEvent inwardEventPo;
    private InwardEvent inwardEventGrn;
    private ErpResponseDto erpResponseDto;
    private PoEventCreationRequest poEventCreationRequest;
    private GrnEventCreationRequest grnEventCreationRequest;


    @BeforeEach
    public void setUp() {
        inwardEventPo = InwardEvent.builder()
                .event(FinanceServiceEventTypes.PO).instanceType(Version.V1).entityId("ABC-123").build();
        inwardEventGrn = InwardEvent.builder()
                .event(FinanceServiceEventTypes.GRN).instanceType(Version.V1).entityId("ABC-123").build();
        erpResponseDto = ErpResponseDto.builder()
                .entityId("ABC-123")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Not supported")
                .build();
        poEventCreationRequest = PoEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.SCM)
                .eventName(FinanceServiceEventTypes.PO)
                .version(Version.V1)
                .build();

        grnEventCreationRequest = GrnEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.SCM)
                .eventName(FinanceServiceEventTypes.GRN)
                .version(Version.V1)
                .build();
    }

    @Test
    void getSource() {
        Assertions.assertEquals(Source.SCM, inwardScm.getSource());
    }

    @Test
    void publishPo() {
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        inwardScm.publishPo(poEventCreationRequest);

    }

    @Test
    void publishGrn() {
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        inwardScm.publishGrn(grnEventCreationRequest);
    }

    @Test
    void processPoBySource() {
        Mockito.when(commonUtils.buildAndGetErpResponseDto(ArgumentMatchers.any(),
                                                          ArgumentMatchers.any(),
                                                          ArgumentMatchers.any())).thenReturn(erpResponseDto);
        ErpResponseDto responseDto = inwardScm.processPoBySource(poEventCreationRequest);
        Assertions.assertEquals("Not supported",responseDto.getResponse());
    }


    @Test
    void processGrnBySource() {
        Mockito.when(commonUtils.buildAndGetErpResponseDto(ArgumentMatchers.any(),
                                                           ArgumentMatchers.any(),
                                                           ArgumentMatchers.any())).thenReturn(erpResponseDto);
        ErpResponseDto responseDto = inwardScm.processGrnBySource(grnEventCreationRequest);
        Assertions.assertEquals("Not supported",responseDto.getResponse());
    }

}