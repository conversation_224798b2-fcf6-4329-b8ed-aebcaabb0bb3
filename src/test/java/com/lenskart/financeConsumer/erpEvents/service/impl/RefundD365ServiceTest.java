package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.Refund;
import com.lenskart.financeConsumer.dao.D365RefundTrackingRepository;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.dto.d365requests.refund.PaymentHeader;
import com.lenskart.financeConsumer.erpEvents.dao.write.RefundHeaderWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dto.BaseResponseDto;
import com.lenskart.financeConsumer.erpEvents.dto.ErpRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.RefundSyncRequest;
import com.lenskart.financeConsumer.erpEvents.model.RefundEvent;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.util.RefundUtil;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundD365ServiceTest {

    @Mock
    RefundEventRepositoryService refundEventRepositoryService;
    @Mock
    KafkaTemplate kafkaTemplate;
    @Mock
    RefundPayloadGenerationV1 refundPayloadGenerationV1;
    @Mock
    RefundPayloadGenerationV2 refundPayloadGenerationV2;
    @Mock
    GenericClientService genericClientService;
    @Mock
    RefundUtil refundUtil;
    @Mock
    ObjectMapper objectMapper;
    @Mock
    CommonUtils commonUtils;
    @Mock
    D365RefundTrackingRepository d365RefundTrackingRepository;
    @Mock
    RefundHeaderWriteRepository refundHeaderWriteRepository;
    @Mock
    ObjectHelper objectHelper;
    @Mock
    ObjectUtils objectUtils;
    @InjectMocks
    RefundD365Service refundD365Service;

    RefundSyncRequest refundSyncRequest;
    Refund refund;
    RefundEvent refundEvent;
    ErpResponseDto responseDto;
    ErpRequestDto requestDto;
    SchedulerConfig schedulerConfig;
    KafkaEventDto kafkaDto;

    @BeforeEach
    void setUp() {
        refund = new Refund();
        refund.setId(123);
        refund.setRefRefundId(123);
        refund.setOrderId(321);
        refund.setStatus("Success");
        refund.setRefundAmount(100.00);

        refundEvent = RefundEvent.builder()
                .entityId(123)
                .instanceType("V1")
                .build();

        refundSyncRequest = RefundSyncRequest.builder()
                .refund(refund)
                .eventName(FinanceServiceEventTypes.REFUND)
                .source(Source.CES)
                .eventId("123")
                .isNewFlowRefund(true)
                .apiVersion("V1")
                .instanceType("V1")
                .legalEntity("LKIN")
                .build();

        responseDto = ErpResponseDto.builder()
                .id(123L)
                .entityId("123")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Created Successfully")
                .build();

        requestDto = ErpRequestDto.builder()
                .event(FinanceServiceEventTypes.REFUND)
                .id(123L)
                .requestPayload(null)
                .entityId("123")
                .build();

        schedulerConfig = SchedulerConfig.builder()
                .recordInterval("20")
                .lastProcessed("10")
                .enabled(Boolean.TRUE)
                .recordLimit(20)
                .build();

        kafkaDto = KafkaEventDto.builder()
                .documentPayload("")
                .eventType("Refund")
                .shipmentId("123")
                .incrementId(123)
                .facilityCode("DK02")
                .build();
    }

    @Test
    void createRefundAlreadyExists() {
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        BaseResponseDto responseDto = refundD365Service.createRefund(refundSyncRequest);
        Assertions.assertEquals(responseDto.isSuccess(), Boolean.TRUE);
        Assertions.assertEquals(responseDto.getResponse(), "Event created in finance layer successfully!");
    }

    @Test
    void createRefund() {
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(null);
        Mockito.when(refundEventRepositoryService.saveRefund(Mockito.any())).thenReturn(refundEvent);
        BaseResponseDto responseDto = refundD365Service.createRefund(refundSyncRequest);
        Assertions.assertEquals(responseDto.isSuccess(), Boolean.TRUE);
        Assertions.assertEquals(responseDto.getResponse(), "Event created in finance layer successfully!");
    }

    @Test
    void processRefundV1RefundDoesNotExists() {
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(null);
        refundD365Service.processRefundV1(refundSyncRequest);
        Mockito.verify(refundEventRepositoryService, Mockito.never()).saveRefund(Mockito.any());
    }

    @Test
    void processRefundV1RefundAmountNull() {
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        refundD365Service.processRefundV1(refundSyncRequest);
    }

    @Test
    void procesRefundV1Success() {
        refundEvent.setRefundAmount(100.00);
        refundEvent.setRetryCount(1);
        PaymentHeader payload = new PaymentHeader();
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        when(refundPayloadGenerationV1.generateRefundPayload(any(), any())).thenReturn(payload);
        when(commonUtils.buildAndGetErpRequestDto(any(), any(), any(), any())).thenReturn(ErpRequestDto.builder()
                                                                                                  .id(123L)
                                                                                                  .entityId("123")
                                                                                                  .requestPayload(payload)
                                                                                                  .event(FinanceServiceEventTypes.REFUND)
                                                                                                  .build());
        when(commonUtils.callFinanceAdaptor(any(), any(), any())).thenReturn(responseDto);

        refundD365Service.processRefundV1(refundSyncRequest);

    }

    @Test
    void procesRefundV1Failure() {
        refundEvent.setRefundAmount(100.00);
        refundEvent.setRetryCount(1);
        PaymentHeader payload = new PaymentHeader();
        responseDto.setD365SyncStatus(D365ResponseType.FAILED);
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        when(refundPayloadGenerationV1.generateRefundPayload(any(), any())).thenReturn(payload);
        when(commonUtils.buildAndGetErpRequestDto(any(), any(), any(), any())).thenReturn(ErpRequestDto.builder()
                                                                                                  .id(123L)
                                                                                                  .entityId("123")
                                                                                                  .requestPayload(payload)
                                                                                                  .event(FinanceServiceEventTypes.REFUND)
                                                                                                  .build());
        when(commonUtils.callFinanceAdaptor(any(), any(), any())).thenReturn(responseDto);

        refundD365Service.processRefundV1(refundSyncRequest);

    }

    @Test
    void procesRefundV1NullResponse() {
        refundEvent.setRefundAmount(100.00);
        PaymentHeader payload = new PaymentHeader();
        responseDto.setD365SyncStatus(D365ResponseType.FAILED);
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        when(refundPayloadGenerationV1.generateRefundPayload(any(), any())).thenReturn(payload);
        when(commonUtils.buildAndGetErpRequestDto(any(), any(), any(), any())).thenReturn(ErpRequestDto.builder()
                                                                                                  .id(123L)
                                                                                                  .entityId("123")
                                                                                                  .requestPayload(payload)
                                                                                                  .event(FinanceServiceEventTypes.REFUND)
                                                                                                  .build());
        when(commonUtils.callFinanceAdaptor(any(), any(), any())).thenReturn(null);

        refundD365Service.processRefundV1(refundSyncRequest);

    }

    @Test
    void procesRefundV1Exception() {
        refundEvent.setRefundAmount(100.00);
        refundEvent.setRetryCount(1);
        PaymentHeader payload = new PaymentHeader();
        responseDto.setD365SyncStatus(D365ResponseType.FAILED);
        Mockito.when(refundEventRepositoryService.getRefundByRefundIdAndInstanceType(Mockito.any(), Mockito.any())).thenReturn(refundEvent);
        when(refundPayloadGenerationV1.generateRefundPayload(any(), any())).thenReturn(payload);
        when(commonUtils.buildAndGetErpRequestDto(any(), any(), any(), any())).thenReturn(ErpRequestDto.builder()
                                                                                                  .id(123L)
                                                                                                  .entityId("123")
                                                                                                  .requestPayload(payload)
                                                                                                  .event(FinanceServiceEventTypes.REFUND)
                                                                                                  .build());
        when(commonUtils.callFinanceAdaptor(any(), any(), any())).thenReturn(responseDto);
        doThrow(RuntimeException.class).when(refundUtil).updateRefundD365SyncFlag(any(), any(), any(), any());
        refundD365Service.processRefundV1(refundSyncRequest);

    }

    @Test
    void syncRefundNullPayload() {
        when(refundPayloadGenerationV1.generateRefundPayload(any(), any())).thenReturn(null);
        refundD365Service.syncRefundV1(refundSyncRequest, refund, Version.V1);
    }

    @Test
    void syncRefund() {
        when(refundPayloadGenerationV1.generateRefundPayload(any(), any())).thenReturn(new PaymentHeader());
        when(commonUtils.buildAndGetErpRequestDto(any(), any(), any(), any())).thenReturn(requestDto);
        when(commonUtils.callFinanceAdaptor(any(), any(), any())).thenReturn(responseDto);
        refundD365Service.syncRefundV1(refundSyncRequest, refund, Version.V1);
    }


    @Test
    void syncFailedRefundEvents() {
        List<RefundEvent> refundEventList = new ArrayList<>();
        refundEventList.add(refundEvent);
        when(refundEventRepositoryService.findRefundFailureRecords(anyInt(), anyInt(), any()))
                .thenReturn(refundEventList);

        refundD365Service.syncFailedRefundEvents(schedulerConfig);
        verify(refundEventRepositoryService, times(1)).findRefundFailureRecords(anyInt(), anyInt(), any());
    }


    @Test
    void retryEvents() {
        when(refundEventRepositoryService.findRetryEligibleRefundEvent(any(), any()))
                .thenReturn(Arrays.asList(refundEvent));

        BaseResponseDto response = refundD365Service.retryEvents(Arrays.asList(1L));

        Assertions.assertTrue(response.isSuccess());
        Assertions.assertEquals("Refund events triggered successfully for syncing", response.getResponse());
        verify(refundEventRepositoryService, times(1)).findRetryEligibleRefundEvent(any(), any());
    }

    @Test
    void retryEventsException() {
        when(refundEventRepositoryService.findRetryEligibleRefundEvent(any(), any()))
                .thenThrow(RuntimeException.class);

        BaseResponseDto response = refundD365Service.retryEvents(Arrays.asList(1L));

        Assertions.assertFalse(response.isSuccess());
        verify(refundEventRepositoryService, times(1)).findRetryEligibleRefundEvent(any(), any());
    }

    @Test
    void getRefundObject() {
        when(commonUtils.getRefundObject(anyInt())).thenReturn(refundSyncRequest);
        Refund response = refundD365Service.getRefundObject(1);
        Assertions.assertEquals(refundSyncRequest.getRefund().getId(), response.getId());
    }

    @Test
    void getRefundObjectNull() {
        when(commonUtils.getRefundObject(anyInt())).thenReturn(null);
        refundD365Service.getRefundObject(1);
    }
}

