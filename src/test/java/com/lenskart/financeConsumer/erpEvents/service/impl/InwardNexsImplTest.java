package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.erpEvents.dto.ErpRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.po.PurchaseOrder;
import com.lenskart.financeConsumer.erpEvents.factory.InwardVersionFactory;
import com.lenskart.financeConsumer.erpEvents.model.InwardEvent;
import com.lenskart.financeConsumer.erpEvents.service.InwardRepositoryService;
import com.lenskart.financeConsumer.erpEvents.service.InwardVersionService;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class InwardNexsImplTest {

    @Mock
    private InwardRepositoryService inwardRepositoryService;
    @Mock
    private InwardVersionFactory inwardVersionFactory;
    @Mock
    private CommonUtils commonUtils;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;

    @Mock
    private ObjectHelper objectHelper;

    @InjectMocks
    private InwardNexsImpl inwardNexs;
    @Mock
    private InwardVersionService inwardVersionService = new InwardV1();

    private InwardEvent inwardEventPo;
    private InwardEvent inwardEventGrn;
    private ErpResponseDto erpResponseDto;
    private PoEventCreationRequest poEventCreationRequest;
    private GrnEventCreationRequest grnEventCreationRequest;


    @BeforeEach
    public void setUp() {
        inwardEventPo = InwardEvent.builder()
                .event(FinanceServiceEventTypes.PO).instanceType(Version.V1).entityId("ABC-123").build();
        inwardEventGrn = InwardEvent.builder()
                .event(FinanceServiceEventTypes.GRN).instanceType(Version.V1).entityId("ABC-123").build();
        erpResponseDto = ErpResponseDto.builder()
                .entityId("ABC-123")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Successfully synced")
                .build();
        poEventCreationRequest = PoEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.NEXS)
                .eventName(FinanceServiceEventTypes.PO)
                .version(Version.V1)
                .build();

        grnEventCreationRequest = GrnEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.NEXS)
                .eventName(FinanceServiceEventTypes.GRN)
                .version(Version.V1)
                .build();

        ReflectionTestUtils.setField(inwardNexs, "nexsPoConsumerTopic", "nexsPoConsumerTopic");
        ReflectionTestUtils.setField(inwardNexs, "nexsGrnConsumerTopic", "nexsGrnConsumerTopic");


    }

    @Test
    void getSource() {
        Assertions.assertEquals(Source.NEXS,inwardNexs.getSource());
    }

    @Test
    void publishPo() {
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        inwardNexs.publishPo(poEventCreationRequest);

    }

    @Test
    void publishGrn() {
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        inwardNexs.publishGrn(grnEventCreationRequest);
    }

    @Test
    void processPoBySource() {
        Mockito.when(inwardRepositoryService.findByEventIdAndInstanceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(inwardEventPo);
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        Mockito.when(commonUtils.buildAndGetErpRequestDto(ArgumentMatchers.any(),
                                                          ArgumentMatchers.any(),
                                                          ArgumentMatchers.any(),
                                                          ArgumentMatchers.any())).thenReturn(
                ErpRequestDto.builder().id(1L).requestPayload(new PurchaseOrder () ).event(FinanceServiceEventTypes.PO).build());
        Mockito.when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any())).thenReturn(erpResponseDto);
        ErpResponseDto responseDto =inwardNexs.processPoBySource(poEventCreationRequest);
        Assertions.assertEquals("Successfully synced",responseDto.getResponse());
    }

    @Test
    void processPoBySourceException() {
        Mockito.when(inwardRepositoryService.findByEventIdAndInstanceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(inwardEventPo);
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        Mockito.when(commonUtils.buildAndGetErpRequestDto(ArgumentMatchers.any(),
                                                          ArgumentMatchers.any(),
                                                          ArgumentMatchers.any(),
                                                          ArgumentMatchers.any())).thenReturn(
                ErpRequestDto.builder().id(1L).requestPayload(new PurchaseOrder () ).event(FinanceServiceEventTypes.PO).build());
        Mockito.when(commonUtils.callFinanceAdaptor(ArgumentMatchers.any(),ArgumentMatchers.any(),ArgumentMatchers.any())).thenThrow(new RuntimeException("Exception Occurred"));
        ErpResponseDto responseDto =inwardNexs.processPoBySource(poEventCreationRequest);
        Assertions.assertEquals("Exception Occurred",responseDto.getResponse());
        Assertions.assertEquals(D365ResponseType.FAILED,responseDto.getD365SyncStatus());

    }

    @Test
    void processGrnBySource() {
        Mockito.when(inwardRepositoryService.findByEventIdAndInstanceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(inwardEventGrn);
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        ErpResponseDto responseDto =inwardNexs.processGrnBySource(grnEventCreationRequest);
        Assertions.assertEquals("Incorrect payload received from NEXS",responseDto.getResponse());
    }
    @Test
    void processGrnBySourceException() {
        Mockito.when(inwardRepositoryService.findByEventIdAndInstanceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(inwardEventGrn);
        Mockito.when(inwardVersionFactory.getVersionInstance(ArgumentMatchers.any())).thenReturn(inwardVersionService);
        ErpResponseDto responseDto =inwardNexs.processGrnBySource(grnEventCreationRequest);
        Assertions.assertEquals("Incorrect payload received from NEXS",responseDto.getResponse());
        Assertions.assertEquals(D365ResponseType.FAILED,responseDto.getD365SyncStatus());
    }
}