package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.erpEvents.dto.BaseResponseDto;
import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.factory.InwardEventFactory;
import com.lenskart.financeConsumer.erpEvents.model.InwardEvent;
import com.lenskart.financeConsumer.erpEvents.service.InwardEventSourceService;
import com.lenskart.financeConsumer.erpEvents.service.InwardRepositoryService;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class PoServiceImplTest {
    @Mock
    private InwardRepositoryService inwardRepositoryService;

    @Mock
    private InwardEventFactory inwardEventFactory;
    @Mock
    InwardEventSourceService inwardEventSourceService = new InwardNexsImpl();

    @InjectMocks
    private PoServiceImpl poService;
    private PoEventCreationRequest poEventCreationRequest;
    private GrnEventCreationRequest grnEventCreationRequest;
    private ErpResponseDto erpResponseDto;
    private InwardEvent inwardEvent;

    @BeforeEach
    public void setUp() {
        poEventCreationRequest = PoEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.NEXS)
                .eventName(FinanceServiceEventTypes.PO)
                .version(Version.V1)
                .build();

        grnEventCreationRequest = GrnEventCreationRequest.builder()
                .eventId("ABC-123")
                .source(Source.NEXS)
                .eventName(FinanceServiceEventTypes.GRN)
                .version(Version.V1)
                .build();
        erpResponseDto = ErpResponseDto.builder()
                .entityId("ABC-123")
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .response("Successfully synced")
                .build();

        inwardEvent= InwardEvent.builder()
                .retryCount(0).build();
    }

    @Test
    void createPo() {
        Mockito.when(inwardEventFactory.getSourceInstance(ArgumentMatchers.any())).thenReturn(inwardEventSourceService);
        BaseResponseDto baseResponseDto = poService.createPo(poEventCreationRequest);
        Assertions.assertEquals("PO Successfully created", baseResponseDto.getResponse());
    }

    @Test
    void createGrn() {
        Mockito.when(inwardEventFactory.getSourceInstance(ArgumentMatchers.any())).thenReturn(inwardEventSourceService);
        BaseResponseDto baseResponseDto = poService.createGrn(grnEventCreationRequest);
        Assertions.assertEquals("GRN Successfully created", baseResponseDto.getResponse());
    }

    @Test
    void processPurchaseOrder() {
        Mockito.when(inwardEventFactory.getSourceInstance(ArgumentMatchers.any())).thenReturn(inwardEventSourceService);
        Mockito.when(inwardRepositoryService.findByEventIdAndInstanceType(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(inwardEvent);
        Mockito.when(inwardEventFactory.getSourceInstance(ArgumentMatchers.any())).thenReturn(inwardEventSourceService);
        Mockito.when(inwardEventSourceService.processPoBySource(ArgumentMatchers.any()))
                .thenReturn(erpResponseDto);
        poService.processPurchaseOrder(poEventCreationRequest);

    }

    @Test
    void processGrn() {
        Mockito.when(inwardEventFactory.getSourceInstance(ArgumentMatchers.any())).thenReturn(inwardEventSourceService);
        Mockito.when(inwardRepositoryService.findByEventIdAndInstanceType(ArgumentMatchers.any(),ArgumentMatchers.any() )).thenReturn(inwardEvent);
        Mockito.when(inwardEventSourceService.processGrnBySource(ArgumentMatchers.any()))
                .thenReturn(erpResponseDto);
        poService.processGrn(grnEventCreationRequest);
    }

    @Test
    void syncInwardForFailureEntries() {
        Mockito.when(inwardEventFactory.getSourceInstance(ArgumentMatchers.any())).thenReturn(inwardEventSourceService);
        List<InwardEvent> inwardEventList = Arrays.asList(InwardEvent.builder().event(FinanceServiceEventTypes.PO).build(),
                                                          InwardEvent.builder().event(FinanceServiceEventTypes.GRN).build());
        Mockito.when(inwardRepositoryService.findInwardFailureRecords(ArgumentMatchers.any(),
                                                                      ArgumentMatchers.anyInt(),
                                                                      ArgumentMatchers.any(),ArgumentMatchers.any() )).thenReturn(
                inwardEventList);
        poService.syncInwardForFailureEntries(SchedulerConfig.builder().recordInterval("2").build());
    }
}