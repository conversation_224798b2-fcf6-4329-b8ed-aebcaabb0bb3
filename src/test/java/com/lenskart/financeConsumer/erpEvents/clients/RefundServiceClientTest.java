package com.lenskart.financeConsumer.erpEvents.clients;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;


@ExtendWith(MockitoExtension.class)
class RefundServiceClientTest {

    @Mock
    @Qualifier("restTemplateWithTimeout")
    RestTemplate restTemplate;

    @InjectMocks
    RefundServiceClient refundServiceClient;


    @Test
    void getRefundObject() {
        String url = "https://abc.com/";
        int refundId = 123;
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Integer> entity = new HttpEntity<>(refundId, header);
        ResponseEntity<String> mockResponse = new ResponseEntity<>("Refund details", HttpStatus.OK);
        Mockito.when(restTemplate.exchange((url + refundId), HttpMethod.GET, entity, String.class)).thenReturn(mockResponse);

        ResponseEntity<String> response = refundServiceClient.getRefundObject(123, url, HttpMethod.GET);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Refund details", response.getBody());
    }

    @Test
    void getRefundObjectResourceAccessException() {
        String url = "https://abc.com/";
        int refundId = 123;
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Integer> entity = new HttpEntity<>(refundId, header);
        ResponseEntity<String> mockResponse = new ResponseEntity<>("Refund details", HttpStatus.OK);
        Mockito.when(restTemplate.exchange((url + refundId), HttpMethod.GET, entity, String.class)).thenThrow(ResourceAccessException.class);
        assertThrows(RuntimeException.class,
                     () -> refundServiceClient.getRefundObject(123, url, HttpMethod.GET));
    }

    @Test
    void getRefundObjectHttpClientErrorException() {
        String url = "https://abc.com/";
        int refundId = 123;
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Integer> entity = new HttpEntity<>(refundId, header);
        ResponseEntity<String> mockResponse = new ResponseEntity<>("Refund details", HttpStatus.OK);
        Mockito.when(restTemplate.exchange((url + refundId), HttpMethod.GET, entity, String.class)).thenThrow(HttpClientErrorException.class);
        assertThrows(RuntimeException.class,
                     () -> refundServiceClient.getRefundObject(123, url, HttpMethod.GET));
    }

    @Test
    void getRefundObjectHttpServerErrorException() {
        String url = "https://abc.com/";
        int refundId = 123;
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Integer> entity = new HttpEntity<>(refundId, header);
        ResponseEntity<String> mockResponse = new ResponseEntity<>("Refund details", HttpStatus.OK);
        Mockito.when(restTemplate.exchange((url + refundId), HttpMethod.GET, entity, String.class)).thenThrow(HttpServerErrorException.class);
        assertThrows(RuntimeException.class,
                     () -> refundServiceClient.getRefundObject(123, url, HttpMethod.GET));
    }

    @Test
    void getRefundObjectRuntimeException() {
        String url = "https://abc.com/";
        int refundId = 123;
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Integer> entity = new HttpEntity<>(refundId, header);
        ResponseEntity<String> mockResponse = new ResponseEntity<>("Refund details", HttpStatus.OK);
        Mockito.when(restTemplate.exchange((url + refundId), HttpMethod.GET, entity, String.class)).thenThrow(RuntimeException.class);
        assertThrows(RuntimeException.class,
                     () -> refundServiceClient.getRefundObject(123, url, HttpMethod.GET));
    }
}