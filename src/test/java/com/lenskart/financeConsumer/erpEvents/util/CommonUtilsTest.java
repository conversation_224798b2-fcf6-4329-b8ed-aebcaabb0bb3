package com.lenskart.financeConsumer.erpEvents.util;

import com.lenskart.financeConsumer.TestUtils.TestUtils;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.erpEvents.service.GrnHeaderWriteRepositoryService;
import com.lenskart.financeConsumer.erpEvents.service.PurchaseOrderHeaderWriteRepositoryService;
import com.lenskart.financeConsumer.erpEvents.service.impl.PoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CommonUtilsTest {

    @Mock
    private PurchaseOrderHeaderWriteRepositoryService purchaseOrderHeaderWriteRepositoryService;
    @Mock
    private GrnHeaderWriteRepositoryService grnHeaderWriteRepositoryService;
    @InjectMocks
    private CommonUtils commonUtils;
    @Mock
    private PoServiceImpl poService;
    private KafkaEventDto kafkaEventDtoPo;
    private KafkaEventDto kafkaEventDtoGrn;

    @BeforeEach
    public void setUp() {
        String poPath = "../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/inwardEventPayloads/po.json";
        String grnPath = "../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/inwardEventPayloads/grn.json";
        kafkaEventDtoPo = TestUtils.getDocToPersistenceKafkaDto(poPath);
        kafkaEventDtoGrn = TestUtils.getDocToPersistenceKafkaDto(grnPath);
    }

    @Test
    void processInwardPayloadDataPo() {
    commonUtils.processPayloadData(kafkaEventDtoPo);
    }
    @Test
    void processInwardPayloadDataGrn() {
        commonUtils.processPayloadData(kafkaEventDtoGrn);
    }
}