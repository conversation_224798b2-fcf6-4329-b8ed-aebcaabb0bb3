{"payload": {"legalEntity": "", "CustAccount": "", "CustomerName": "", "FirstName": "", "MiddleName": "", "LastName": "", "Search": "", "Parent_id": 0, "Gender": "", "CustomerGroup": "", "Currency": "", "Type": "", "TermsOfPayment": "", "Site": "", "Units": "", "CostCentre": "", "Store": "", "SaleChannel": "", "PartnerType": "", "ItemClassification": "", "Brand": "", "Employee": "", "CustomerContact": [{"LocationName": "", "Locator": "", "Type": "", "IsPrimaryContact": ""}], "AddressList": [{"Address": "", "Purpose": "", "Street": "", "City": "", "State": "", "ZipCode": "", "County": "", "Country": "", "TAN": "", "GSTIN": "", "isPrimaryAddress": ""}], "CustomerBankDetails": [{"BankAccountId": "", "BankAccountNo": "", "BankName": ""}]}, "status": false, "message": "Unable to find customer payload"}