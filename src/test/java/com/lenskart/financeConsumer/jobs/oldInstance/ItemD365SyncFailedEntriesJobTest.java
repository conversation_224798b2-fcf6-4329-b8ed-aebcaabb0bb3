package com.lenskart.financeConsumer.jobs.oldInstance;

import com.lenskart.financeConsumer.facade.ItemD365SyncFacade;
import com.lenskart.financeConsumer.model.financeDb.ItemD365Sync;
import com.lenskart.financeConsumer.util.Constants;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ItemD365SyncFailedEntriesJobTest {

    @Mock
    private ItemD365SyncFacade itemD365SyncFacade;

    @Mock
    private Environment environment;

    @InjectMocks
    private ItemD365SyncFailedEntriesJob job;

    @Test
    public void testRetryFailedItemSyncEvents_EnabledJob() throws Exception {
        ReflectionTestUtils.setField(job,"isEnabled",true);
        job.retryFailedItemSyncEvents();
        verify(itemD365SyncFacade, times(1)).retryFailureRecords();
    }

    @Test
    public void testRetryFailedItemSyncEvents_DisabledJob() throws Exception {
        ReflectionTestUtils.setField(job,"isEnabled",false);
        job.retryFailedItemSyncEvents();
        verifyNoInteractions(itemD365SyncFacade);
    }

    @Test
    public void testRetryFailedItemSyncEvents_Exception() throws Exception {

        ReflectionTestUtils.setField(job,"isEnabled",true);
        doThrow(new RuntimeException("Simulated exception")).when(itemD365SyncFacade).retryFailureRecords();
        job.retryFailedItemSyncEvents();

        verify(itemD365SyncFacade, times(1)).retryFailureRecords();
    }

    @Test
    void ItemD365SyncEntity(){
        LocalDateTime currentDate =LocalDateTime.now();
        ItemD365Sync itemD365Sync = ItemD365Sync.builder().
                referenceId(0L).
                productId(121).
                createdAt(currentDate).
                createdBy(Constants.Common.financeConsumer).
                updatedAt(currentDate).
                updatedBy(Constants.Common.financeConsumer).
                eventTime(currentDate).
                retryCount(0).
                errorMessage("eee").
                build();
        assertEquals(121,itemD365Sync.getProductId());
        assertEquals(currentDate,itemD365Sync.getEventTime());
        assertEquals(currentDate,itemD365Sync.getCreatedAt());

        assertEquals(currentDate,itemD365Sync.getUpdatedAt());
        assertEquals(Constants.Common.financeConsumer,itemD365Sync.getCreatedBy());
        assertEquals(Constants.Common.financeConsumer,itemD365Sync.getUpdatedBy());
        assertEquals(0,itemD365Sync.getVersion());
        assertEquals("eee",itemD365Sync.getErrorMessage());
        assertEquals(true,itemD365Sync.toString().contains("121"));




    }

}
