package com.lenskart.financeConsumer.strategy;

import com.lenskart.financeConsumer.constant.EventErrorType;
import com.lenskart.financeConsumer.constant.StrategyName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FailureEventHelperTest {

    @InjectMocks
    FailureEventHelper failureEventHelper;


    private Map<String, FailureEventStrategy> failureEventStrategyMap;

    @Mock
    FailureEventStrategy failureEventStrategy;
  //  @Mock

    @BeforeEach
    void setup(){
        Map<String, FailureEventStrategy> map=new HashMap<>();
        map.put(StrategyName.SALE_ORDER_RETRY,failureEventStrategy);
        ReflectionTestUtils.setField(failureEventHelper,"failureEventStrategyMap",map);

    }

    @Test
    void findFailureStrategy() {
    }

    @Test
    void findEventErrorType() {
    }
    @Test
    void testGetTransferOrderTypeStrategyConflict(){
        EventErrorType errorType = EventErrorType.CONFLICT;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferOrderTypeStrategy",errorType);
        assertEquals(failureEventStrategy,strategy);
    }
    @Test
    void testGetTransferOrderTypeStrategyIOError(){
        EventErrorType errorType = EventErrorType.IO_ERROR;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferOrderTypeStrategy",errorType);
        assertEquals(failureEventStrategy,strategy);
    }
    @Test
    void testGetTransferOrderTypeStrategyInvalidStrategy(){
        EventErrorType errorType = EventErrorType.NONE;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferOrderTypeStrategy",errorType);
        assertEquals(null,strategy);
    }
    @Test
    void testGetTransferOrderTypeStrategyAppUnavilableStrategy(){
        EventErrorType errorType = EventErrorType.APPLICATION_UNAVAILBLE;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferOrderTypeStrategy",errorType);
        assertEquals(failureEventStrategy,strategy);
    }


    @Test
    void testGetTransferJournalTypeStrategyConflict(){
        EventErrorType errorType = EventErrorType.CONFLICT;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferJournalTypeStrategy",errorType);
        assertEquals(failureEventStrategy,strategy);
    }
    @Test
    void testGetTransferJournalTypeStrategyIOError(){
        EventErrorType errorType = EventErrorType.IO_ERROR;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferJournalTypeStrategy",errorType);
        assertEquals(failureEventStrategy,strategy);
    }
    @Test
    void testGetTransferJournalTypeStrategyInvalidStrategy(){
        EventErrorType errorType = EventErrorType.NONE;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferJournalTypeStrategy",errorType);
        assertEquals(null,strategy);
    }
    @Test
    void testGetTransferJournalTypeStrategyAppUnavilableStrategy(){
        EventErrorType errorType = EventErrorType.APPLICATION_UNAVAILBLE;
        FailureEventStrategy strategy = ReflectionTestUtils.invokeMethod(failureEventHelper,"getTransferJournalTypeStrategy",errorType);
        assertEquals(failureEventStrategy,strategy);
    }
}