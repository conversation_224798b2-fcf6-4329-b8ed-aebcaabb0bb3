package com.lenskart.financeConsumer.strategy;

import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dto.SerialNumberErrorDto;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemSyncDto;
import com.lenskart.financeConsumer.financeDb.inventory.read.OrdersHeaderReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.PackingSlipUtil;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SerialNumberErrorStrategyTest {
    @Mock
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @InjectMocks
    private SerialNumberErrorStrategy serialNumberErrorStrategy;
    @Mock
    private UwOrdersReadRepositoryFinance uwOrdersReadRepositoryFinance;

    @Mock
    private OrdersHeaderReadRepository ordersHeaderReadRepository;

    @Mock
    PackingSlipUtil packingSlipUtil;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;
    @Mock
    private DateUtils dateUtils;
    @Mock
    private GenericClientService genericClientService;

    FinanceSourceSystemSyncDto financeSourceSystemSyncDto = new FinanceSourceSystemSyncDto();
    FinanceSourceSystemSync financeSourceSystemSync = new FinanceSourceSystemSync();
    List<UwOrder> uwOrders = new ArrayList<>();



    @BeforeEach
    public void setup(){
        financeSourceSystemSyncDto.setErrorMessage("Serial number 8967vgvi has not been created for item number 85446546...");
        financeSourceSystemSyncDto.setPayload("[602462590,602462592,602462594]");
        financeSourceSystemSyncDto.setEntityId("LKH03SP146837");
        financeSourceSystemSyncDto.setFacilityCode("QNXS2");
        financeSourceSystemSyncDto.setId(1l);
        financeSourceSystemSyncDto.setCreatedBy("finance-consumer");
        financeSourceSystemSyncDto.setUpdatedBy("finance-consumer");

        financeSourceSystemSync.setErrorMessage(financeSourceSystemSyncDto.getErrorMessage());
        financeSourceSystemSync.setId(financeSourceSystemSyncDto.getId());
        financeSourceSystemSync.setEntityId(financeSourceSystemSyncDto.getEntityId());
        financeSourceSystemSync.setFacilityCode(financeSourceSystemSyncDto.getFacilityCode());
        financeSourceSystemSync.setRetryCount(0);

        UwOrder uwOrder = new UwOrder();
        uwOrder.setIncrementId(456789);
        uwOrders.add(uwOrder);

        ReflectionTestUtils.setField(serialNumberErrorStrategy,"serialNumberKafkaTopic","serialNumberKafkaTopic");
    }

    @Test
    @SneakyThrows
    void testSerialNumberErrorStrategySuccess() {


        try(MockedStatic dateUtilsMockedStatic = mockStatic(DateUtils.class)) {


            when(DateUtils.getLocalDateObjectFromDate(any())).thenReturn(LocalDateTime.now());

            when(financeSystemSyncRepository.findById(any())).thenReturn(Optional.of(financeSourceSystemSync));

            List<UwOrder> uwOrders = new ArrayList<>();
            UwOrder uwOrder = new UwOrder();
            uwOrder.setIncrementId(456789);
            uwOrders.add(uwOrder);
            when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(any(), any())).thenReturn(uwOrders);

            OrdersHeader ordersHeader = new OrdersHeader();
            when(ordersHeaderReadRepository.findByIncrementId(any())).thenReturn(ordersHeader);

            Mockito.when(packingSlipUtil.getLegalEntity(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn("LKIN");

            SerialNumberErrorDto serialNumberErrorDto = new SerialNumberErrorDto();
            serialNumberErrorDto.setId(financeSourceSystemSyncDto.getId());
            serialNumberErrorDto.setErrorString(financeSourceSystemSyncDto.getErrorMessage());
            serialNumberErrorDto.setEntityId(financeSourceSystemSyncDto.getEntityId());
            serialNumberErrorDto.setFacilityCode(financeSourceSystemSyncDto.getFacilityCode());
            serialNumberErrorDto.setLegalEntity("LKIN");
            serialNumberErrorDto.setSource("SCM");
            serialNumberErrorDto.setPayload(financeSourceSystemSyncDto.getPayload());

            when(financeSystemSyncRepository.save(any())).thenReturn(financeSourceSystemSync);

            FinanceSourceSystemSyncDto result = serialNumberErrorStrategy.execute(financeSourceSystemSyncDto);
            assertNotNull(result);
            assertEquals("LKH03SP146837", result.getEntityId());
        }

    }
    @Test
    @SneakyThrows
    void testSerialNumberErrorStrategyUwOrderMissing() {

        try(MockedStatic dateUtilsMockedStatic = mockStatic(DateUtils.class)) {
            when(DateUtils.getLocalDateObjectFromDate(any())).thenReturn(LocalDateTime.now());

            when(financeSystemSyncRepository.findById(any())).thenReturn(Optional.of(financeSourceSystemSync));

            when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(any(), any())).thenReturn(null);

            when(financeSystemSyncRepository.save(any())).thenReturn(financeSourceSystemSync);

            FinanceSourceSystemSyncDto result = serialNumberErrorStrategy.execute(financeSourceSystemSyncDto);
            assertNotNull(result);
            assertEquals("Failure", result.getD365SyncStatus());
        }

    }
    @Test
    @SneakyThrows
    void testSerialNumberErrorStrategyOrderHeaderMissing() {


        try(MockedStatic dateUtilsMockedStatic = mockStatic(DateUtils.class)) {

            when(DateUtils.getLocalDateObjectFromDate(any())).thenReturn(LocalDateTime.now());

            when(financeSystemSyncRepository.findById(any())).thenReturn(Optional.of(financeSourceSystemSync));

            when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(any(), any())).thenReturn(uwOrders);

            when(ordersHeaderReadRepository.findByIncrementId(any())).thenReturn(null);

            when(financeSystemSyncRepository.save(any())).thenReturn(financeSourceSystemSync);

            FinanceSourceSystemSyncDto result = serialNumberErrorStrategy.execute(financeSourceSystemSyncDto);
            assertNotNull(result);
            assertEquals("Failure", result.getD365SyncStatus());
        }

    }
    @Test
    @SneakyThrows
    void testSerialNumberErrorStrategyLegalEntityMissing() {

        try(MockedStatic dateUtilsMockedStatic = mockStatic(DateUtils.class)) {
            when(DateUtils.getLocalDateObjectFromDate(any())).thenReturn(LocalDateTime.now());

            when(financeSystemSyncRepository.findById(any())).thenReturn(Optional.of(financeSourceSystemSync));
            when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(any(), any())).thenReturn(uwOrders);

            OrdersHeader ordersHeader = new OrdersHeader();
            when(ordersHeaderReadRepository.findByIncrementId(any())).thenReturn(ordersHeader);

            Mockito.when(packingSlipUtil.getLegalEntity(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(null);

            when(financeSystemSyncRepository.save(any())).thenReturn(financeSourceSystemSync);

            FinanceSourceSystemSyncDto result = serialNumberErrorStrategy.execute(financeSourceSystemSyncDto);
            assertNotNull(result);
            assertEquals("Failure", result.getD365SyncStatus());
        }

    }

    @Test
    @SneakyThrows
    void testSerialNumberErrorStrategyExceptionHandling() {
        try (MockedStatic<DateUtils> dateUtilsMockedStatic = mockStatic(DateUtils.class)) {
            when(DateUtils.getLocalDateObjectFromDate(any())).thenReturn(LocalDateTime.now());

            when(financeSystemSyncRepository.findById(any())).thenReturn(Optional.of(financeSourceSystemSync));
            when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(any(),any())).thenThrow(new RuntimeException("Entity not found"));
            when(genericClientService.getErrorMessage(any())).thenReturn("Entity not found");
            FinanceSourceSystemSyncDto result = serialNumberErrorStrategy.execute(financeSourceSystemSyncDto);

            assertNotNull(result);
            assertEquals("Entity not found", result.getErrorMessage());
        }
    }

}
