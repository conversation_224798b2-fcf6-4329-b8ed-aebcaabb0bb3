package com.lenskart.financeConsumer.clients;

import com.lenskart.financeConsumer.dto.d365requests.juno.giftCard.JunoGiftCardResponse;
import com.lenskart.financeConsumer.dto.d365requests.juno.sbrt.JunoSBRTResponse;
import com.lenskart.financeConsumer.service.GenericClientService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ServerErrorException;

import java.rmi.ServerException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class JunoClientTest {

    @InjectMocks
    JunoClient junoClient;

    @Mock
    RestTemplate restTemplate;


    @Mock
    ResponseEntity<JunoGiftCardResponse> responseResponseEntity;
    @Mock
    ResponseEntity<JunoSBRTResponse> sbrtResponseResponseEntity;

    @Mock
    GenericClientService genericClientService;

    @BeforeEach
    void setUp() {
    }

    @Test
    void getGiftCardDetails() {

        String itemIds ="1,2,3";
        ReflectionTestUtils.setField(junoClient,"junoBaseUrl","abc.com");
        ReflectionTestUtils.setField(junoClient,"restTemplate",restTemplate);
        JunoGiftCardResponse junoGiftCardResponse =new JunoGiftCardResponse();
        junoGiftCardResponse.setMessage("posted successfully!");
        //ReflectionTestUtils.setField(junoClient,"JUNO_GIFT_CARD_BREAKUP_URL","/v1/order");
        Mockito.when(responseResponseEntity.getStatusCode()).thenReturn(HttpStatus.OK);
        Mockito.when(restTemplate.exchange(any(),any(),any(),Mockito.<Class<JunoGiftCardResponse>>any())).thenReturn(responseResponseEntity);
        Mockito.when(responseResponseEntity.getBody()).thenReturn(junoGiftCardResponse);
        JunoGiftCardResponse junoGiftCardResponseOutput = junoClient.getGiftCardDetails(1,itemIds);
        assertEquals("posted successfully!" ,junoGiftCardResponseOutput.getMessage());

    }
    @Test
    void getGiftCardDetailsNullPointerExceptionResponse() {

        String itemIds ="1,2,3";
        ReflectionTestUtils.setField(junoClient,"junoBaseUrl","abc.com");
        JunoGiftCardResponse junoGiftCardResponse =new JunoGiftCardResponse();
        junoGiftCardResponse.setMessage("posted successfully!");
        Mockito.when(genericClientService.getErrorMessage(any())).thenReturn("NullPointer");

        JunoGiftCardResponse junoGiftCardResponseOutput = junoClient.getGiftCardDetails(1,itemIds);
        assertTrue(junoGiftCardResponseOutput.getMessage().contains("NullPointer"));


    }

    @Test
    void junoForwardRequest() {
    }

    @Test
    void isSBRTAtOrderLevel() {
        ReflectionTestUtils.setField(junoClient,"junoBaseUrl","juno.com");
        ReflectionTestUtils.setField(junoClient,"posAuthToken","token");

        JunoSBRTResponse junoSBRTResponse = JunoSBRTResponse.builder().result(true).build();
        ReflectionTestUtils.setField(junoClient,"restTemplate",restTemplate);


        Mockito.when(restTemplate.exchange(any(),any(),any(),Mockito.<Class<JunoSBRTResponse>>any())).thenReturn(sbrtResponseResponseEntity);
        Mockito.when(sbrtResponseResponseEntity.getStatusCode()).thenReturn(HttpStatus.OK);
        Mockito.when(sbrtResponseResponseEntity.getBody()).thenReturn(junoSBRTResponse);


       assertTrue( junoClient.isSBRTAtOrderLevel(122));
    }
    @Test
    void isSBRTAtOrderLevelException() {
        ReflectionTestUtils.setField(junoClient,"junoBaseUrl","juno.com");
        ReflectionTestUtils.setField(junoClient,"posAuthToken","token");

        JunoSBRTResponse junoSBRTResponse = JunoSBRTResponse.builder().result(true).build();
        ReflectionTestUtils.setField(junoClient,"restTemplate",restTemplate);



        assertThrows(ServerException.class, () -> {
            junoClient.isSBRTAtOrderLevel(1222);
        });

    }

}