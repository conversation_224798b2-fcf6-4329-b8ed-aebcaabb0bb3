package com.lenskart.financeConsumer.clients;

import com.lenskart.financeConsumer.model.financeDb.Invoice;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.lenskart.financeConsumer.v2.dto.MetaDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

@ExtendWith(MockitoExtension.class)
class NexsClientTest {

    @InjectMocks
    private NexsClient nexsClient;
    @Mock
    private RestTemplate restTemplate;
    @BeforeEach
    public void setup() {
        ReflectionTestUtils.setField(nexsClient, "nexsPoApiBaseUrl", "https://nexs.mock.com");
    }
    @Test
    void getNexsInvoiceDetailsForThreeWayMatch() {
        URI uri = UriComponentsBuilder.fromUriString("https://nexs.mock.com/internal/nexs/api/invoice/v1/get3WayMatchDetails")
                .queryParam("vendor_invoice_num", "ABC-123")
                .build().toUri();        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(httpHeaders);
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().meta(MetaDto.builder().message("Success").build()).build();
        ResponseEntity<GenericResponseDto> responseEntity = new ResponseEntity<>(
                genericResponseDTO,
                httpHeaders,
                HttpStatus.OK
        );
        Mockito.when(restTemplate.exchange(uri, HttpMethod.GET, entity,
                                           GenericResponseDto.class)).thenReturn(responseEntity);

        nexsClient.getNexsInvoiceDetailsForThreeWayMatch("ABC-123");
        Mockito.verify(restTemplate, Mockito.times(1)).exchange(uri, HttpMethod.GET, entity, GenericResponseDto.class);
    }
    @Test
    void getNexsInvoiceDetailsForThreeWayMatchFailed() {
        URI uri = UriComponentsBuilder.fromUriString("https://nexs.mock.com/internal/nexs/api/invoice/v1/get3WayMatchDetails")
                .queryParam("vendor_invoice_num", "ABC-123")
                .build().toUri();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(httpHeaders);
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().meta(MetaDto.builder().message("Failed").build()).build();
        ResponseEntity<GenericResponseDto> responseEntity = new ResponseEntity<>(
                genericResponseDTO,
                httpHeaders,
                HttpStatus.OK
        );
        Mockito.when(restTemplate.exchange(uri, HttpMethod.GET, entity,
                                           GenericResponseDto.class)).thenReturn(responseEntity);

        nexsClient.getNexsInvoiceDetailsForThreeWayMatch("ABC-123");
        Mockito.verify(restTemplate, Mockito.times(1)).exchange(uri, HttpMethod.GET, entity, GenericResponseDto.class);
    }

    @Test
    void getNexsInvoiceDetailsForThreeWayMatchException() {
        URI uri = UriComponentsBuilder.fromUriString("https://nexs.mock.com/internal/nexs/api/invoice/v1/get3WayMatchDetails")
                .queryParam("vendor_invoice_num", "ABC-123")
                .build().toUri();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(httpHeaders);
        Mockito.when(restTemplate.exchange(uri, HttpMethod.GET, entity,
                                           GenericResponseDto.class)).thenThrow(new HttpClientErrorException(HttpStatus.INTERNAL_SERVER_ERROR));

        nexsClient.getNexsInvoiceDetailsForThreeWayMatch("ABC-123");
        Mockito.verify(restTemplate, Mockito.times(1)).exchange(uri, HttpMethod.GET, entity, GenericResponseDto.class);
    }

    @Test
    void testGetNexsInvoiceDetails() {
        Invoice invoice = new Invoice();
        invoice.setPoNumber("PO_01");
        invoice.setVendorInvoiceNumber("ABC-123");

        URI uri = UriComponentsBuilder.fromUriString( "https://nexs.mock.com/internal/nexs/api/invoice/v1/getInvoiceDetails")
                .queryParam("vendor_invoice_num", invoice.getVendorInvoiceNumber())
                .queryParam("po_num", invoice.getPoNumber())
                .queryParam("status", "CLOSED").build().toUri();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(httpHeaders);
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().meta(MetaDto.builder().message("Success").build()).build();
        ResponseEntity<GenericResponseDto> responseEntity = new ResponseEntity<>(
                genericResponseDTO,
                httpHeaders,
                HttpStatus.OK
        );
        Mockito.when(restTemplate.exchange(uri, HttpMethod.GET, entity,
                                           GenericResponseDto.class)).thenReturn(responseEntity);
        nexsClient.getNexsInvoiceDetails(invoice);
    }

    @Test
    void getNexsPoGrnDetails() {
        URI uri = UriComponentsBuilder.fromUriString("https://nexs.mock.com/internal/nexs/api/invoice/v1/getInvoiceDetails")
                .queryParam("vendor_invoice_num", "ABC-123")
                .build().toUri();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(httpHeaders);
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().meta(MetaDto.builder().message("Success").build()).build();
        ResponseEntity<GenericResponseDto> responseEntity = new ResponseEntity<>(
                genericResponseDTO,
                httpHeaders,
                HttpStatus.OK
        );
        Mockito.when(restTemplate.exchange(uri, HttpMethod.GET, entity,
                                           GenericResponseDto.class)).thenReturn(responseEntity);
        nexsClient.getNexsPoGrnDetails("ABC-123");
    }
}