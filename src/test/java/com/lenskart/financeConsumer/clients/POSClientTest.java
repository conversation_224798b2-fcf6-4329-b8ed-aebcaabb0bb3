package com.lenskart.financeConsumer.clients;

import com.lenskart.financeConsumer.util.PosUtils;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;


import com.lenskart.financeConsumer.util.Constants;
import org.apache.commons.lang.BooleanUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.rmi.ServerException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class POSClientTest {

    @Mock
    private PosUtils posUtils;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private RedisTemplate redisTemplate;

    @InjectMocks
    private POSClient posClient;

    @Mock
    ValueOperations ops;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(posClient,"posBaseUrl","http://abc.com");
        ReflectionTestUtils.setField(posClient,"isPOSTokenRequired",true);
    }

    @Test
    void testFetchLegalEntityForFacilityCode_Success() throws Exception {
        String facilityCode = "9460";
        String url = "https://example.com/franchises/sbrt/status";

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(Constants.POS.User_Agent, "Application");
        headers.set(Constants.POS.X_Lenskart_API_Key, "valyoo123");
        headers.set(Constants.POS.X_Lenskart_App_Id, "connect");
        headers.set(Constants.POS.X_Lenskart_Session_Token, "dummyToken");

        Map<String, String> params = new HashMap<>();
        params.put("franchiseId", "9460");

        HttpEntity<Map<String, String>> entity = new HttpEntity<>(params, headers);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("isSbrtStore", true);

        ResponseEntity responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), Mockito.<Class<String>>any())).thenReturn(responseEntity);
        when(redisTemplate.opsForValue()).thenReturn(ops);
        when(ops.get(any())).thenReturn("token");
        String result = posClient.fetchLegalEntityForFacilityCode(facilityCode);
        assertEquals("LK", result);
    }

    @Test
    void testFetchLegalEntityForFacilityCode_NoSBRT() throws Exception {
        String facilityCode = "9460";
        String url = "https://example.com/franchises/sbrt/status";

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(Constants.POS.User_Agent, "Application");
        headers.set(Constants.POS.X_Lenskart_API_Key, "valyoo123");
        headers.set(Constants.POS.X_Lenskart_App_Id, "connect");
        headers.set(Constants.POS.X_Lenskart_Session_Token, "dummyToken");

        Map<String, String> params = new HashMap<>();
        params.put("franchiseId", "9460");

        HttpEntity<Map<String, String>> entity = new HttpEntity<>(params, headers);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("isSbrtStore", false);
        responseBody.put("sessionToken", "sss");


        ResponseEntity responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), Mockito.<Class<String>>any())).thenReturn(responseEntity);
       when(redisTemplate.opsForValue()).thenReturn(ops);
       when(ops.get(any())).thenReturn(null);

        String result = posClient.fetchLegalEntityForFacilityCode(facilityCode);
        assertEquals("DK", result);
    }

    @Test
    void testFetchLegalEntityForFacilityCode_Exception() throws Exception {
        String facilityCode = "9460";
        String url = "https://example.com/franchises/sbrt/status";

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(Constants.POS.User_Agent, "Application");
        headers.set(Constants.POS.X_Lenskart_API_Key, "valyoo123");
        headers.set(Constants.POS.X_Lenskart_App_Id, "connect");
        headers.set(Constants.POS.X_Lenskart_Session_Token, "dummyToken");

        Map<String, String> params = new HashMap<>();
        params.put("franchiseId", "9460");
        params.put("sessionToken","ss");


        HttpEntity<Map<String, String>> entity = new HttpEntity<>(params, headers);

        ResponseEntity responseEntity = new ResponseEntity<>(HttpStatus.OK);
        ResponseEntity responseEntity1 = new ResponseEntity<>(params,HttpStatus.OK);

        when(restTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), Mockito.<Class<String>>any())).thenReturn(responseEntity1).thenReturn(responseEntity);
        when(redisTemplate.opsForValue()).thenReturn(ops);
        when(ops.get(any())).thenReturn(null);
        ServerException exception = assertThrows(ServerException.class, () -> posClient.fetchLegalEntityForFacilityCode(facilityCode));
        assertTrue( exception.getMessage().contains("POS client API Response body is null "));
    }





}
