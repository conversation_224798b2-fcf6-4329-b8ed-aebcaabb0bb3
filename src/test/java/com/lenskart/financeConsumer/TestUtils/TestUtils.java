package com.lenskart.financeConsumer.TestUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.dto.d365requests.StatusAPIResponse;
import com.lenskart.financeConsumer.v2.dto.ForwardFinanceSyncRequestDto;
import com.lenskart.financeConsumer.v2.model.Shipment;
import com.lenskart.financeConsumer.v2.model.enums.EntityType;
import com.lenskart.financeConsumer.v2.model.enums.Source;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class TestUtils {

    public static final ObjectMapper objectMapper = new ObjectMapper();
    public static final String configDirectoryB2B="../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/saleOrder/b2b/";
    public static final String configDirectoryDtc="../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/saleOrder/dtc/";
    public static final String configDirectoryFofoB2b="../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/saleOrder/fofob2b/";
    public static final String configDirectoryShipment="../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/shipment/";
    public static final String configDirectory="../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/";
    public static final String configDirectoryCustomer="../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/customer/";

    static {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static ResponseEntity getStatusResponse() throws IOException {
        File file = new File("src/test/java/com/lenskart/financeConsumer/config/StatusResponseJson.json");
        StatusAPIResponse statusAPIResponse = new ObjectMapper().readValue(file, StatusAPIResponse.class);
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        return new ResponseEntity<>(
                statusAPIResponse,
                header,
                HttpStatus.OK
        );
    }

    public static HashMap getSaleOrderPayload() throws IOException {
        File file = new File("src/test/java/com/lenskart/financeConsumer/config/SaleOrderRequest.json");
        return new ObjectMapper().readValue(file, HashMap.class);
    }

    public static ForwardFinanceSyncRequestDto getForwardFinanceSyncRequestDtoTestDto(){
        return ForwardFinanceSyncRequestDto.builder()
                .eventTime(new Date())
                .facilityCode("LK0295")
                .orderId(123L)
                .navChannel("WebB2B")
                .shippingPackageId("LKH03SP125863")
                .uwItemIds(Arrays.asList(394108842, 394108846))
                .source(Source.SCM)
                .entityType(EntityType.SALE_ORDER)
                .build();
    }
    public static Shipment getShipmentTestObject() throws IOException {
        File shipment = new File("../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/saleOrder/b2b/Shipment.json");
        return objectMapper.readValue(shipment, Shipment.class);
    }
    public static Shipment getShipment(Boolean isB2b) {
        File file;
        try {
            if (isB2b) {
                file = new File(configDirectoryB2B + "Shipment.json");
            } else {
                file = new File(configDirectoryDtc + "Shipment.json");
            }
            return objectMapper.readValue(file,Shipment.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T getObjectFromFile(Boolean isB2b, String fileName,Class<T> clazz) {
        File file;
        try {
            if (isB2b) {
                file = new File(configDirectoryB2B + fileName);
            } else {
                file = new File(configDirectoryDtc + fileName);
            }
            return objectMapper.readValue(file,clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static <T> T getObjectFromFile(String fileName,Class<T> clazz) {
        File file;
        try {
                file = new File(configDirectoryFofoB2b + fileName);

            return objectMapper.readValue(file,clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static List<UwOrder> getTestListUwOrder(Boolean isB2b, String fileName) {
        File file;
        try {
            if (isB2b) {
                file = new File(configDirectoryB2B + fileName);
            } else {
                file = new File(configDirectoryDtc + fileName);
            }
            return objectMapper.readValue(file,new TypeReference<List<UwOrder>>(){});
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static List<UwOrder> getTestListUwOrder(String fileName) {
        File file;
        try {
                file = new File(configDirectoryFofoB2b + fileName);
            return objectMapper.readValue(file,new TypeReference<List<UwOrder>>(){});
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static List<Order> getTestListOrder(Boolean isB2b, String fileName) {
        File file;
        try {
            if (isB2b) {
                file = new File(configDirectoryB2B + fileName);
            } else {
                file = new File(configDirectoryDtc + fileName);
            }
            return objectMapper.readValue(file,new TypeReference<List<Order>>(){});
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static List<Order> getTestListOrder(String fileName) {
        File file;
        try {
            file = new File(configDirectoryFofoB2b + fileName);
            return objectMapper.readValue(file, new TypeReference<List<Order>>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static <T> T getObjectFromFileV2(String fileName, Class<T> clazz) {
        File file;
        try {
            file = new File(configDirectoryShipment + fileName);
            return objectMapper.readValue(file, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static <T> T getObject(String fileName, Class<T> clazz) {
        File file;
        try {
            file = new File(configDirectory + fileName);
            return objectMapper.readValue(file, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static KafkaEventDto getDocToPersistenceKafkaDto(String fileName) {
        try {
            File file = new File(fileName);
            return objectMapper.readValue(file, KafkaEventDto.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public static <T> T getObjectFromCustomerFile(String fileName, Class<T> clazz) {
        File file;
        try {
            file = new File(configDirectoryCustomer + fileName);
            return objectMapper.readValue(file, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
