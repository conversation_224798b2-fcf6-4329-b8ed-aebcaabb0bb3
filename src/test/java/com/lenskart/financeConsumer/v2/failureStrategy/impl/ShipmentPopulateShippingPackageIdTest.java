package com.lenskart.financeConsumer.v2.failureStrategy.impl;

import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.TestUtils.TestUtils;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import com.lenskart.financeConsumer.v2.dto.ShipmentConsumerDto;
import com.lenskart.financeConsumer.v2.failureStrategy.ShipmentFailureStrategyService;
import com.lenskart.financeConsumer.v2.failureStrategy.ShipmentStrategy;
import com.lenskart.financeConsumer.v2.model.Shipment;
import com.lenskart.financeConsumer.v2.repository.ShipmentRepository;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import static com.lenskart.financeConsumer.constant.EventErrorType.INVOICE_NOT_PRESENT;

@ExtendWith(MockitoExtension.class)
class ShipmentPopulateShippingPackageIdTest {

    @Mock
    private UwOrdersRepository uwOrdersRepository;
    @Mock
    private SaleOrderUtil saleOrderUtil;
    @Mock
    private Map<String, ShipmentStrategy> shipmentStrategyMap;
    @Mock
    private ShipmentFailureStrategyService shipmentFailureStrategyService;
    @Mock
    ShipmentRepository shipmentRepository;
    @InjectMocks
    private ShipmentPopulateShippingPackageId shipmentPopulateShippingPackageId;
    private Shipment shipment;
    private UwOrder uwOrder;
    private OrdersHeader ordersHeader;
    @BeforeEach
    @SneakyThrows
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        shipment = TestUtils.getObjectFromFileV2("shipment.json", Shipment.class);
        uwOrder=TestUtils.getObjectFromFileV2("UwOrder.json", UwOrder.class);
        ordersHeader = TestUtils.getObjectFromFileV2("OrdersHeader.json", OrdersHeader.class);
    }
    @Test
    @SneakyThrows
    void execute() {
        Mockito.when(shipmentRepository.findById(32L)).thenReturn(Optional.of(shipment));
        Mockito.when(uwOrdersRepository.findByUwItemIdIn(ArgumentMatchers.anyList())).thenReturn(Collections.singletonList(uwOrder));
        Mockito.when(saleOrderUtil.generateMissingShipmentId(ArgumentMatchers.any(),ArgumentMatchers.anyString(),ArgumentMatchers.anyList())).thenReturn(true);
        ShipmentConsumerDto shipmentConsumerDto = ShipmentConsumerDto.builder()
                .id(32L)
                .eventErrorType(INVOICE_NOT_PRESENT)
                .build();
        shipmentPopulateShippingPackageId.execute(shipmentConsumerDto);
        Mockito.verify(shipmentRepository,Mockito.atLeast(1)).findById(ArgumentMatchers.any());
    }
    @Test
    @SneakyThrows
    void executeMissingShipmentFalse() {
        Mockito.when(shipmentRepository.findById(32L)).thenReturn(Optional.of(shipment));
        Mockito.when(uwOrdersRepository.findByUwItemIdIn(ArgumentMatchers.anyList())).thenReturn(Collections.singletonList(uwOrder));
        Mockito.when(saleOrderUtil.generateMissingShipmentId(ArgumentMatchers.any(),ArgumentMatchers.anyString(),ArgumentMatchers.anyList())).thenReturn(false);
        ShipmentConsumerDto shipmentConsumerDto = ShipmentConsumerDto.builder()
                .id(32L)
                .eventErrorType(INVOICE_NOT_PRESENT)
                .build();
        shipmentPopulateShippingPackageId.execute(shipmentConsumerDto);
        Mockito.verify(shipmentRepository,Mockito.atLeast(1)).findById(ArgumentMatchers.any());
    }
    @Test
    @SneakyThrows
    void executeMissingShipmentException() {
        Mockito.when(shipmentRepository.findById(32L)).thenReturn(Optional.empty());
        ShipmentConsumerDto shipmentConsumerDto = ShipmentConsumerDto.builder()
                .id(32L)
                .eventErrorType(INVOICE_NOT_PRESENT)
                .build();
        shipmentPopulateShippingPackageId.execute(shipmentConsumerDto);
        Mockito.verify(shipmentRepository,Mockito.atLeast(1)).findById(ArgumentMatchers.any());
    }
}