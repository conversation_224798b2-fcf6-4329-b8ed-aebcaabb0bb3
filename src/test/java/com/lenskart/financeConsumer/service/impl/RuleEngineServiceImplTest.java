package com.lenskart.financeConsumer.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.RuleJsonUtils;
import com.lenskart.financeConsumer.dto.EventRulesPayloadDto;
import com.lenskart.financeConsumer.exceptions.RuleUpdationException;
import com.lenskart.financeConsumer.exceptions.UnableToFetchRuleException;
import com.lenskart.financeConsumer.financeDb.writeRepository.RuleEngineRepository;
import com.lenskart.financeConsumer.model.financeDb.EventRules;


import com.lenskart.financeConsumer.util.JsonUtil;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import org.junit.Before;
import org.junit.Test;

import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;

import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.Mockito.when;

import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.support.MethodArgumentNotValidException;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@RunWith(MockitoJUnitRunner.class)
public class RuleEngineServiceImplTest {

    @InjectMocks
    RuleEngineServiceImpl ruleEngineService;
    @Mock
    RuleEngineRepository ruleEngineRepository;

    @Mock
    RedisTemplate redisTemplate;

    @Mock
    RuleJsonUtils ruleJsonUtils;

    @Mock
    JsonUtil jsonUtil;

    List<EventRules> expectedAllEventRulesList = new ArrayList<>();
    ObjectMapper objectMapper = new ObjectMapper();
    EventRules expectedSOEventRules, expectedPSEventRules;
    JsonNode validPayload, invalidPayload;
    EventRulesPayloadDto eventRulesPayloadDto;
    String eventType;

    @Before
    public void init() throws IOException {
        MockitoAnnotations.initMocks(this);
        File SOfile = new File("../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/SaleOrderRule.json");
        File PSfile = new File("../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/PackingSlipRule.json");
        File ValidPayloadFile = new File("../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/ValidPayload.json");
        File InvalidPayloadFile = new File("../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/InvalidPayload.json");
        File EventsPayloadRequestDtoFile = new File("../finance-consumer/src/test/java/com/lenskart/financeConsumer/config/EventsRulePayloadDto.json");
        expectedSOEventRules = objectMapper.readValue(SOfile, EventRules.class);
        expectedPSEventRules = objectMapper.readValue(PSfile, EventRules.class);
        validPayload = objectMapper.readTree(ValidPayloadFile);
        invalidPayload = objectMapper.readTree(InvalidPayloadFile);
        eventType = FinanceSourceSystemSyncEvent.SALE_ORDER.toString();
        eventRulesPayloadDto = objectMapper.readValue(EventsPayloadRequestDtoFile, EventRulesPayloadDto.class);
        Collections.addAll(expectedAllEventRulesList, expectedSOEventRules, expectedPSEventRules);
    }

    @Test
    public void getAllRules() {
        when(ruleEngineRepository.findAll()).thenReturn(expectedAllEventRulesList);
        List<EventRules> actualAllEventRules = ruleEngineService.getAllRules();
        Assertions.assertEquals(actualAllEventRules.size(), expectedAllEventRulesList.size());
    }

    @Test
    public void getRuleByEventFromDB() {
        when(ruleEngineRepository.findRulesByEvent(eventType)).thenReturn(expectedSOEventRules);
        EventRules actualSOEventRule = ruleEngineService.getRuleByEventFromDB(eventType);
        Assertions.assertEquals(actualSOEventRule, expectedSOEventRules);
    }

    @Test
    public void getAllEvents() {
        when(ruleEngineRepository.findAllEvents()).thenReturn(Arrays.asList("SALE_ORDER", "PACKING_SLIP", "ITEM_MASTER"));
        List<String> actualEventsList = ruleEngineService.getAllEvents();
        Assertions.assertEquals(actualEventsList.size(), 3);
    }

    @Test(expected = UnableToFetchRuleException.class)
    public void getRuleByEventEmptyEventType() {
        EventRules eventRules = ruleEngineService.getRulesByEvent("");
        when(ruleEngineService.getRulesByEvent(eventType)).thenThrow(new RuntimeException("Test exception"));
        ruleEngineService.createOrUpdateRulesByEvent(eventRulesPayloadDto);
        EventRules eventRulesResponse = ruleEngineService.getRulesByEvent(eventType);
    }

    @Test(expected = UnableToFetchRuleException.class)
    public void getAllEventsFailed() {
        when(ruleEngineRepository.findAllEvents()).thenThrow(new RuntimeException("Test Exception"));
        List<String> events = ruleEngineService.getAllEvents();
    }

    @Test(expected = UnableToFetchRuleException.class)
    public void getRulesFromDBFailed() {
        EventRules eventRulesResponse = ruleEngineService.getRuleByEventFromDB("");
    }

    @Test(expected = RuleUpdationException.class)
    public void createOrUpdateRulesFailed() throws JsonProcessingException {
        when(ruleEngineService.getRuleByEventFromDB(eventType)).thenThrow(new RuntimeException("Test exception"));
        ruleEngineService.createOrUpdateRulesByEvent(eventRulesPayloadDto);
    }
}
