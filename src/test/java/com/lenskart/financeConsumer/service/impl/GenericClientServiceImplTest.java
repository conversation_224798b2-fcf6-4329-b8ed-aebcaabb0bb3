package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.service.RuleEngineService;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import java.net.URI;



@RunWith(MockitoJUnitRunner.class)
public class GenericClientServiceImplTest {

    @Mock
    RuleEngineService ruleEngineService;

    @Mock
    RestTemplate restTemplate;

    @InjectMocks
    GenericClientServiceImpl genericClientService;
    ObjectMapper objectMapper = new ObjectMapper();

    String payloadString1 = "{\"name\":\"Test\",\"age\":10}";
    Object payload1;

    @Before
    public void init() throws JsonProcessingException {
        MockitoAnnotations.initMocks(this);
        payload1 = objectMapper.readTree(payloadString1);
    }

    @Test
    public void forwardRequest_NullRequestPayload() {
        String body = "{\"$id\":\"1\",\"Success\":\"True\",\"Message\":\"Sales Order 123 created successfully\"}";
        HttpEntity entity = null;
        String service = "http://finance-adaptor.scm.prod-eks.internal/sales-order/create/prod";

        Mockito.when(restTemplate.exchange(Mockito.any(URI.class),
                                           Mockito.eq(HttpMethod.POST),
                                           Mockito.any(HttpEntity.class),
                                           Mockito.<Class<String>>any()))
                .thenReturn(new ResponseEntity<>(body, HttpStatus.OK));

        ResponseEntity responseEntity= genericClientService.forwardRequest(service, new HttpHeaders(), HttpMethod.POST, payload1);
        String responseBody = "{Message=Sales Order 123 created successfully, Success=True, $id=1}";

        Assertions.assertEquals(responseEntity.getStatusCode(), HttpStatus.OK);
        Assertions.assertTrue(responseEntity.getBody().toString().equals(responseBody));
    }
}