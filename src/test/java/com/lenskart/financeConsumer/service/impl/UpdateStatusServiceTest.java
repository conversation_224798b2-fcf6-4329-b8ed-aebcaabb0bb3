package com.lenskart.financeConsumer.service.impl;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.lenskart.financeConsumer.TestUtils.TestUtils;
import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dto.d365requests.StatusAPIRequest;
import com.lenskart.financeConsumer.dto.d365requests.StatusAPIResponse;
import com.lenskart.financeConsumer.model.mongo.D365ApiLog;
import com.lenskart.financeConsumer.model.unireports.UnireportSaleOrderDetails;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.UpdateStatusService;
import com.lenskart.financeConsumer.unireports.readOnlyRepository.UnireportSaleOrderSyncReadRepository;
import com.lenskart.financeConsumer.util.Constants;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@RunWith(SpringRunner.class)
public class UpdateStatusServiceTest {

    @InjectMocks
    UpdateStatusService updateStatusService;

    @Mock
    GenericClientService genericClientService;

    @Mock
    D365LogRepository d365LogRepository;

    @Mock
    UnireportSaleOrderSyncReadRepository unireportSaleOrderSyncReadRepository;

    String entityId = "LKH03SP131616_LKH03";
    String packingSlipMessage = "Serial number 17959417 has not been created for item number 148248..";
    String saleOrderMessage = "Record generated for sales order LKH03SP131616_LKH03";
    String shippingPackageId = "LKH03SP131616";
    String url = "https://lk-uat.sandbox.operations.dynamics.com"+Constants.SalesOrder.URL;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(updateStatusService,"statusUpdateManualOrdersMaxCount",100);
        ReflectionTestUtils.setField(updateStatusService,"statusUpdateManualOrdersPageLimit",10);
        ReflectionTestUtils.setField(updateStatusService,"d365ProdUrl","https://lk-uat.sandbox.operations.dynamics.com");
        ReflectionTestUtils.setField(updateStatusService,"financeAdapterUrl","https://finance-adaptor.scm.preprod.lenskart.com");
        D365ApiLog saleOrderD365ApiLog = new D365ApiLog();
        saleOrderD365ApiLog.setEntityId(shippingPackageId);
        saleOrderD365ApiLog.setApiUrl(url);
        saleOrderD365ApiLog.setRequest(TestUtils.getSaleOrderPayload());
        Mockito.when(d365LogRepository.findTopByEntityIdAndApiUrl(any(), any())).thenReturn(saleOrderD365ApiLog);
    }

    @Test
    @Ignore
    public void testHitD365API() throws Exception {
        StatusAPIRequest statusAPIRequest = new StatusAPIRequest();
        StatusAPIRequest.SalesPackingSlipLineRequest salesPackingSlipLineRequest = new StatusAPIRequest.SalesPackingSlipLineRequest();
        salesPackingSlipLineRequest.setShippingPackageId("LKH03SP131616_LKH03");
        salesPackingSlipLineRequest.setLegalEntity("LKIN");
        statusAPIRequest.set_salesPackingSlipLineRequest(salesPackingSlipLineRequest);
        Mockito.when(genericClientService.forwardRequest(ReflectionTestUtils.getField(updateStatusService,"financeAdapterUrl") + Constants.STATUSAPI.API,
                new HttpHeaders(), HttpMethod.POST, statusAPIRequest)).thenReturn(TestUtils.getStatusResponse());
        StatusAPIResponse statusAPIResponse = updateStatusService.hitD365API(statusAPIRequest);
        StatusAPIResponse.PackingSlip packingSlip = statusAPIResponse.getPackingSlip().get(0);
        StatusAPIResponse.SalesOrder salesOrder = statusAPIResponse.getSalesOrder().get(0);
        assertEquals(packingSlip.getMessage(),packingSlipMessage);
        assertEquals(salesOrder.getMessage(),saleOrderMessage);
        assertEquals("Failure",packingSlip.getStatus());
        assertEquals("Success",salesOrder.getStatus());
        assertEquals(0,packingSlip.getSyncedItemCount());
        assertEquals(6,salesOrder.getSyncedItemCount());
    }

    @Test
    public void testUpdateEntryInMongoSO(){
        String status = "Success";
        D365ApiLog response = updateStatusService.updateEntryInMongo(shippingPackageId,saleOrderMessage, Constants.SalesOrder.URL,status);
        assertEquals(saleOrderMessage,response.getMessage());
        assertEquals(status,response.getStatus());
    }

    @Test
    public void testCreateRequest(){
        StatusAPIRequest statusAPIRequest = updateStatusService.createRequest("LKIN",entityId);
        assertEquals("LKIN",statusAPIRequest.get_salesPackingSlipLineRequest().getLegalEntity());
        assertEquals(entityId,statusAPIRequest.get_salesPackingSlipLineRequest().getShippingPackageId());
    }

    @Test
    public void testUpdateStatus() throws IOException {
        Logger updateUserServiceLogger = (Logger) LoggerFactory.getLogger(UpdateStatusService.class);
        ListAppender<ILoggingEvent> listAppender = new ListAppender<>();
        listAppender.start();
        updateUserServiceLogger.addAppender(listAppender);
        List<ILoggingEvent> logsList = listAppender.list;
        UnireportSaleOrderDetails unireportSaleOrderDetails1 = new UnireportSaleOrderDetails();
        unireportSaleOrderDetails1.setFacilityCode("LKH03");
        unireportSaleOrderDetails1.setShippingPackageCode("LKH03SP131616");
        UnireportSaleOrderDetails unireportSaleOrderDetails2 = new UnireportSaleOrderDetails();
        unireportSaleOrderDetails2.setFacilityCode("LKH03");
        unireportSaleOrderDetails2.setShippingPackageCode("LKH03SP131617");
        UnireportSaleOrderDetails unireportSaleOrderDetails3 = new UnireportSaleOrderDetails();
        unireportSaleOrderDetails3.setFacilityCode("LKH03");
        unireportSaleOrderDetails3.setShippingPackageCode("LKH03SP131618");
        UnireportSaleOrderDetails unireportSaleOrderDetails4 = new UnireportSaleOrderDetails();
        unireportSaleOrderDetails4.setFacilityCode("LKH03");
        unireportSaleOrderDetails4.setShippingPackageCode("LKH03SP131618");
        List<UnireportSaleOrderDetails> orderDetails = new ArrayList<>();
        orderDetails.add(unireportSaleOrderDetails1);
        orderDetails.add(unireportSaleOrderDetails2);
        orderDetails.add(unireportSaleOrderDetails3);
        orderDetails.add(unireportSaleOrderDetails4);
        Pageable pageable = PageRequest.of(0, 10);
        Calendar fromDateCalendar = Calendar.getInstance();
        Calendar toDateCalendar = Calendar.getInstance();
        fromDateCalendar.setTime(new Date());
        fromDateCalendar.add(Calendar.HOUR, -12);
        toDateCalendar.setTime(new Date());
        toDateCalendar.add(Calendar.MINUTE, -10);
        StatusAPIRequest statusAPIRequest = new StatusAPIRequest();
        StatusAPIRequest.SalesPackingSlipLineRequest salesPackingSlipLineRequest = new StatusAPIRequest.SalesPackingSlipLineRequest();
        salesPackingSlipLineRequest.setShippingPackageId("LKH03SP131616_LKH03");
        salesPackingSlipLineRequest.setLegalEntity("LKIN");
        statusAPIRequest.set_salesPackingSlipLineRequest(salesPackingSlipLineRequest);
        Mockito.when(genericClientService.forwardRequest(ReflectionTestUtils.getField(updateStatusService,"financeAdapterUrl") + Constants.STATUSAPI.API,
                new HttpHeaders(), HttpMethod.POST, statusAPIRequest)).thenReturn(TestUtils.getStatusResponse());
        Mockito.when(unireportSaleOrderSyncReadRepository.getEntriesForStatusUpdate(fromDateCalendar.getTime(), toDateCalendar.getTime(), Constants.UnicomSaleOrder.payloadType, pageable)).thenReturn(orderDetails);
        updateStatusService.updateStatus(fromDateCalendar.getTime(),toDateCalendar.getTime(),Constants.UnicomSaleOrder.payloadType);
        List<ILoggingEvent> logs = logsList
                .stream()
                .filter(c -> c.getMessage().startsWith("updateStatus saleOrder"))
                .collect(Collectors.toList());
        assertEquals(4, logs.size());
        assertEquals(Level.INFO, logsList.get(0)
                .getLevel());
    }
}
