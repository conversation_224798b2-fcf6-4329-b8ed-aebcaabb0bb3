//package com.lenskart.financeConsumer.service.impl;
//
//import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
//import com.lenskart.financeConsumer.model.unireports.UnireportSaleOrderDetails;
//import com.lenskart.financeConsumer.service.GenericClientService;
//import com.lenskart.financeConsumer.unireports.readOnlyRepository.UnireportSaleOrderSyncReadRepository;
//import com.lenskart.financeConsumer.util.ManualForwardFlowUtil;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.Spy;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//
//import java.util.HashMap;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.doNothing;
//
//@ExtendWith(MockitoExtension.class)
//public class ManualPackingSlipServiceImplTest {
//
//    @Spy
//    @InjectMocks
//    ManualPackingSlipServiceImpl manualPackingSlipService;
//
//    @Mock
//    UnireportSaleOrderSyncReadRepository unireportSaleOrderSyncReadRepository;
//
//    @Mock
//    ManualForwardFlowUtil manualForwardFlowUtil;
//
//    @Mock
//    GenericClientService genericClientService;
//
//    @BeforeEach
//    public void setUp() throws Exception {
//    }
//
//    @Test
//    void createPackingSlip() throws Exception {
//        UnireportSaleOrderDetails unireportSaleOrderDetails1 = new UnireportSaleOrderDetails();
//        unireportSaleOrderDetails1.setFacilityCode("LKH03");
//        unireportSaleOrderDetails1.setShippingPackageCode("LKH03SP131616");
//        unireportSaleOrderDetails1.setSaleOrderCode("1025");
//        Mockito.when(unireportSaleOrderSyncReadRepository.findTopByShippingPackageCode(anyString())).thenReturn(unireportSaleOrderDetails1);
//        Mockito.doReturn(new PackingSlip()).when(manualPackingSlipService).generatePackingSlipPayload(anyString(),any());
//        doNothing().when(manualForwardFlowUtil).updateResponseInUnireportsDB(anyString(),anyString(),anyString(),anyString(),anyString());
//        doNothing().when(genericClientService).saveLog(anyString(),anyString(),any(),anyString(),anyString(),anyString());
//        HashMap<String,String> responseBody = new HashMap<>();
//        responseBody.put("Status","Success");
//        responseBody.put("Message","Record generated for packing slip LKH03SP131616_LKH03");
//        ResponseEntity responseEntity = new ResponseEntity<HashMap>(
//                responseBody,
//                new HttpHeaders(),
//                HttpStatus.OK
//        );
//        Mockito.when(genericClientService.forwardRequest(any(),any(),any(),any())).thenReturn(responseEntity);
//        ResponseEntity response = manualPackingSlipService.createPackingSlip(anyString());
//        assertEquals("Record generated for packing slip LKH03SP131616_LKH03",((HashMap)response.getBody()).get("Message"));
//    }
//}