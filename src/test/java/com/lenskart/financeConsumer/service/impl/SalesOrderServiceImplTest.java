package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.Classification;
import com.lenskart.core.model.HubMaster;
import com.lenskart.core.model.ItemWisePriceDetails;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.TestUtils.TestUtils;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.ItemWisePricesRepository;
import com.lenskart.financeConsumer.dao.MpOrderDetailsRepository;
import com.lenskart.financeConsumer.dao.mongo.ShippingInvoiceDetailsRepository;
import com.lenskart.financeConsumer.dto.FinanceConsumerResponse.FinanceConsumerResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardEligibilityRequestDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.NewInstanceDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SoLine;
import com.lenskart.financeConsumer.financeDb.inventory.read.HubMasterReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.ItemWisePricesReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.MpOrderDetailsReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.InventoryCorrectionWriteRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.ShipmentInvoiceDetailsRepository;
import com.lenskart.financeConsumer.model.InvoiceDetails;
import com.lenskart.financeConsumer.model.OrderItemGSTDetail;
import com.lenskart.financeConsumer.model.ShippingInvoiceDetails;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.service.CourierInfoService;
import com.lenskart.financeConsumer.service.InvoiceDetailsService;
import com.lenskart.financeConsumer.service.OrderAddressUpdateService;
import com.lenskart.financeConsumer.service.OrderHeaderService;
import com.lenskart.financeConsumer.service.OrderItemGstDetailsService;
import com.lenskart.financeConsumer.service.OrderService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.service.SBRTOrderItemService;
import com.lenskart.financeConsumer.service.ShippingStatusService;
import com.lenskart.financeConsumer.service.SystemPreferenceService;
import com.lenskart.financeConsumer.service.UwItemWisePriceInfoService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.PackingSlipUtil;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import com.lenskart.orderops.model.OrderAddressUpdate;
import com.lenskart.orderops.model.UwItemWisePriceInfo;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.model.ShipmentDetails;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SalesOrderServiceImplTest {

    @InjectMocks
    SalesOrderServiceImpl salesOrderService;

    @Mock
    ItemWisePricesReadRepository itemWisePricesReadRepository;
    @Mock
    ItemWisePricesRepository itemWisePricesRepository;
    @Mock
    UwOrdersService uwOrdersService;
    @Mock
    ForwardTransferOrderService forwardTransferOrderService;

    @Mock
    private UwOrdersReadRepositoryFinance uwOrdersReadRepository;
    @Mock
    OrderItemGstDetailsService orderItemGstDetailsService;
    @Mock
    ProductService productService;
    @Mock
    SystemPreferenceService systemPreferenceService;
    @Mock
    ShippingStatusService shippingStatusService;
    @Mock
    UwItemWisePriceInfoService uwItemWisePriceInfoService;
    @Mock
    ClassificationService classificationService;
    @Mock
    OrderService orderService;
    @Mock
    OrderHeaderService orderHeaderService;
    @Mock
    OrderAddressUpdateService orderAddressUpdateService;
    @Mock
    InvoiceDetailsService invoiceDetailsService;
    @Mock
    CourierInfoService courierInfoService;
    @Mock
    SaleOrderUtil saleOrderUtil;
    @Mock
    PackingSlipUtil packingSlipUtil;
    @Mock
    KafkaTemplate kafkaProducerTemplate;
    @Mock
    FinanceSystemSyncRepository financeSystemSyncRepository;
    @Mock
    MpOrderDetailsReadRepository mpOrderDetailsReadRepository;
    @Mock
    MpOrderDetailsRepository mpOrderDetailsRepository;
    @Mock
    HubMasterReadRepository hubMasterReadRepository;
    @Mock
    ShippingInvoiceDetailsRepository shippingInvoiceDetailsRepository;
    @Mock
    ShipmentInvoiceDetailsRepository shipmentInvoiceDetailsRepository;
    @Mock
    SBRTOrderItemService sbrtOrderItemService;
    @Mock
    InventoryCorrectionWriteRepository inventoryCorrectionWriteRepository;

    @Mock
    ObjectMapper objectMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(salesOrderService, "goldMembershipProductIds", Collections.singletonList(12869));
        ReflectionTestUtils.setField(salesOrderService, "taxRateForShippingCharges", 18.0);
        ReflectionTestUtils.setField(salesOrderService, "blankHsnCodeClassifications", new HashSet<>());
        ReflectionTestUtils.setField(salesOrderService, "isShippingChargesPayloadEnabled", true);
        ReflectionTestUtils.setField(salesOrderService, "invoiceDetailsFetchEnabled", false);
        ReflectionTestUtils.setField(salesOrderService, "packingSlipLive", "false");
        ReflectionTestUtils.setField(salesOrderService,"isGiftCardEnabled",true);
        ReflectionTestUtils.setField(salesOrderService,"dummyBarCodeSuffixValue","_1");

    }

    @Test
    public void fetchSalePriceForUWItemIdForDTCOrder() throws Exception {
        UwOrder uwOrder = new UwOrder();
        uwOrder.setUwItemId(11);
        uwOrder.setProductDeliveryType(Constants.Common.DTC);
        ItemWisePriceDetails itemWisePriceDetails = new ItemWisePriceDetails();
        itemWisePriceDetails.setPrepaidDiscount(10.0);
        itemWisePriceDetails.setGiftVoucherDiscount(0.0);
        itemWisePriceDetails.setImplicitDiscount(0.0);
        itemWisePriceDetails.setLenskartDiscount(0.0);
        itemWisePriceDetails.setLenskartPlusDiscount(0.0);
        itemWisePriceDetails.setCouponDiscount(0.0);
        itemWisePriceDetails.setFcDiscount(0.0);
        itemWisePriceDetails.setExchangeDiscount(0.0);
        itemWisePriceDetails.setItemTotalAfterDiscount(10.0);
        itemWisePriceDetails.setShippingCharges(0.0);
        OrderItemGSTDetail orderItemGSTDetail = new OrderItemGSTDetail();
        orderItemGSTDetail.setCostPerItem(0.0);
        orderItemGSTDetail.setCgstPc(18.0);
        orderItemGSTDetail.setUgstPc(0.0);
        orderItemGSTDetail.setIgstPc(0.0);
        orderItemGSTDetail.setSgstPc(0.0);
        Product product = new Product();

        Mockito.when(itemWisePricesRepository.findByItemId(any())).thenReturn(itemWisePriceDetails);
        Mockito.when(productService.getProduct(anyInt())).thenReturn(product);
        Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(anyInt())).thenReturn(orderItemGSTDetail);
        Mockito.when(systemPreferenceService.getValuesAsList(any(), any())).thenReturn(new ArrayList<>());

        double salePrice = salesOrderService.fetchCostPriceForUWOrder(uwOrder);
        assertEquals("8.47", String.format("%.2f", salePrice));
    }

    @Test
    public void fetchSalePriceForUWItemIdB2BForOrder() throws Exception {
        UwOrder uwOrder = new UwOrder();
        uwOrder.setUwItemId(11);
        uwOrder.setProductDeliveryType(Constants.Common.B2B);
        uwOrder.setNavChannel("COCOB2B");
        ItemWisePriceDetails itemWisePriceDetails = new ItemWisePriceDetails();
        itemWisePriceDetails.setPrepaidDiscount(10.0);
        itemWisePriceDetails.setGiftVoucherDiscount(0.0);
        itemWisePriceDetails.setImplicitDiscount(0.0);
        itemWisePriceDetails.setLenskartDiscount(0.0);
        itemWisePriceDetails.setLenskartPlusDiscount(0.0);
        itemWisePriceDetails.setCouponDiscount(0.0);
        itemWisePriceDetails.setFcDiscount(0.0);
        itemWisePriceDetails.setExchangeDiscount(0.0);
        itemWisePriceDetails.setItemTotalAfterDiscount(10.0);
        itemWisePriceDetails.setShippingCharges(0.0);
        OrderItemGSTDetail orderItemGSTDetail = new OrderItemGSTDetail();
        orderItemGSTDetail.setCostPerItem(0.0);
        orderItemGSTDetail.setCgstPc(18.0);
        orderItemGSTDetail.setUgstPc(0.0);
        orderItemGSTDetail.setIgstPc(0.0);
        orderItemGSTDetail.setSgstPc(0.0);
        Product product = new Product();

        Mockito.when(uwOrdersService.getUwOrderByUwItemId(any())).thenReturn(uwOrder);
        Mockito.when(itemWisePricesRepository.findByItemId(any())).thenReturn(itemWisePriceDetails);
        Mockito.when(productService.getProduct(anyInt())).thenReturn(product);
        Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(anyInt())).thenReturn(orderItemGSTDetail);
        Mockito.when(systemPreferenceService.getValuesAsList(any(), any())).thenReturn(new ArrayList<>());

        double salePrice = salesOrderService.fetchCostPriceForUWOrder(uwOrder);

        assertEquals("8.47", String.format("%.2f", salePrice));
    }

    @Test
    @SneakyThrows
    public void getSoLinesforFreeGoldMemberShipItem() {
        UwOrder uwOrder = new UwOrder();
        uwOrder.setUwItemId(11);
        uwOrder.setProductId(12869);
        uwOrder.setProductDeliveryType(Constants.Common.B2B);
        uwOrder.setNavChannel("COCOB2B");
        List<UwOrder> uwOrderList = new ArrayList<>();
        uwOrderList.add(uwOrder);
        ItemWisePriceDetails itemWisePriceDetails = new ItemWisePriceDetails();
        itemWisePriceDetails.setPrepaidDiscount(00.0);
        itemWisePriceDetails.setGiftVoucherDiscount(0.0);
        itemWisePriceDetails.setImplicitDiscount(0.0);
        itemWisePriceDetails.setLenskartDiscount(0.0);
        itemWisePriceDetails.setShippingCharges(10.0);
        itemWisePriceDetails.setLenskartPlusDiscount(0.0);
        itemWisePriceDetails.setItemTotalAfterDiscount(10.0);
        itemWisePriceDetails.setCouponDiscount(0.0);
        itemWisePriceDetails.setFcDiscount(0.0);
        itemWisePriceDetails.setExchangeDiscount(0.0);
        Classification classification = new Classification();

        List<ItemWisePriceDetails> itemWisePriceDetailsList = new ArrayList<>();
        itemWisePriceDetailsList.add(itemWisePriceDetails);
        OrderItemGSTDetail orderItemGSTDetail = new OrderItemGSTDetail();
        orderItemGSTDetail.setCostPerItem(0.0);

        orderItemGSTDetail.setCgstPc(00.0D);
        orderItemGSTDetail.setIgstPc(0.0D);
        orderItemGSTDetail.setSgstPc(0.0D);
        orderItemGSTDetail.setUgstPc(0.0D);
        orderItemGSTDetail.setHsn("1111");
        Product product = new Product();
        product.setBrand("free");
        product.setClassification(1);
        Order order = new Order();
        OrdersHeader ordersHeader = new OrdersHeader();
        ordersHeader.setLkCountry("IN");

        ShippingStatus shippingStatus1 = new ShippingStatus();
        UwItemWisePriceInfo uwItemWisePriceInfo = new UwItemWisePriceInfo();
        Date dispatchDate = new Date();

        Mockito.when(uwOrdersService.getUwOrderByUwItemId(any())).thenReturn(uwOrder);
        Mockito.when(itemWisePricesReadRepository.findByItemId(anyInt())).thenReturn(itemWisePriceDetails);
        Mockito.when(itemWisePricesRepository.findByItemId(anyInt())).thenReturn(itemWisePriceDetails);
        Mockito.when(itemWisePricesRepository.findByItemIdIn(any())).thenReturn(itemWisePriceDetailsList);
        Mockito.when(productService.getProduct(anyInt())).thenReturn(product);
        Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(anyInt())).thenReturn(orderItemGSTDetail);
        Mockito.when(systemPreferenceService.getValuesAsList(any(), any())).thenReturn(new ArrayList<>());
        Mockito.when(shippingStatusService.getShippingStatus((anyString()))).thenReturn(shippingStatus1);
        Mockito.when(uwItemWisePriceInfoService.getUwItemWisePriceInfoB2B(anyInt())).thenReturn(uwItemWisePriceInfo);
        Mockito.when(classificationService.getClassification(anyInt())).thenReturn(classification);
        Mockito.when(saleOrderUtil.getDispatchDate(any())).thenReturn(dispatchDate);
        Mockito.when(saleOrderUtil.getStoreCode(any())).thenReturn("DA111");
        try (MockedStatic<ProductUtil> pb = mockStatic(ProductUtil.class)) {
            pb.when(() -> ProductUtil.getClassificationDisplayName(anyInt(), anyString()))
                    .thenReturn("");
            ArrayList<SoLine> soLines = salesOrderService.getSoLines(uwOrderList,
                                                                     order,
                                                                     ordersHeader,
                                                                     "DK02",
                                                                     "COCOB2B",
                                                                     true,
                                                                     NewInstanceDto.builder().isNewInstance(false).build(),false );
            SoLine soLineForFoldFreeMembership = soLines.get(0);
            String shippingchargeExpected = String.format("%.2f", 10.0 / 1.18);

            assertEquals(shippingchargeExpected, soLineForFoldFreeMembership.getSalesPrice());
        }

    }

    @Test
    @SneakyThrows
    public void getSoLinesforNonFreeGoldMemberShipItem() {
        UwOrder uwOrder = new UwOrder();
        uwOrder.setUwItemId(11);
        uwOrder.setProductId(12869);
        uwOrder.setProductDeliveryType(Constants.Common.B2B);
        uwOrder.setNavChannel("COCOB2B");
        List<UwOrder> uwOrderList = new ArrayList<>();
        uwOrderList.add(uwOrder);
        ItemWisePriceDetails itemWisePriceDetails = new ItemWisePriceDetails();
        itemWisePriceDetails.setPrepaidDiscount(10.0);
        itemWisePriceDetails.setGiftVoucherDiscount(10.0);
        itemWisePriceDetails.setImplicitDiscount(10.0);
        itemWisePriceDetails.setLenskartDiscount(10.0);
        itemWisePriceDetails.setShippingCharges(0.0);
        itemWisePriceDetails.setLenskartPlusDiscount(0.0);
        itemWisePriceDetails.setCouponDiscount(0.0);
        itemWisePriceDetails.setFcDiscount(0.0);
        itemWisePriceDetails.setExchangeDiscount(0.0);
        itemWisePriceDetails.setItemTotalAfterDiscount(10.0);
        Classification classification = new Classification();

        List<ItemWisePriceDetails> itemWisePriceDetailsList = new ArrayList<>();
        itemWisePriceDetailsList.add(itemWisePriceDetails);
        OrderItemGSTDetail orderItemGSTDetail = new OrderItemGSTDetail();
        orderItemGSTDetail.setCostPerItem(0.0);

        orderItemGSTDetail.setCgstPc(00.0D);
        orderItemGSTDetail.setIgstPc(0.0D);
        orderItemGSTDetail.setSgstPc(0.0D);
        orderItemGSTDetail.setUgstPc(0.0D);
        orderItemGSTDetail.setHsn("1111");
        Product product = new Product();
        product.setBrand("free");
        product.setClassification(1);
        Order order = new Order();
        OrdersHeader ordersHeader = new OrdersHeader();
        ordersHeader.setLkCountry("IN");

        UwItemWisePriceInfo uwItemWisePriceInfo = new UwItemWisePriceInfo();
        Date dispatchDate = new Date();

        Mockito.when(uwOrdersService.getUwOrderByUwItemId(any())).thenReturn(uwOrder);
        Mockito.when(itemWisePricesReadRepository.findByItemId(any())).thenReturn(itemWisePriceDetails);
        Mockito.when(itemWisePricesReadRepository.findByItemIdIn(any())).thenReturn(itemWisePriceDetailsList);
        Mockito.when(productService.getProduct(anyInt())).thenReturn(product);
        Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(anyInt())).thenReturn(orderItemGSTDetail);
        Mockito.when(systemPreferenceService.getValuesAsList(any(), any())).thenReturn(new ArrayList<>());
//        Mockito.when(shippingStatusService.getShippingStatus((anyString()))).thenReturn(shippingStatus1);
        Mockito.when(uwItemWisePriceInfoService.getUwItemWisePriceInfoB2B(anyInt())).thenReturn(uwItemWisePriceInfo);
        Mockito.when(classificationService.getClassification(anyInt())).thenReturn(classification);
        Mockito.when(saleOrderUtil.getDispatchDate(any())).thenReturn(dispatchDate);
        Mockito.when(saleOrderUtil.getStoreCode(any())).thenReturn("DA111");
        try (MockedStatic<ProductUtil> pb = mockStatic(ProductUtil.class)) {
            pb.when(() -> ProductUtil.getClassificationDisplayName(anyInt(), anyString()))
                    .thenReturn("");
            ArrayList<SoLine> soLines = salesOrderService.getSoLines(uwOrderList,
                                                                     order,
                                                                     ordersHeader,
                                                                     "DK02",
                                                                     "COCOB2B",
                                                                     true,
                                                                     NewInstanceDto.builder().isNewInstance(false).build(),false );
            SoLine soLineForFoldFreeMembership = soLines.get(0);
            String salesPriceExpected = String.format("%.2f", 40.0);

            assertEquals(salesPriceExpected, soLineForFoldFreeMembership.getSalesPrice());
        }

    }

    @Test
    public void generatePayload() throws Exception {
        try (MockedStatic<ProductUtil> pb = mockStatic(ProductUtil.class)) {
            ReflectionTestUtils.setField(salesOrderService,"isSBRTFlowEnabledForSalesOrder",true);
            pb.when(() -> ProductUtil.getClassificationDisplayName(anyInt(), anyString()))
                    .thenReturn("Eyeglasses");
            Map<String, String> CountryCurrencyMapping = new HashMap<>();
            CountryCurrencyMapping.put("IN", "INR");
            Whitebox.setInternalState(ProductUtil.class, "CountryCurrencyMapping", CountryCurrencyMapping);

            Mockito.when(saleOrderUtil.getInventLocationId(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                    "DK02");
            Mockito.when(saleOrderUtil.getSubChannel(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                    "WebB2B");
            Mockito.when(saleOrderUtil.getTaxCheck(ArgumentMatchers.anyString(),
                                                   ArgumentMatchers.any(),
                                                   ArgumentMatchers.anyString())).thenReturn("yes");
            Mockito.when(saleOrderUtil.getDeliveryName(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                    "test test");
            Mockito.when(saleOrderUtil.getStoreCode(ArgumentMatchers.any())).thenReturn(null);
            Mockito.when(classificationService.getClassification(11355)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                                "Classification.json",
                                                                                                                Classification.class));

            doAnswer(invocation -> {
                Object[] args = invocation.getArguments();
                SalesOrderHeader salesOrderHeader = (SalesOrderHeader) args[0];
                salesOrderHeader.setAddressCountryRegionId("IND");
                salesOrderHeader.setAddressStreet("test \ntest");
                salesOrderHeader.setAddressZipCode("122008");
                salesOrderHeader.setAddressCity("GURUGRAM");
                salesOrderHeader.setAddressState("HR");
                return null;
            }).when(saleOrderUtil).setAddress(ArgumentMatchers.any(), ArgumentMatchers.any(),ArgumentMatchers.any());

            String string = "2023-05-25 12:40:17";
            Date date = new SimpleDateFormat("yyyy-MM-dd").parse(string);
            Mockito.when(saleOrderUtil.getDispatchDate(ArgumentMatchers.any())).thenReturn(date);
            Mockito.when(mpOrderDetailsRepository.findOneByIncrementIdNew(1930523229)).thenReturn(null);


            Mockito.when(mpOrderDetailsReadRepository.findOneByIncrementIdNew(1930523229)).thenReturn(null);
            Mockito.when(financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(ArgumentMatchers.any(),
                                                                                                        ArgumentMatchers.any(),
                                                                                                        ArgumentMatchers.any(),
                                                                                                        ArgumentMatchers.anyString()))
                    .thenReturn((TestUtils.getObjectFromFile(true, "FinanceSourceSystemSync.json",
                                                                                                                                                                                                               FinanceSourceSystemSync.class)));
            Mockito.when(systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365,
                                                                 Constants.SYSTEM_PREFERENCE_KEYS.D365_insurance_pids)).thenReturn(
                    Arrays.asList("135217",
                                  "152747",
                                  "152748",
                                  "152749"));
            Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(319903982)).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "OrderItemGSTDetailVirtual.json",
                    OrderItemGSTDetail.class));
            Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(319903981)).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "OrderItemGSTDetailNonVirtual.json",
                    OrderItemGSTDetail.class));
            Mockito.when(uwOrdersService.getUwOrderByUwItemId(319903981)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                                 "UwOrderLK.json",
                                                                                                                 UwOrder.class));
            Mockito.when(itemWisePricesReadRepository.findByItemId(258524787)).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "ItemWisePriceDetails.json",
                    ItemWisePriceDetails.class));
            Mockito.when(productService.getProduct(138362)).thenReturn(TestUtils.getObjectFromFile(true, "Product.json",
                                                                                                   Product.class));
            Mockito.when(orderService.findOrderByItemId(258524787)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                           "PaymentOrder.json",
                                                                                                           Order.class));
            Mockito.when(shippingStatusService.getShippingStatus("LKH03SP125863")).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "ShippingStatus.json",
                    ShippingStatus.class));
            Mockito.when(courierInfoService.getCourierInfoFromMongo("LKH03SP125863",
                                                                    "LKH03")).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "ShipmentDetails.json",
                    com.lenskart.wm.model.ShipmentDetails.class));
            Mockito.when(invoiceDetailsService.getInvoiceDetails("DK02SP12938")).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "InvoiceDetails.json",
                    InvoiceDetails.class));
            Mockito.when(orderAddressUpdateService.getOrderAddressUpdateShipping(51242811)).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "OrderAddressUpdate.json",
                    OrderAddressUpdate.class));
            Mockito.when(orderAddressUpdateService.getOrderAddressUpdateBilling(51242811)).thenReturn(TestUtils.getObjectFromFile(
                    true,
                    "OrderAddressUpdate.json",
                    OrderAddressUpdate.class));
            Mockito.when(orderHeaderService.getOrderHeader(1930523229)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                               "OrdersHeader.json",
                                                                                                               OrdersHeader.class));
            Mockito.when(uwOrdersService.getUwOrders(Collections.singletonList(319903982))).thenReturn(TestUtils.getTestListUwOrder(
                    true,
                    "UwOrdersList.json"));
            Mockito.when(uwOrdersService.getUwOrderByUwItemId(319903981)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                                 "FirstUwOrderLK.json",
                                                                                                                 UwOrder.class));
            Mockito.when(orderService.getOrders(1930523229)).thenReturn(TestUtils.getTestListOrder(true,
                                                                                                   "Orders.json"));
            ReflectionTestUtils.setField(salesOrderService,"nonSBRTItemFlag","00000684");
            ReflectionTestUtils.setField(salesOrderService,"SBRTItemFlag","00000683");
            ReflectionTestUtils.setField(salesOrderService,"isGiftCardEnabled",true);

            SalesOrderHeader result = salesOrderService.generatePayload(Collections.singletonList(319903982),"url",
                                                                        false,
                                                                        NewInstanceDto.builder().isNewInstance(
                                                                                false).id(3L).build());
            SalesOrderHeader expected = TestUtils.getObjectFromFile(true,
                                                                    "SaleOrderHeader.json",
                                                                    SalesOrderHeader.class);
            assertEquals(expected, result);
        }
    }

    @Test
    public void generatePackingSlipPayload() throws Exception {
        Mockito.when(financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(ArgumentMatchers.any(),
                                                                                                    ArgumentMatchers.any(),
                                                                                                    ArgumentMatchers.any(),
                                                                                                    ArgumentMatchers.anyString()))
                .thenReturn(TestUtils.getObjectFromFile(true, "FinanceSourceSystemSyncPS.json", FinanceSourceSystemSync.class));

        Mockito.when(uwOrdersService.getUwOrders(Collections.singletonList(319903982))).thenReturn(TestUtils.getTestListUwOrder(
                true,
                "UwOrdersList.json"));
        Mockito.when(uwOrdersService.getUwOrderByUwItemId(319903981)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                             "UwOrderLK.json",
                                                                                                             UwOrder.class));
        Mockito.when(hubMasterReadRepository.findByFacilityCode("LKH03")).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                                 "HubMaster.json",
                                                                                                                 HubMaster.class));
        Mockito.when(orderHeaderService.getOrderHeader(1930523229)).thenReturn(TestUtils.getObjectFromFile(true,
                                                                                                           "OrdersHeader.json",
                                                                                                           OrdersHeader.class));
        Mockito.when(saleOrderUtil.getInventLocationId(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                "DK02");
        Mockito.when(orderService.getOrders(1930523229)).thenReturn(TestUtils.getTestListOrder(true,
                                                                                               "Orders.json"));
        Mockito.when(packingSlipUtil.getLegalEntity(ArgumentMatchers.any(),ArgumentMatchers.any())).thenReturn("LKIN");
        Mockito.when(courierInfoService.getCourierInfoFromMongo("LKH03SP125863",
                                                                "LKH03")).thenReturn(TestUtils.getObjectFromFile(
                true,
                "ShipmentDetails.json",
                com.lenskart.wm.model.ShipmentDetails.class));
        Mockito.when(shippingInvoiceDetailsRepository.findById("LKH03SP125863")).thenReturn(Optional.of(TestUtils.getObjectFromFile(
                true,
                "ShippingInvoiceDetails.json",
                ShippingInvoiceDetails.class)));

        Mockito.when(mpOrderDetailsReadRepository.findOneByIncrementIdNew(1930523229)).thenReturn(null);
        Mockito.when(courierInfoService.getCourierInfo("LKH03SP125863")).thenReturn(null);
        Mockito.when(shippingStatusService.getShippingStatus("LKH03SP125863")).thenReturn(TestUtils.getObjectFromFile(
                true,
                "ShippingStatus.json",
                ShippingStatus.class));
        Mockito.when(shipmentInvoiceDetailsRepository.findByShippingPackageIdAndFacilityCode("DK02SP12938",
                                                                                             "DK02")).thenReturn(null);

        PackingSlip result = salesOrderService.generatePackingSlipPayload(Collections.singletonList(319903982),
                                                                          false,
                                                                          NewInstanceDto.builder().isNewInstance(
                                                                            false).id(3L).build());
        PackingSlip expected = TestUtils.getObjectFromFile(true,
                                                           "PackingSlip.json",
                                                           PackingSlip.class);
        assertEquals(expected, result);



    }
    @Test
    public void generatePayloadDtc() throws Exception {
        try (MockedStatic<ProductUtil> pb = mockStatic(ProductUtil.class)) {
            ReflectionTestUtils.setField(salesOrderService,"isSBRTFlowEnabledForSalesOrder",true);
            pb.when(() -> ProductUtil.getClassificationDisplayName(anyInt(), anyString()))
                    .thenReturn("Loyalty Services");
            Map<String, String> CountryCurrencyMapping = new HashMap<>();
            CountryCurrencyMapping.put("IN", "INR");
            Whitebox.setInternalState(ProductUtil.class, "CountryCurrencyMapping", CountryCurrencyMapping);

            Mockito.when(saleOrderUtil.getInventLocationId(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                    "LKH03");
            Mockito.when(saleOrderUtil.getSubChannel(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                    "COCODTC");
            Mockito.when(saleOrderUtil.getTaxCheck(ArgumentMatchers.anyString(),
                                                   ArgumentMatchers.any(),
                                                   ArgumentMatchers.anyString())).thenReturn("yes");
            Mockito.when(saleOrderUtil.getDeliveryName(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                    "Dark Store Dark Store");
            Mockito.when(saleOrderUtil.getStoreCode(ArgumentMatchers.any())).thenReturn("ST05");
            Mockito.when(classificationService.getClassification(21566)).thenReturn(TestUtils.getObjectFromFile(false,
                                                                                                                "Classification.json",
                                                                                                                Classification.class));

            doAnswer(invocation -> {
                Object[] args = invocation.getArguments();
                SalesOrderHeader salesOrderHeader = (SalesOrderHeader) args[0];
                salesOrderHeader.setAddressCountryRegionId("IND");
                salesOrderHeader.setAddressStreet("M Block Gk-2 M-38, Ground floor &amp; Basement, M Block Market, GK-2, New Delhi \n");
                salesOrderHeader.setAddressZipCode("121003");
                salesOrderHeader.setAddressCity("Delhi");
                salesOrderHeader.setAddressState("HR");
                return null;
            }).when(saleOrderUtil).setAddress(ArgumentMatchers.any(), ArgumentMatchers.any(),ArgumentMatchers.any());


            String string = "2023-10-03 12:40:17";
            Date date = new SimpleDateFormat("yyyy-MM-dd").parse(string);
            Mockito.when(saleOrderUtil.getDispatchDate(ArgumentMatchers.any())).thenReturn(date);
            Mockito.when(mpOrderDetailsRepository.findOneByIncrementIdNew(1930547893)).thenReturn(null);


            Mockito.when(mpOrderDetailsReadRepository.findOneByIncrementIdNew(1930523229)).thenReturn(null);
            Mockito.when(financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(ArgumentMatchers.any(),
                                                                                                        ArgumentMatchers.any(),
                                                                                                        ArgumentMatchers.anyString(),
                                                                                                        ArgumentMatchers.anyString()))
                    .thenReturn(TestUtils.getObjectFromFile(false, "FinanceSourceSystemSync.json", FinanceSourceSystemSync.class));
            Mockito.when(systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365,
                                                                 Constants.SYSTEM_PREFERENCE_KEYS.D365_insurance_pids)).thenReturn(
                    Arrays.asList("135217",
                                  "152747",
                                  "152748",
                                  "152749"));
            Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(419680494)).thenReturn(TestUtils.getObjectFromFile(
                    false,
                    "OrderItemGSTDetailVirtual.json",
                    OrderItemGSTDetail.class));
            Mockito.when(itemWisePricesReadRepository.findByItemId(362119020)).thenReturn(TestUtils.getObjectFromFile(
                    false,
                    "ItemWisePriceDetails.json",
                    ItemWisePriceDetails.class));
            Mockito.when(productService.getProduct(148393)).thenReturn(TestUtils.getObjectFromFile(false, "Product.json",
                                                                                                   Product.class));
            Mockito.when(orderService.findOrderByItemId(362119020)).thenReturn(TestUtils.getObjectFromFile(false,
                                                                                                           "PaymentOrder.json",
                                                                                                           Order.class));
            Mockito.when(shippingStatusService.getShippingStatus("LKH03SP130842")).thenReturn(TestUtils.getObjectFromFile(
                    false,
                    "ShippingStatus.json",
                    ShippingStatus.class));
            Mockito.when(courierInfoService.getCourierInfoFromMongo("LKH03SP130842",
                                                                    "LKH03")).thenReturn(TestUtils.getObjectFromFile(
                    false,
                    "ShipmentDetails.json",
                    com.lenskart.wm.model.ShipmentDetails.class));
            Mockito.when(invoiceDetailsService.getInvoiceDetails("LKH03SP130842")).thenReturn(null);
            Mockito.when(orderAddressUpdateService.getOrderAddressUpdateShipping(51267695)).thenReturn(TestUtils.getObjectFromFile(
                    false,
                    "OrderAddressUpdate.json",
                    OrderAddressUpdate.class));
            Mockito.when(orderAddressUpdateService.getOrderAddressUpdateBilling(51267695)).thenReturn(TestUtils.getObjectFromFile(
                    false,
                    "OrderAddressUpdate.json",
                    OrderAddressUpdate.class));
            Mockito.when(orderHeaderService.getOrderHeader(1930547893)).thenReturn(TestUtils.getObjectFromFile(false,
                                                                                                               "OrdersHeader.json",
                                                                                                               OrdersHeader.class));
            Mockito.when(uwOrdersService.getUwOrders(Collections.singletonList(419680494))).thenReturn(TestUtils.getTestListUwOrder(
                    false,
                    "UwOrdersList.json"));
            Mockito.when(orderService.getOrders(1930547893)).thenReturn(TestUtils.getTestListOrder(false,
                                                                                                   "Orders.json"));
            Mockito.when(sbrtOrderItemService.fetchUwItemIdsFromSBRTOrderItem(any())).thenReturn(Arrays.asList(1));
            ReflectionTestUtils.setField(salesOrderService,"nonSBRTItemFlag","00000684");
            ReflectionTestUtils.setField(salesOrderService,"SBRTItemFlag","00000683");
            ReflectionTestUtils.setField(salesOrderService,"isGiftCardEnabled",true);
            SalesOrderHeader result = salesOrderService.generatePayload(Collections.singletonList(419680494),
                                                                        "url",false,
                                                                        NewInstanceDto.builder().isNewInstance(
                                                                                false).id(5L).build());
            SalesOrderHeader expected = TestUtils.getObjectFromFile(false,
                                                                    "SaleOrderHeader.json",
                                                                    SalesOrderHeader.class);
            assertEquals(expected, result);
        }
    }
    @Test
    public void generatePackingSlipPayloadDtc() throws Exception {
        Mockito.when(financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(ArgumentMatchers.any(),
                                                                                                    ArgumentMatchers.any(),
                                                                                                    ArgumentMatchers.anyString(),
                                                                                                    ArgumentMatchers.anyString()))
                .thenReturn(TestUtils.getObjectFromFile(false, "FinanceSourceSystemSyncPS.json", FinanceSourceSystemSync.class));

        Mockito.when(uwOrdersService.getUwOrders(Collections.singletonList(419680494))).thenReturn(TestUtils.getTestListUwOrder(
                false,
                "UwOrdersList.json"));
        Mockito.when(orderHeaderService.getOrderHeader(1930547893)).thenReturn(TestUtils.getObjectFromFile(false,
                                                                                                           "OrdersHeader.json",
                                                                                                           OrdersHeader.class));
        Mockito.when(saleOrderUtil.getInventLocationId(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                "LKH03");
        Mockito.when(orderService.getOrders(1930547893)).thenReturn(TestUtils.getTestListOrder(false,
                                                                                               "Orders.json"));
        Mockito.when(packingSlipUtil.getLegalEntity(ArgumentMatchers.any(),ArgumentMatchers.any())).thenReturn("LKIN");
        Mockito.when(courierInfoService.getCourierInfoFromMongo("LKH03SP130842",
                                                                "LKH03")).thenReturn(TestUtils.getObjectFromFile(
                false,
                "ShipmentDetails.json",
                ShipmentDetails.class));

        String string = "2023-10-03 12:40:17";
        Date date = new SimpleDateFormat("yyyy-MM-dd").parse(string);
        Mockito.when(saleOrderUtil.getDispatchDate(ArgumentMatchers.any())).thenReturn(date);
        Mockito.when(mpOrderDetailsReadRepository.findOneByIncrementIdNew(1930547893)).thenReturn(null);
        Mockito.when(courierInfoService.getCourierInfo("LKH03SP130842")).thenReturn(null);
        Mockito.when(shippingStatusService.getShippingStatus("LKH03SP130842")).thenReturn(TestUtils.getObjectFromFile(
                false,
                "ShippingStatus.json",
                ShippingStatus.class));
        Mockito.when(shipmentInvoiceDetailsRepository.findByShippingPackageIdAndFacilityCode("DK02SP12938",
                                                                                             "DK02")).thenReturn(null);

        PackingSlip result = salesOrderService.generatePackingSlipPayload(Collections.singletonList(419680494),
                                                                          false,
                                                                          NewInstanceDto.builder().isNewInstance(
                                                                                  false).id(5L).build());
        PackingSlip expected = TestUtils.getObjectFromFile(false,
                                                           "PackingSlip.json",
                                                           PackingSlip.class);
        assertEquals(expected, result);


    }

    @Test
    public void createSaleOrderTestForTransferOrder() throws Exception {
        String shipmentId ="";
        String warehouseFacilityCode ="";
        NewInstanceDto newInstanceDto = NewInstanceDto.builder().isNewInstance(false).id(1L).build();
        UwOrder uwOrder = new UwOrder();
        uwOrder.setProductDeliveryType("DTC");
        uwOrder.setShippingPackageId("SDDCDV");
        uwOrder.setFacilityCode("NXS1");
        OrdersHeader ordersHeader = new OrdersHeader();
        ordersHeader.setLkCountry("IN");
        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");

        ReflectionTestUtils.setField(salesOrderService, "saleOrderEligibleCountry", Collections.singletonList("IN"));

        when(uwOrdersReadRepository.findByUwItemId(anyInt())).thenReturn((uwOrder));
        when( orderHeaderService.getOrderHeader(anyInt())).thenReturn(ordersHeader);
        when(saleOrderUtil.fetchFinanceSourceSystemRecord(anyString(),anyString())).thenReturn(financeSourceSystemSync);
       //doReturn(true).when(salesOrderService).is
        when(forwardTransferOrderService.isValidTransferOrder(any())).thenReturn(true);
        ReflectionTestUtils.setField(salesOrderService,"giftCardTopic","gift");
        assertNull(salesOrderService.createSalesOrder(shipmentId,warehouseFacilityCode ,Arrays.asList(1), "lenskrt.com",
                null, false, newInstanceDto));

    }


    @Test
    public void processGiftCardIfApplicableForUwItems(){

        UwOrder uwOrder = new UwOrder();
        uwOrder.setProductDeliveryType("DTC");
        uwOrder.setShippingPackageId("SDDCDV");
        uwOrder.setFacilityCode("NXS1");

        ItemWisePriceDetails itemWisePriceDetails = new ItemWisePriceDetails();
        itemWisePriceDetails.setPrepaidDiscount(00.0);
        itemWisePriceDetails.setGiftCardDiscount(10.0);

        List<ItemWisePriceDetails> itemWisePriceDetailsList = new ArrayList<>();
        itemWisePriceDetailsList.add(itemWisePriceDetails);

        when(uwOrdersReadRepository.findByUwItemIdIn(any())).thenReturn(Arrays.asList(uwOrder));
        //when(uwOrdersReadRepository.findByUwItemId(any())).then(uwOrder);
        when(itemWisePricesReadRepository.
                findByItemIdIn(any())).thenReturn(itemWisePriceDetailsList);
        ReflectionTestUtils.setField(salesOrderService,"giftCardTopic","gift");

        assertNull(salesOrderService.processGiftCardIfApplicableForUwItems(Arrays.asList(1),"LKIN","SCM"));
    }
    @Test
    public void processGiftCardIfApplicableForUwItemsReturn(){

        UwOrder uwOrder = new UwOrder();
        uwOrder.setProductDeliveryType("DTC");
        uwOrder.setShippingPackageId("SDDCDV");
        uwOrder.setFacilityCode("NXS1");

        ItemWisePriceDetails itemWisePriceDetails = new ItemWisePriceDetails();
        itemWisePriceDetails.setPrepaidDiscount(00.0);
        itemWisePriceDetails.setGiftCardDiscount(10.0);

        List<ItemWisePriceDetails> itemWisePriceDetailsList = new ArrayList<>();
        itemWisePriceDetailsList.add(itemWisePriceDetails);

        when(uwOrdersReadRepository.findByUwItemIdIn(any())).thenReturn(Arrays.asList(uwOrder));
        //when(uwOrdersReadRepository.findByUwItemId(any())).then(uwOrder);
        when(itemWisePricesReadRepository.
                findByItemIdIn(any())).thenReturn(itemWisePriceDetailsList);

        GiftCardEligibilityRequestDto giftCardEligibilityRequestDto = GiftCardEligibilityRequestDto.builder().source("RETURN").uwItems(Arrays.asList(1,2)).build();
        ReflectionTestUtils.setField(salesOrderService,"giftCardTopic","gift");
     FinanceConsumerResponseDto financeConsumerResponseDto =   salesOrderService.checkIfGiftCardApplicableForUwItems(giftCardEligibilityRequestDto);
     assertEquals(financeConsumerResponseDto.getMessage(),"Items eligible for gift card Sync");
    }
    @Test
    public void testUpdatedFinanceSourceEntityByIds_withValidIds() {
        Set<Long> ids= new HashSet<>();
        ids.add(123L);
        ids.add(124L);
        FinanceSourceSystemSync financeSourceSystemSync= new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setEntityId("LDK0260913459");
        financeSourceSystemSync.setFacilityCode("DK02");

        FinanceSourceSystemSync financeSourceSystem= new FinanceSourceSystemSync();
        financeSourceSystem.setId(2L);
        financeSourceSystem.setEntityId("LDK0260913458");
        financeSourceSystem.setFacilityCode("DK02");

        List<FinanceSourceSystemSync> financeSourceSystemSyncList = Arrays.asList(
                financeSourceSystemSync,
                financeSourceSystem
        );

        when(financeSystemSyncRepository.findAllByIdIn(ids)).thenReturn(financeSourceSystemSyncList);
        salesOrderService.updatedFinanceSourceEntityByIds(ids);
        verify(financeSystemSyncRepository, times(1)).findAllByIdIn(ids);
        verify(saleOrderUtil, times(2)).getUwItemIds(anyString(), anyString());
    }

    @Test
    public void testUpdatedFinanceSourceEntityByIds_withEmptyIds() {
        Set<Long> emptyIds = new HashSet<>();
        salesOrderService.updatedFinanceSourceEntityByIds(emptyIds);
        verify(financeSystemSyncRepository, never()).findAllByIdIn(emptyIds);
        verify(saleOrderUtil, never()).getUwItemIds(anyString(), anyString());
    }

    @Test
    public void testUpdatedFinanceSourceEntityByIds_withException() {
        Set<Long> ids= new HashSet<>();
        ids.add(123L);
        ids.add(124L);
        when(financeSystemSyncRepository.findAllByIdIn(ids)).thenThrow(new RuntimeException("Connection not available"));
        salesOrderService.updatedFinanceSourceEntityByIds(ids);
        verify(financeSystemSyncRepository, times(1)).findAllByIdIn(ids);
        verify(saleOrderUtil, never()).getUwItemIds(anyString(), anyString());
    }

    @Test
    public void testUpdatedFinanceSourceEntityByIds_withNullIds() {
        salesOrderService.updatedFinanceSourceEntityByIds(null);
        verify(financeSystemSyncRepository, never()).findAllByIdIn(anySet());
        verify(saleOrderUtil, never()).getUwItemIds(anyString(), anyString());
    }
}