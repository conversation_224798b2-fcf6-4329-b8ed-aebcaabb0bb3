package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.GetInvoiceRequestDTO;
import com.lenskart.financeConsumer.exceptions.DatabaseException;
import com.lenskart.financeConsumer.exceptions.InvoiceNotFoundException;
import com.lenskart.financeConsumer.financeDb.writeRepository.InvoicePostingRepository;
import com.lenskart.financeConsumer.model.enums.InvoicePostingStatus;
import com.lenskart.financeConsumer.model.enums.InvoicePostingSubStatus;
import com.lenskart.financeConsumer.model.enums.SortOrderType;
import com.lenskart.financeConsumer.model.financeDb.Invoice;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InvoicePostingRepositoryServiceImplTest {

    @InjectMocks
    InvoicePostingRepositoryServiceImpl invoicePostingRepositoryService;
    @Mock
    InvoicePostingRepository invoicePostingRepository;

    private final List<Invoice> invoiceList = new ArrayList<>();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        invoiceList.add(invoice);
    }

    @Test
    public void getAllInvoice() {
        Pageable tempPageable = PageRequest.of(0, 1);
        Page<Invoice> invoicePage = new PageImpl<>(invoiceList, tempPageable, 1);
        GetInvoiceRequestDTO getInvoiceRequestDTO = new GetInvoiceRequestDTO();
        getInvoiceRequestDTO.setPageNumber(0);
        getInvoiceRequestDTO.setPageSize(2);
        getInvoiceRequestDTO.setSortKey("createdAt");
        getInvoiceRequestDTO.setSortOrder(SortOrderType.ASCENDING);
        Pageable pageable = PageRequest.of(getInvoiceRequestDTO.getPageNumber(), getInvoiceRequestDTO.getPageSize(),
                                           Sort.by(getInvoiceRequestDTO.getSortKey()).ascending());
        when(invoicePostingRepository.findAll(pageable)).thenReturn(invoicePage);
        Page<Invoice> responseList = invoicePostingRepositoryService.getAllInvoice(pageable);
        Assertions.assertEquals(1, responseList.getTotalElements());
        Mockito.verify(invoicePostingRepository, Mockito.times(1)).findAll(pageable);
    }

    @Test(expected = DatabaseException.class)
    public void getAllInvoice_Exception() {
        GetInvoiceRequestDTO getInvoiceRequestDTO = new GetInvoiceRequestDTO();
        getInvoiceRequestDTO.setPageNumber(0);
        getInvoiceRequestDTO.setPageSize(2);
        getInvoiceRequestDTO.setSortKey("createdAt");
        getInvoiceRequestDTO.setSortOrder(SortOrderType.ASCENDING);
        Pageable pageable = PageRequest.of(getInvoiceRequestDTO.getPageNumber(), getInvoiceRequestDTO.getPageSize(),
                                           Sort.by(getInvoiceRequestDTO.getSortKey()).ascending());
        when(invoicePostingRepository.findAll(pageable)).thenThrow(EmptyResultDataAccessException.class);
        invoicePostingRepositoryService.getAllInvoice(pageable);
        Mockito.verify(invoicePostingRepository, Mockito.times(1)).findAll(pageable);
    }

    @Test
    public void getInvoiceById() {
        long invoiceId = 1L;
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        when(invoicePostingRepository.findById(invoiceId)).thenReturn(Optional.of(invoice));
        Invoice response = invoicePostingRepositoryService.getInvoiceById(invoiceId);
        Assertions.assertEquals(1, response.getId());
    }

    @Test(expected = InvoiceNotFoundException.class)
    public void getInvoiceById_NotFound() {
        long invoiceId = 999L;
        when(invoicePostingRepository.findById(invoiceId)).thenReturn(Optional.empty());
        invoicePostingRepositoryService.getInvoiceById(invoiceId);
        Mockito.verify(invoicePostingRepository, Mockito.times(1)).findById(invoiceId);
        Mockito.verifyNoMoreInteractions(invoicePostingRepository);
    }

    @Test(expected = DatabaseException.class)
    public void getInvoiceById_Exception() {
        long invoiceId = 1L;
        when(invoicePostingRepository.findById(invoiceId)).thenThrow(EmptyResultDataAccessException.class);
        invoicePostingRepositoryService.getInvoiceById(invoiceId);
        Mockito.verify(invoicePostingRepository, Mockito.times(1)).findById(invoiceId);
        Mockito.verifyNoMoreInteractions(invoicePostingRepository);
    }

    @Test
    public void deleteInvoice() {
        long invoiceId = 1L;
        invoicePostingRepositoryService.deleteInvoice(invoiceId);
        Mockito.verify(invoicePostingRepository, Mockito.times(1)).deleteById(invoiceId);
        Mockito.verifyNoMoreInteractions(invoicePostingRepository);
    }

    @Test(expected = DatabaseException.class)
    public void deleteInvoice_Exception() {
        long invoiceId = 1L;
        Mockito.doThrow(new RuntimeException("Database error")).when(invoicePostingRepository).deleteById(invoiceId);
        invoicePostingRepositoryService.deleteInvoice(invoiceId);
    }

    @Test(expected = InvoiceNotFoundException.class)
    public void deleteInvoice_NotFound() {
        long invoiceId = 1L;
        Mockito.doThrow(EmptyResultDataAccessException.class).when(invoicePostingRepository).deleteById(invoiceId);
        invoicePostingRepositoryService.deleteInvoice(invoiceId);
    }

    @Test
    public void getInvoiceByVendorInvoiceNumber() {
        String invoiceNumber = "I1";
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        when(invoicePostingRepository.findByVendorInvoiceNumber(invoiceNumber)).thenReturn(invoice);
        Invoice response = invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(invoiceNumber);
        Assertions.assertEquals(1, response.getId());
    }

    @Test
    public void getInvoiceByVendorInvoiceNumber_Null() {
        String invoiceNumber = "I1";
        when(invoicePostingRepository.findByVendorInvoiceNumber(invoiceNumber)).thenReturn(null);
        Invoice response = invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(invoiceNumber);
        Assertions.assertNull(response);
    }

    @Test(expected = DatabaseException.class)
    public void getInvoiceByVendorInvoiceNumber_Exception() {
        String invoiceNumber = "I1";
        when(invoicePostingRepository.findByVendorInvoiceNumber(invoiceNumber)).thenThrow(EmptyResultDataAccessException.class);
        Invoice response = invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(invoiceNumber);
    }

    @Test
    public void saveInvoice() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepository.save(invoice)).thenReturn(invoice);
        Invoice response = invoicePostingRepositoryService.saveInvoice(invoice);
        Assertions.assertEquals(1, response.getId());
    }

    @Test(expected = DatabaseException.class)
    public void saveInvoice_Exception() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepository.save(invoice)).thenThrow(EmptyResultDataAccessException.class);
        Invoice response = invoicePostingRepositoryService.saveInvoice(invoice);
    }

    @Test
    public void getInvoiceBySubStatus() {
    }

    @Test
    public void updateInvoiceStatus() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepository.save(Mockito.any())).thenReturn(invoice);
        Invoice response = invoicePostingRepositoryService.updateInvoiceStatus(invoice, InvoicePostingStatus.IN_PROGRESS);
        Assertions.assertEquals(InvoicePostingStatus.IN_PROGRESS, response.getInvoiceStatus());
    }

    @Test
    public void updateInvoiceSubStatus() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepository.save(Mockito.any())).thenReturn(invoice);
        Invoice response = invoicePostingRepositoryService.updateInvoiceSubStatus(invoice, InvoicePostingSubStatus.CREATED);
        Assertions.assertEquals(InvoicePostingSubStatus.CREATED, response.getInvoiceSubStatus());
    }

    @Test
    public void updateInvoiceNexsDetails() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepository.save(Mockito.any())).thenReturn(invoice);
        Invoice response = invoicePostingRepositoryService.updateInvoiceNexsDetails(invoice, "100.0000");
        Assertions.assertEquals("100.0000", response.getNexsInvoiceAmount().toString());
    }

    @Test
    public void updateInvoiceState() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("I1")
                .poNumber("P1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepository.save(Mockito.any())).thenReturn(invoice);
        Invoice response = invoicePostingRepositoryService.updateInvoiceState(invoice, InvoicePostingSubStatus.INVOICE_POSTED_SUCCESS.getErrorMessage(), InvoicePostingSubStatus.INVOICE_POSTED_SUCCESS);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_POSTED_SUCCESS, response.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_POSTED_SUCCESS.getErrorMessage(), response.getErrorMessage());
    }
}

