package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.core.model.*;
import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.config.AppConfig;
import com.lenskart.financeConsumer.dao.*;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnCreateRequest;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.InventoryCorrectionWriteRepository;
import com.lenskart.financeConsumer.model.OrderItemGSTDetail;
import com.lenskart.financeConsumer.model.financeDb.InventoryCorrection;
import com.lenskart.financeConsumer.service.*;
import com.lenskart.financeConsumer.service.InventoryCorrectionService;
import com.lenskart.financeConsumer.service.Return.ReturnOrderItemService;
import com.lenskart.financeConsumer.util.*;
import com.lenskart.inventoryadapter.client.InventoryAdapterClient;
import com.lenskart.orderops.model.ReturnOrderItem;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.Mock;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;

@ExtendWith(MockitoExtension.class)
class ReturnOrderServiceImplTest {

    @Spy
    @InjectMocks
    ReturnOrderServiceImpl returnOrderService;

    @Mock
    D365ReturnTrackingRepository d365ReturnTrackingRepository;
    @Mock
    InventoryCorrectionService inventoryCorrectionService;
    @Mock
    UwOrdersReadRepositoryFinance uwOrdersReadRepositoryFinance;
    @Mock
    ReturnOrderItemRepository returnOrderItemRepository;
    @Mock
    private KafkaTemplate kafkaProducerTemplate;
    @Mock
    private OrdersHeaderRepository ordersHeaderRepository;
    @Mock
    ReturnUtil returnUtil;
    @Mock
    ObjectMapper objectMapper;
    @Mock
    FinanceSystemSyncRepository financeSystemSyncRepository;
    @Mock
    UwOrdersRepository uwOrdersRepository;

    @Mock
    InventoryAdapterClient inventoryAdapterClient;

    @Mock
    private SBRTOrderItemService sbrtOrderItemService;

    @Mock
    private D365ReturnService d365ReturnService;

    @Mock
    UwOrdersRepository uwOrderRepository;

    @Mock
    ReturnEInvoiceService returnEInvoiceService;

    @Mock
    HubMasterService hubMasterService;

    @Mock
    private NexsClient nexsClient;

    @Mock
    SystemPreferenceService systemPreferenceService;

    @Mock
    AppConfig appConfig;

    @Mock
    private CostAverageNewTempService costAverageNewTempService;

    @Mock
    private ItemWisePriceInfoDetails itemWisePriceInfoService;

    @Mock
    private ReturnOrderRepository returnOrderRepository;

    @Mock
    private ReturnOrderItemService returnOrderItemService;

    @Mock
    ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Mock
    private GenericClientService genericClientService;

    @Mock
    private OrderService orderService;

    @Mock
    private UwOrdersService uwOrdersService;

    @Mock
    private ProductService productService;

    @Mock
    private OrderAddressUpdateService orderAddressUpdateService;

    @Mock
    private ShippingStatusService shippingStatusService;

    @Mock
    private SaleOrderUtil saleOrderUtil;

    @Mock
    private OrderHeaderService orderHeaderService;

    @Mock
    private CourierAssignmentsService courierAssignmentsService;

    @Mock
    private OrderItemGstDetailsService orderItemGstDetailsService;

    @Mock
    private ClassificationService classificationService;

    @Mock
    private CourierWisePickingService courierWisePickingService;

    @Mock
    private S3InvoiceDetailsService s3InvoiceDetailsService;

    @Mock
    ReturnHistoryRepository returnHistoryRepository;


    @Mock
    SalesOrderService salesOrderService;

    @Mock
    ItemMasterService itemMasterService;

    @Mock
    PosUtils posUtils;

    @Mock
    Gson gson;

    @Mock
    private InventoryCorrectionWriteRepository inventoryCorrectionWriteRepository;
    public static Pattern barcodePattern = Pattern.compile("Serial number (\\S+)", Pattern.CASE_INSENSITIVE);


    @BeforeEach
    public void setup(){
        ReflectionTestUtils.setField(returnOrderService,"kafkaProducerTemplate",kafkaProducerTemplate);
        ReflectionTestUtils.setField(returnOrderService,"startHoursForReturnSerialNumberCron",1);
        ReflectionTestUtils.setField(returnOrderService,"serialNumberCorrectionTopic","topic");
        ReflectionTestUtils.setField(returnOrderService,"barcodePattern",barcodePattern);
        ReflectionTestUtils.setField(returnOrderService,"isSBRTFlowEnabledForSalesOrder",true);
        ReflectionTestUtils.setField(returnOrderService,"SBRTItemFlag","SBRT");
        ReflectionTestUtils.setField(returnOrderService,"nonSBRTItemFlag","NON_SBRT");
        ReflectionTestUtils.setField(returnOrderService,"dummyBarCodeSuffixValue","_1");
    }



    @Test
    public void generatePayloadAndPushToKafka() {

        ReturnOrderItem returnOrderItem =new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        returnOrderItem.setUwItemId(12);
        UwOrder uworder =new UwOrder();
        uworder.setProductId(1);
        OrdersHeader ordersHeader =new OrdersHeader();
        List<ReturnOrderItem>  returnOrderItemList =new ArrayList<>();
        D365ReturnTracking d365ReturnTracking =new D365ReturnTracking();
        d365ReturnTracking.setReturnId(1);
        d365ReturnTracking.setPslipSyncMessage("Serial number CCC050688737 already exists physically in the inventory..Operation has been cancelled.");



        Mockito.when(returnOrderItemRepository.findReturnItemListByReturnId(any())).thenReturn(Arrays.asList(returnOrderItem));
        Mockito.when(uwOrdersReadRepositoryFinance.findByUwItemId(any())).thenReturn(uworder);
        //  Mockito.when(ordersHeaderRepository.findByIncrementId(any())).thenReturn(ordersHeader);
//        Mockito.when(returnUtil.getSBRTFlag(any())).thenReturn(true);
        // Mockito.when(returnUtil.getPSlipLegalEntity(any(),any(),anyBoolean())).thenReturn("LKIN");
        //Mockito.when(kafkaProducerTemplate.send()).

        returnOrderService.generatePayloadAndPushToKafka( d365ReturnTracking );

    }

    @Test
    public void fetchD365BarcodeTestFwPackingSlipSuccessFul(){
        ReturnOrderItem returnOrderItem =new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        returnOrderItem.setUwItemId(12);
        UwOrder uworder =new UwOrder();
        uworder.setProductDeliveryType("DTC");
        uworder.setNavChannel("DTC");
        uworder.setProductId(1);
        uworder.setBarcode("AXXSR1");

        D365ReturnTracking d365ReturnTracking =new D365ReturnTracking();
        d365ReturnTracking.setReturnId(1);


        Mockito.when(returnUtil.getBarCode(any())).thenReturn("AXXSR1");
        Mockito.when(inventoryCorrectionWriteRepository.findByBarcode(any())).thenReturn(Arrays.asList(new InventoryCorrection()));

//        returnOrderService.fetchD365Barcode(uworder,false);
        assertEquals("AXXSR1",returnOrderService.fetchD365Barcode(uworder,true));
        Mockito.when(inventoryCorrectionWriteRepository.findByBarcode(any())).thenReturn(null); // not exiting entry in inventory corection
        assertEquals("AXXSR1",returnOrderService.fetchD365Barcode(uworder,true));

        InventoryCorrection inventoryCorrection =new InventoryCorrection();
        inventoryCorrection.setBarcode("AXXSR1");
        inventoryCorrection.setD365Barcode("AXXSR1_1");
        Mockito.when(inventoryCorrectionWriteRepository.findByBarcode(any())).thenReturn(Arrays.asList(inventoryCorrection));
        assertEquals("AXXSR1",returnOrderService.fetchD365Barcode(uworder,true));

    }
    @Test
    public void fetchD365BarcodeTestFwPackingSlipFailure(){
        ReturnOrderItem returnOrderItem =new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        returnOrderItem.setUwItemId(12);
        UwOrder uworder =new UwOrder();
        uworder.setProductDeliveryType("DTC");
        uworder.setNavChannel("DTC");
        uworder.setProductId(1);
        uworder.setBarcode("AXXSR1");

        D365ReturnTracking d365ReturnTracking =new D365ReturnTracking();
        d365ReturnTracking.setReturnId(1);


        Mockito.when(returnUtil.getBarCode(any())).thenReturn("AXXSR1");

        Mockito.when(inventoryCorrectionWriteRepository.findByBarcode(any())).thenReturn(null); // not exiting entry in inventory corection
        assertEquals("AXXSR1_1",returnOrderService.fetchD365Barcode(uworder,false));
    }

    @Test
    public void fetchIsFWPackingslipSyncSuccessFulTrue(){

        UwOrder uworder =new UwOrder();
        uworder.setProductDeliveryType("DTC");
        uworder.setNavChannel("DTC");
        uworder.setProductId(1);
        uworder.setBarcode("AXXSR1");

//        Mockito.when(returnUtil.b2BVirtualOrder(any())).thenReturn(false);

        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setD365SyncStatus("Success");
        Mockito.when(financeSystemSyncRepository.
                findByEventAndEntityTypeAndEntityIdAndFacilityCode(any(),any(),any(),any())).thenReturn(financeSourceSystemSync);
        assertTrue(returnOrderService.isFWPackingslipSyncSuccessFul(uworder));
    }
    @Test
    public void fetchIsFWPackingslipSyncSuccessFulFalse(){
        UwOrder uworder =new UwOrder();
        uworder.setProductDeliveryType("DTC");
        uworder.setNavChannel("DTC");
        uworder.setProductId(1);
        uworder.setBarcode("AXXSR1");


        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setD365SyncStatus("Success");
        Mockito.when(financeSystemSyncRepository.
                findByEventAndEntityTypeAndEntityIdAndFacilityCode(any(),any(),any(),any())).thenReturn(null);
        assertFalse(returnOrderService.isFWPackingslipSyncSuccessFul(uworder));


        uworder.setProductDeliveryType("B2B");
        uworder.setNavChannel("B2B");       // B2B order
        Mockito.when(returnUtil.b2BVirtualOrder(any())).thenReturn(true);

        Mockito.when(uwOrdersRepository.findByB2bRefrenceItemId(any())).thenReturn(uworder);

        Mockito.when(financeSystemSyncRepository.
                findByEventAndEntityTypeAndEntityIdAndFacilityCode(any(),any(),any(),any())).thenReturn(null);
        assertFalse(returnOrderService.isFWPackingslipSyncSuccessFul(uworder));

    }

    @Test
    public void syncReturnOrderByReturnId(){


        ReturnOrderItem returnOrderItem = new ReturnOrderItem();
        returnOrderItem.setUwItemId(1);
        returnOrderItem.setReturnId(1);

        Mockito.when(returnOrderItemRepository.findReturnItemListByReturnId(any())).thenReturn(Arrays.asList(returnOrderItem));

        returnOrderService.syncReturnOrderByReturnId(1);
        Mockito.verify(kafkaProducerTemplate, Mockito.atMost(1)).send(any(),any());

    }


    @Test
    public void syncReturnOrderForSerialNumberErrorViaCron() {
        ReturnOrderItem returnOrderItem = new ReturnOrderItem();
        returnOrderItem.setUwItemId(1);
        returnOrderItem.setReturnId(1);

        D365ReturnTracking d365ReturnTracking =new D365ReturnTracking();
        d365ReturnTracking.setReturnId(1);

        Mockito.when(d365ReturnTrackingRepository.fetchFailedReturnIdsForPackingslip(any(),any())).thenReturn(Arrays.asList(d365ReturnTracking));
        Mockito.when(returnOrderItemRepository.findReturnItemListByReturnId(any())).thenReturn(Arrays.asList(returnOrderItem));

        returnOrderService.syncReturnOrderForSerialNumberErrorViaCron();
        Mockito.verify(kafkaProducerTemplate, Mockito.atMost(1)).send(any(),any(),any());
    }


    @Test
    public void syncReturnOrdersByReturnIdsTest(){

        Mockito.doNothing().when(returnOrderService).syncReturnOrderByReturnId(anyInt());
        returnOrderService.syncReturnOrdersByReturnIds(Arrays.asList(1));


        Mockito.doThrow(new NullPointerException()).when(returnOrderService).syncReturnOrderByReturnId(anyInt());
        returnOrderService.syncReturnOrdersByReturnIds(Arrays.asList(1));


        returnOrderService.syncReturnOrdersByReturnIds(null);

    }

    @Test
    void testGeneratePayloadForReturn() throws Exception {
        UwOrder mockUwOrder = new UwOrder();
        mockUwOrder.setUwItemId(12);
        mockUwOrder.setProductDeliveryType(Constants.Common.OTC);

        UwOrder mockUwOrder2 = new UwOrder();
        mockUwOrder2.setUwItemId(125);
        mockUwOrder2.setProductDeliveryType(Constants.Common.OTC);
        SalesOrderHeader salesOrderHeader = null;

        ReturnCreateRequest returnCreateRequest = new ReturnCreateRequest();
        returnCreateRequest.setReturnId(1);
        returnCreateRequest.setUwItemId(12);
        returnCreateRequest.setUwOrder(mockUwOrder);

        Order mockOrder = new Order();
        mockOrder.setIncrementId(123);
        mockOrder.setProductDeliveryType(Constants.Common.DTC);

        OrdersHeader mockOrderHeader = new OrdersHeader();
        mockOrderHeader.setLkCountry("IN");

        ReturnOrderItem mockReturnOrderItem = new ReturnOrderItem();
        mockReturnOrderItem.setReturnId(1);

        Product mockProduct = new Product();
        mockProduct.setClassification(123);
        mockProduct.setBrand("Lens");

        ProductUtil.CountryCurrencyMapping = new HashMap<>();
        ProductUtil.CountryCurrencyMapping.put("IN", "INR");

        Mockito.when(orderService.findOrderByItemId(any())).thenReturn(mockOrder);
        Mockito.when(orderHeaderService.getOrderHeader(any())).thenReturn(mockOrderHeader);
        Mockito.when(productService.getProduct((Integer) any())).thenReturn(mockProduct);
        Mockito.when(returnOrderItemService.findByUwItemId(12)).thenReturn(mockReturnOrderItem);
        Mockito.when(orderItemGstDetailsService.getOrderItemGSTDetail(any())).thenReturn(new OrderItemGSTDetail());
        Mockito.when(classificationService.getClassification(any())).thenReturn(new Classification());
        Mockito.when(returnUtil.getSalesChannel(any(),any(),any())).thenReturn("testSalesChannel");
        Mockito.when(saleOrderUtil.getSubChannel(any(),any())).thenReturn("testSubChannel");
        Mockito.when(uwOrderRepository.findUwItemIdByParentUwItemId(12)).thenReturn(Collections.singletonList(125));
        Mockito.when(uwOrderRepository.findByUwItemId(12)).thenReturn(mockUwOrder);
        Mockito.when(uwOrderRepository.findByUwItemId(125)).thenReturn(mockUwOrder2);
        Mockito.when(sbrtOrderItemService.fetchUwItemIdsFromSBRTOrderItem(any())).thenReturn(Collections.singletonList(12));
        salesOrderHeader = returnOrderService.generatePayloadForReturn(salesOrderHeader, returnCreateRequest);
        Assert.assertEquals("SBRT", salesOrderHeader.soLines.get(0).getUnits());
        Assert.assertEquals("NON_SBRT", salesOrderHeader.soLines.get(1).getUnits());
    }


}