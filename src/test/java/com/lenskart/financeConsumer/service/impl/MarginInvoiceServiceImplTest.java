package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.dto.MarginSyncDTO;
import com.lenskart.financeConsumer.pos.writeRepository.MarginRepository;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.util.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MarginInvoiceServiceImplTest {

    @InjectMocks
    private MarginInvoiceServiceImpl marginInvoiceService;

    @Mock
    private GenericClientService genericClientService;

    @Mock
    private MarginRepository marginRepository;

    @Value("${financeAdapter.url}")
    private String financeAdapterUrl;

    @BeforeEach
    void setUp() {
        financeAdapterUrl = "http://localhost:8080";
    }

    @Test
    void testD365MarginSync_Success() throws Exception {
        MarginSyncDTO marginSyncDTO = new MarginSyncDTO();
        marginSyncDTO.setSalesOrderNumber("12345");

        HashMap<String, Object> responseMap = new HashMap<>();
        responseMap.put("Message", "Success");
        responseMap.put("Success", "true");

        ResponseEntity<HashMap<String, Object>> responseEntity = ResponseEntity.ok(responseMap);

        when(genericClientService.forwardRequest(anyString(), any(HttpHeaders.class), any(HttpMethod.class), any(MarginSyncDTO.class)))
                .thenReturn(responseEntity);

        marginInvoiceService.d365MarginSync(marginSyncDTO);

        verify(marginRepository, times(1)).updateD365SyncStatusWithResponse(
                eq("12345"),
                eq(Constants.POSD365SyncStatus.SYNCED),
                eq("Success")
        );
    }

    @Test
    void testD365MarginSync_Failure() throws Exception {
        MarginSyncDTO marginSyncDTO = new MarginSyncDTO();
        marginSyncDTO.setSalesOrderNumber("12345");

        HashMap<String, Object> responseMap = new HashMap<>();
        responseMap.put("Message", "Failure");
        responseMap.put("Success", "false");

        ResponseEntity<HashMap<String, Object>> responseEntity = ResponseEntity.ok(responseMap);

        when(genericClientService.forwardRequest(anyString(), any(HttpHeaders.class), any(HttpMethod.class), any(MarginSyncDTO.class)))
                .thenReturn(responseEntity);

        marginInvoiceService.d365MarginSync(marginSyncDTO);

        verify(marginRepository, times(1)).updateD365SyncStatusWithResponse(
                eq("12345"),
                eq(Constants.POSD365SyncStatus.NOT_SYNCED),
                eq("Failure")
        );
    }

    @Test
    void testD365MarginSync_Exception() throws Exception {
        MarginSyncDTO marginSyncDTO = new MarginSyncDTO();
        marginSyncDTO.setSalesOrderNumber("12345");

        when(genericClientService.forwardRequest(anyString(), any(HttpHeaders.class), any(HttpMethod.class), any(MarginSyncDTO.class)))
                .thenThrow(new RuntimeException("Service unavailable"));

        marginInvoiceService.d365MarginSync(marginSyncDTO);

        verify(marginRepository, times(1)).updateD365SyncStatusWithResponse(
                eq("12345"),
                eq(Constants.POSD365SyncStatus.NOT_SYNCED),
                eq("Service unavailable")
        );
    }
}
