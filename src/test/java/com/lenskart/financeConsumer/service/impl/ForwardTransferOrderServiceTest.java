package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.OrderHeadersMeta;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dto.d365requests.CostPriceResponseBody;
import com.lenskart.financeConsumer.dto.d365requests.InvoiceDetailsResponse;
import com.lenskart.financeConsumer.dto.d365requests.TransferOrderFinanceSourceRequestDto;
import com.lenskart.financeConsumer.dto.d365requests.forward.TransferOrderDTO.TransferOrderHeader;
import com.lenskart.financeConsumer.dto.d365requests.forward.TransferOrderDTO.TransferOrderLines;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.TransferJournalHeaderRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.TransferOrderHeaderRepository;
import com.lenskart.financeConsumer.model.OrderItemGSTDetail;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.HubMasterService;
import com.lenskart.financeConsumer.service.OrderHeaderService;
import com.lenskart.financeConsumer.service.OrderItemGstDetailsService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.service.ShippingStatusService;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.ForwardTransferUtil;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import org.apache.kafka.common.KafkaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ForwardTransferOrderServiceTest {

    @Mock
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @Mock
    private KafkaTemplate kafkaProducerTemplate;

    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private TransferOrderHeaderRepository transferOrderHeaderRepository;
    @Spy
    @InjectMocks
    private ForwardTransferOrderService forwardTransferOrderService;

    @Mock
    private OrderHeaderService orderHeaderService;
    @Mock
    private UwOrdersReadRepositoryFinance uwOrdersReadRepositoryFinance;
    @Mock
    private ShippingStatusService shippingStatusService;
    @Mock
    private ProductUtil productUtil;
    @Mock
    private ProductService productService;
    @Mock
    private OrderItemGstDetailsService orderItemGstDetailsService;
    @Mock
    private GenericClientService genericClientService;
    @Mock
    private HubMasterService hubMasterService;

    @Mock
    private DateUtils dateUtils;

    //  @Mock
  //  private InventoryAdaptorConnectorImpl inventoryAdaptorConnector;
    @Mock
    NexsClient nexsClient;
    @Mock
    private ForwardTransferUtil forwardTransferUtil;

    @BeforeEach
    public void setup(){
        ReflectionTestUtils.setField(forwardTransferOrderService,"subListBatchSize",1);
        ReflectionTestUtils.setField(forwardTransferOrderService,"saleOrderConsumerTopic","topic");
        ReflectionTestUtils.setField(forwardTransferOrderService,"financeAdapterUrl","hello.com");
        ReflectionTestUtils.setField(forwardTransferOrderService,"isTransferOrderSyncBlocked",false);
    }


    @Test
    void syncTransferOrdersForFinanceSourceIds() {
        TransferOrderFinanceSourceRequestDto transferOrderFinanceSourceRequestDto = new TransferOrderFinanceSourceRequestDto();
        transferOrderFinanceSourceRequestDto.setFinanceSourceSystemIds(Arrays.asList(1L));

        FinanceSourceSystemSync financeSourceSystemSync = new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");

        Mockito.when(financeSystemSyncRepository.findByIdIn(anyList())).thenReturn(Arrays.asList(financeSourceSystemSync));
        forwardTransferOrderService.syncTransferOrdersForFinanceSourceIds(transferOrderFinanceSourceRequestDto);
        verify(kafkaProducerTemplate, times(1)).send(anyString(), anyString());
    }

    @Test
    void syncTransferOrdersForFinanceSourceIdsEmptyList() {
        TransferOrderFinanceSourceRequestDto transferOrderFinanceSourceRequestDto = new TransferOrderFinanceSourceRequestDto();
        transferOrderFinanceSourceRequestDto.setFinanceSourceSystemIds(Arrays.asList(1L));

        Mockito.when(financeSystemSyncRepository.findByIdIn(anyList())).thenReturn(Arrays.asList());
        forwardTransferOrderService.syncTransferOrdersForFinanceSourceIds(transferOrderFinanceSourceRequestDto);
        verify(kafkaProducerTemplate, times(0)).send(anyString(), anyString());
    }


    @Test
    void syncTransferOrdersForFinanceSourceIdsWithListSmallerThanBatchSize() {

        ReflectionTestUtils.setField(forwardTransferOrderService,"subListBatchSize",5);

        TransferOrderFinanceSourceRequestDto transferOrderFinanceSourceRequestDto = new TransferOrderFinanceSourceRequestDto();
        transferOrderFinanceSourceRequestDto.setFinanceSourceSystemIds(Arrays.asList(1L));

        Mockito.when(financeSystemSyncRepository.findByIdIn(anyList())).thenReturn(Arrays.asList());
        forwardTransferOrderService.syncTransferOrdersForFinanceSourceIds(transferOrderFinanceSourceRequestDto);

        verify(kafkaProducerTemplate, times(0)).send(anyString(), anyString());
        verify(financeSystemSyncRepository, times(1)).findByIdIn(anyList());
    }

    @Test
    void syncTransferOrdersForFinanceSourceIdsKafkaSendException() {
        TransferOrderFinanceSourceRequestDto transferOrderFinanceSourceRequestDto = new TransferOrderFinanceSourceRequestDto();
        transferOrderFinanceSourceRequestDto.setFinanceSourceSystemIds(Arrays.asList(1L));

        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");

        Mockito.when(financeSystemSyncRepository.findByIdIn(anyList())).thenReturn(Arrays.asList(financeSourceSystemSync));
        Mockito.when(kafkaProducerTemplate.send(anyString(),anyString())).
                thenThrow(new KafkaException("Producer closed while send in progress"));
        forwardTransferOrderService.syncTransferOrdersForFinanceSourceIds(transferOrderFinanceSourceRequestDto);
        verify(kafkaProducerTemplate,times(1)).send(anyString(),anyString());
    }




    @Test
    void isValidTransferOrderTrue() {


        OrderHeadersMeta  orderHeadersMeta =new OrderHeadersMeta();
        orderHeadersMeta.setEventKey("is_sbrt");
        orderHeadersMeta.setEventValue("true");
        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");
        financeSourceSystemSync.setEvent(FinanceSourceSystemSyncEvent.TRANSFER_ORDER);


        assertTrue(forwardTransferOrderService.isValidTransferOrder(financeSourceSystemSync));
    }
    @Test
    void isValidTransferOrderFalse() {


        OrderHeadersMeta  orderHeadersMeta =new OrderHeadersMeta();
        orderHeadersMeta.setEventKey("is_sbrt");
        orderHeadersMeta.setEventValue("false");
        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");
        financeSourceSystemSync.setEvent(FinanceSourceSystemSyncEvent.SALE_ORDER);

        //when(ordersHeaderMetaReadRepository.findByOrderIdAndEventKeyAndEventName(anyInt(),any(),anyString())).thenReturn((orderHeadersMeta));

        assertFalse(forwardTransferOrderService.isValidTransferOrder(financeSourceSystemSync));
    }



    @Test
    void createTransferOrderForShippingIdAndFacilityCodeInvalidRequest() throws Exception {

        String shipmentId ="HXS001", facilityCode ="NXS01";

        UwOrder uwOrder=new UwOrder();
        uwOrder.setIncrementId(122);
        OrdersHeader ordersHeader=new OrdersHeader();
        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");


        when(financeSystemSyncRepository.findByEntityIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(financeSourceSystemSync));


        assertThrows( InvalidRequestException.class,()-> forwardTransferOrderService.createTransferOrderForShippingIdAndFacilityCode(shipmentId,facilityCode));
    }

    @Test
    void createTransferOrderForShippingIdAndFacilityCodeValidRequest() throws Exception {

        String shipmentId ="HXS001", facilityCode ="NXS01";

        UwOrder uwOrder=new UwOrder();
        uwOrder.setIncrementId(122);
        OrdersHeader ordersHeader=new OrdersHeader();
        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");
        financeSourceSystemSync.setEvent(FinanceSourceSystemSyncEvent.TRANSFER_ORDER);


          when(productService.fetchProductsFromProductIds(anyList())).thenReturn(Arrays.asList(new Product()));
        OrderItemGSTDetail  orderItemGSTDetail =new OrderItemGSTDetail();
        orderItemGSTDetail.setCgstPc(1.0);
        orderItemGSTDetail.setIgstPc(1.0);
        orderItemGSTDetail.setSgstPc(1.0);
        orderItemGSTDetail.setUgstPc(1.0);
        HashMap responseMap =new HashMap();
        responseMap.put("Message","sycned successfully");
        responseMap.put("Success",true);

        InvoiceDetailsResponse invoiceDetailsResponse =new InvoiceDetailsResponse();
        invoiceDetailsResponse.setInvoice(new InvoiceDetailsResponse.Invoice());
        ArrayList<InvoiceDetailsResponse.Invoice> invoices =new ArrayList<>();
        InvoiceDetailsResponse.Invoice invoice = new InvoiceDetailsResponse.Invoice();
        invoice.setCreated("1333444444");
        invoices.add(invoice);
        invoiceDetailsResponse.setInvoices(invoices);
        when(forwardTransferUtil.getLegalEntity(any(),any())).thenReturn("LKIN");
        when(nexsClient.getCostPrice(any(),any())).thenReturn(Arrays.asList(new CostPriceResponseBody()));

        doReturn(true).when(forwardTransferOrderService).isValidTransferOrder(any());
        when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(uwOrder));
        when(orderHeaderService.getOrderHeader(anyInt())).thenReturn((ordersHeader));
        when(financeSystemSyncRepository.findByEntityIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(financeSourceSystemSync));

        when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(uwOrder));

        when(shippingStatusService.getShippingStatusByUnicomCode(anyString())).thenReturn(new ShippingStatus());


        when(orderItemGstDetailsService.fetchOrderItemGSTDetailsFromUwItemIds(anyList())).thenReturn(Arrays.asList(orderItemGSTDetail));
        ResponseEntity responseEntity =new ResponseEntity(responseMap , HttpStatus.OK);
        when(genericClientService.forwardRequest(anyString(),any(),any(),any())).thenReturn(responseEntity);

        ResponseEntity resultResponseEntity= forwardTransferOrderService.createTransferOrderForShippingIdAndFacilityCode(shipmentId,facilityCode);
        HashMap resultResponse = (HashMap) resultResponseEntity.getBody();

        assertEquals("sycned successfully",resultResponse.get("Message"));

    }

    @Test
    void createTransferOrderForShippingIdAndFacilityCodeValidRequestTimeout408Response() throws Exception {

        String shipmentId ="HXS001", facilityCode ="NXS01";

        UwOrder uwOrder=new UwOrder();
        uwOrder.setIncrementId(122);
        OrdersHeader ordersHeader=new OrdersHeader();
        FinanceSourceSystemSync financeSourceSystemSync =new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setPayload("[20222]");
        financeSourceSystemSync.setEntityId("SDSSDVFVV");
        financeSourceSystemSync.setFacilityCode("NXS1");
        financeSourceSystemSync.setEvent(FinanceSourceSystemSyncEvent.TRANSFER_ORDER);


        when(productService.fetchProductsFromProductIds(anyList())).thenReturn(Arrays.asList(new Product()));
        OrderItemGSTDetail  orderItemGSTDetail =new OrderItemGSTDetail();
        orderItemGSTDetail.setCgstPc(1.0);
        orderItemGSTDetail.setIgstPc(1.0);
        orderItemGSTDetail.setSgstPc(1.0);
        orderItemGSTDetail.setUgstPc(1.0);
        HashMap responseMap =new HashMap();
        responseMap.put("Message","sycned successfully");
        responseMap.put("Success",true);

        InvoiceDetailsResponse invoiceDetailsResponse =new InvoiceDetailsResponse();
        invoiceDetailsResponse.setInvoice(new InvoiceDetailsResponse.Invoice());

        when(forwardTransferUtil.getLegalEntity(any(),any())).thenReturn("LKIN");
        when(nexsClient.getCostPrice(any(),any())).thenReturn(Arrays.asList(new CostPriceResponseBody()));

        doReturn(true).when(forwardTransferOrderService).isValidTransferOrder(any());
        when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(uwOrder));
        when(orderHeaderService.getOrderHeader(anyInt())).thenReturn((ordersHeader));
        when(financeSystemSyncRepository.findByEntityIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(financeSourceSystemSync));

        when(uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(anyString(),anyString())).thenReturn(Arrays.asList(uwOrder));

        when(shippingStatusService.getShippingStatusByUnicomCode(anyString())).thenReturn(new ShippingStatus());


        when(orderItemGstDetailsService.fetchOrderItemGSTDetailsFromUwItemIds(anyList())).thenReturn(Arrays.asList(orderItemGSTDetail));
        ResponseEntity responseEntity =new ResponseEntity(responseMap , HttpStatus.REQUEST_TIMEOUT);
        when(genericClientService.forwardRequest(anyString(),any(),any(),any())).thenReturn(responseEntity);

        ResponseEntity resultResponseEntity= forwardTransferOrderService.createTransferOrderForShippingIdAndFacilityCode(shipmentId,facilityCode);
     //   HashMap resultResponse = (HashMap) resultResponseEntity.getBody();

        assertEquals("Some error occured while syncing TransferOrder to D365",resultResponseEntity.getBody().toString());

    }

    @Test
    void persistTransferOrderHeader() throws JsonProcessingException {
        TransferOrderHeader transferOrderHeader=new TransferOrderHeader();

        TransferOrderLines transferOrderLines =new TransferOrderLines();
        transferOrderLines.setLineNumber("1");
        transferOrderLines.setTransferQuantity("1");
        transferOrderLines.setTaxRateType("1");
        transferOrderLines.setUnitPrice("1");


        transferOrderHeader.setTransferOrderLines(Arrays.asList(transferOrderLines));

        when(objectMapper.readValue(anyString(),any(Class.class))).thenReturn(transferOrderHeader);


        try (MockedStatic<DateUtils> dateUtils = Mockito.mockStatic(DateUtils.class)) {
            when(DateUtils.getDateFromString(any())).thenReturn(LocalDateTime.now());

            forwardTransferOrderService.persistTransferOrderHeader("",1,"SHIPX");

            verify(transferOrderHeaderRepository,times(1)).save(any());
        }

    }

}