package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.dto.d365requests.D365ResponseDto;
import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.D365SyncStatusResponseDTO;
import com.lenskart.financeConsumer.model.enums.InvoicePostingStatus;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.InvoiceClosureResponseDTO;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.InvoiceValidationDto;
import com.lenskart.financeConsumer.model.enums.InvoicePostingSubStatus;
import com.lenskart.financeConsumer.model.financeDb.Invoice;
import com.lenskart.financeConsumer.service.InvoicePostingRepositoryService;
import com.lenskart.financeConsumer.service.InvoicePostingService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;

import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvoicePostingNexsServiceImplTest {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static InvoiceValidationDto invoiceValidationDto;
    private static Invoice invoice;
    @InjectMocks
    InvoicePostingNexsServiceImpl invoicePostingNexsService;
    @Mock
    InvoicePostingRepositoryService invoicePostingRepositoryService;
    @Mock
    InvoicePostingService invoicePostingService;

    @Mock
    NexsClient nexsClient;

    @BeforeEach
    public void setUp() throws IOException {
        File file = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/invoiceValidationDto.json");
        invoiceValidationDto = new ObjectMapper().readValue(file, InvoiceValidationDto.class);
        invoice = new Invoice();
    }

    @Test
    void processThreeWayValidation() throws IOException {
        ReflectionTestUtils.setField(invoicePostingNexsService, "amountMatchTolerance", BigDecimal.valueOf(40));
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().data(invoiceValidationDto).build();
        Mockito.when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber("9876")).thenReturn(invoice);
        Mockito.when(invoicePostingService.syncVendorInvoice(ArgumentMatchers.any())).thenReturn(D365ResponseDto.builder()
                                                                                                         .errorMessage("Success")
                                                                                                         .d365SyncStatus("SUCCESS")
                                                                                                         .build());
        Mockito.when(invoicePostingRepositoryService.updateInvoiceState(ArgumentMatchers.any(),ArgumentMatchers.anyString(),ArgumentMatchers.any())).thenReturn(invoice);
        invoicePostingNexsService.processThreeWayValidation(genericResponseDTO);
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).updateInvoiceState(invoice,
                                                                                             InvoicePostingSubStatus.NEXS_3_WAY_MATCH_SUCCESS.getErrorMessage(),
                                                                                             InvoicePostingSubStatus.NEXS_3_WAY_MATCH_SUCCESS);
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).updateInvoiceState(invoice,
                                                                                             "Success",
                                                                                             InvoicePostingSubStatus.D365_3_WAY_MATCH_SUCCESS);
    }

    @Test
    void processThreeWayValidationAmtMismatch() throws IOException {
        ReflectionTestUtils.setField(invoicePostingNexsService, "amountMatchTolerance", BigDecimal.valueOf(0));
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().data(invoiceValidationDto).build();
        Mockito.when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber("9876")).thenReturn(invoice);
        invoicePostingNexsService.processThreeWayValidation(genericResponseDTO);
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).updateInvoiceState(invoice,
                                                                                             "NEXS 3 Way match is unsuccessful Nexs amount : 200.0000 and grn/debit amount 160.0000 mismatch",
                                                                                             InvoicePostingSubStatus.NEXS_3_WAY_MATCH_FAILED);
    }

    @Test
    void processThreeWayValidationQtyMismatch() throws IOException {
        invoiceValidationDto.setInvoiceTotalQty(200);
        GenericResponseDto genericResponseDTO = GenericResponseDto.builder().data(invoiceValidationDto).build();
        Mockito.when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber("9876")).thenReturn(invoice);
        invoicePostingNexsService.processThreeWayValidation(genericResponseDTO);
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).updateInvoiceState(invoice,
                                                                                             "NEXS 3 Way match is unsuccessful Nexs quantity : 200 and grn/debit quantity 16 mismatch",
                                                                                             InvoicePostingSubStatus.NEXS_3_WAY_MATCH_FAILED);
    }


    @Test
    void consumeInvoiceClosureTest_Success_EqualAmount() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseSuccess.json");

        ReflectionTestUtils.setField(invoicePostingNexsService, "amountMatchTolerance", BigDecimal.valueOf(40));
        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceAmount(new BigDecimal("200.0000"));
        invoice.setVendorInvoiceQuantity(10);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_AMOUNT_MATCH);
            updatedInvoice.setErrorMessage(null);
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_AMOUNT_MATCH, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingStatus.NEXS_CLOSED, invoice.getInvoiceStatus());
        Assertions.assertNull(invoice.getErrorMessage());
    }

    @Test
    void consumeInvoiceClosureTest_Success_OutsideTolerance() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseSuccess3.json");

        ReflectionTestUtils.setField(invoicePostingNexsService, "amountMatchTolerance", BigDecimal.valueOf(40));
        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceAmount(new BigDecimal("200.0000"));
        invoice.setVendorInvoiceQuantity(10);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH);
            updatedInvoice.setErrorMessage(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH.getFormattedMessage(invoice.getVendorInvoiceAmount(), invoice.getNexsInvoiceAmount()));
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH.getFormattedMessage(invoice.getVendorInvoiceAmount(), invoice.getNexsInvoiceAmount()), invoice.getErrorMessage());
    }

    @Test
    void consumeInvoiceClosureTest_Success_WithinTolerance() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseSuccess2.json");

        ReflectionTestUtils.setField(invoicePostingNexsService, "amountMatchTolerance", BigDecimal.valueOf(40));
        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceAmount(new BigDecimal("200.0000"));
        invoice.setVendorInvoiceQuantity(10);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_AMOUNT_MATCH);
            updatedInvoice.setErrorMessage(null);
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_AMOUNT_MATCH, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingStatus.NEXS_CLOSED, invoice.getInvoiceStatus());
        Assertions.assertNull(invoice.getErrorMessage());
    }

    @Test
    void consumeInvoiceClosureTest_AmountMismatch() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseSuccess.json");

        ReflectionTestUtils.setField(invoicePostingNexsService, "amountMatchTolerance", BigDecimal.valueOf(40));
        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceAmount(new BigDecimal("320.000"));
        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH);
            updatedInvoice.setErrorMessage(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH.getErrorMessage());
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_AMOUNT_MISMATCH.getErrorMessage(), invoice.getErrorMessage());
    }

    @Test
    void consumeInvoiceClosureTest_QtyMismatch() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseSuccess.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_QTY_MISMATCH);
            updatedInvoice.setErrorMessage(InvoicePostingSubStatus.INVOICE_QTY_MISMATCH.getErrorMessage());
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_QTY_MISMATCH, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_QTY_MISMATCH.getErrorMessage(), invoice.getErrorMessage());
    }


    @Test
    void consumeInvoiceClosureTest_InvoiceNumberNotExists() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseFailed1.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_NUMBER_NOT_EXIST);
            updatedInvoice.setErrorMessage(InvoicePostingSubStatus.INVOICE_NUMBER_NOT_EXIST.getErrorMessage());
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_NUMBER_NOT_EXIST, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_NUMBER_NOT_EXIST.getErrorMessage(), invoice.getErrorMessage());
    }


    @Test
    void consumeInvoiceClosureTest_InvoiceNumberPoNumberMismatch() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseFailed2.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_PO_NUMBER_MISMATCH);
            updatedInvoice.setErrorMessage(InvoicePostingSubStatus.INVOICE_PO_NUMBER_MISMATCH.getErrorMessage());
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_PO_NUMBER_MISMATCH, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_PO_NUMBER_MISMATCH.getErrorMessage(), invoice.getErrorMessage());
    }

    @Test
    void consumeInvoiceClosureTest_InvoiceNotClosed() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseFailed3.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_NOT_CLOSED);
            updatedInvoice.setErrorMessage(InvoicePostingSubStatus.INVOICE_NOT_CLOSED.getErrorMessage());
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_NOT_CLOSED, invoice.getInvoiceSubStatus());
        Assertions.assertEquals(InvoicePostingSubStatus.INVOICE_NOT_CLOSED.getErrorMessage(), invoice.getErrorMessage());
    }


    @Test
    void consumeInvoiceClosureTest_InvoiceNumberNotFoundInRequest() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceClosureGenericResponseFailed4.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);

        GenericResponseDto<InvoiceClosureResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<InvoiceClosureResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setErrorMessage("Vendor invoice number not found in request");
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processInvoiceClosure(invoice, genericResponseDTO);
        Assertions.assertEquals(InvoicePostingSubStatus.CREATED, invoice.getInvoiceSubStatus());
        Assertions.assertEquals("Vendor invoice number not found in request", invoice.getErrorMessage());
    }

    @Test
    void processD365StbcStatusTest_Success() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceD365SyncStatusResponseSuccess.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);
        GenericResponseDto<D365SyncStatusResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<D365SyncStatusResponseDTO>>() {
        });

        when(invoicePostingRepositoryService.saveInvoice(Mockito.any())).thenReturn(invoice);
        invoicePostingNexsService.processD365SyncStatus(invoice, genericResponseDTO);
    }


    @Test
    void processD365StbcStatusTest_FailedPO() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceD365SyncStatusResponseFailedPO.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);
        GenericResponseDto<D365SyncStatusResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<D365SyncStatusResponseDTO>>() {
        });

        when(invoicePostingRepositoryService.saveInvoice(Mockito.any())).thenReturn(invoice);
        invoicePostingNexsService.processD365SyncStatus(invoice, genericResponseDTO);
    }


    @Test
    void processD365StbcStatusTest_FailedGRN() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceD365SyncStatusResponseFailedGRN.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);
        GenericResponseDto<D365SyncStatusResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<D365SyncStatusResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setErrorMessage("GRN syncing unsuccessfulPG0QNXS-25-00977");
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        when(invoicePostingRepositoryService.saveInvoice(Mockito.any())).thenReturn(invoice);
        invoicePostingNexsService.processD365SyncStatus(invoice, genericResponseDTO);
        Assertions.assertEquals("GRN syncing unsuccessfulPG0QNXS-25-00977", invoice.getErrorMessage());
    }


    @Test
    void processD365StbcStatusTest_FailedDN() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceD365SyncStatusResponseFailedDN.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);
        GenericResponseDto<D365SyncStatusResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<D365SyncStatusResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setErrorMessage("Debit note syncing unsuccessfulDNQNXS-25-000228,DNQNXS-25-000228");
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        when(invoicePostingRepositoryService.saveInvoice(Mockito.any())).thenReturn(invoice);
        invoicePostingNexsService.processD365SyncStatus(invoice, genericResponseDTO);
        Assertions.assertEquals("Debit note syncing unsuccessfulDNQNXS-25-000228,DNQNXS-25-000228", invoice.getErrorMessage());
    }


    @Test
    void processD365StbcStatusTest_Failed() throws IOException {
        File invoiceFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/Invoice.json");
        File invoiceClosureGenericResponseFile = new File("src/test/java/com/lenskart/financeConsumer/config/invoicePosting/InvoiceD365SyncStatusResponseFailed.json");

        Invoice invoice = objectMapper.readValue(invoiceFile, Invoice.class);
        invoice.setVendorInvoiceQuantity(1);
        GenericResponseDto<D365SyncStatusResponseDTO> genericResponseDTO = objectMapper.readValue(invoiceClosureGenericResponseFile, new TypeReference<GenericResponseDto<D365SyncStatusResponseDTO>>() {
        });

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            Invoice updatedInvoice = (Invoice) args[0];
            updatedInvoice.setErrorMessage("Error while receiving d365 sync status");
            return updatedInvoice;
        }).when(invoicePostingRepositoryService).updateInvoiceState(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());

        invoicePostingNexsService.processD365SyncStatus(invoice, genericResponseDTO);
        Assertions.assertEquals("Error while receiving d365 sync status", invoice.getErrorMessage());
    }

}