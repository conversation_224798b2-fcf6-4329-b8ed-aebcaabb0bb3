package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.dto.d365requests.CostPriceResponseBody;
import com.lenskart.financeConsumer.model.financeDb.Inventory;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class MovementJournalServiceImplTest {

    @Mock
    NexsClient nexsClient;

    @InjectMocks
    MovementJournalServiceImpl movementJournalService;

    @Test
    @SneakyThrows
    void updateCostPriceDetails() {
        CostPriceResponseBody costPriceResponseBody = new CostPriceResponseBody();
        costPriceResponseBody.setPrice("10");
        List<CostPriceResponseBody> costPriceResponseBodyList = Collections.singletonList(costPriceResponseBody);
        Inventory inventory = new Inventory();
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                costPriceResponseBodyList);
        movementJournalService.updateCostPriceDetails(inventory);
        Assertions.assertEquals(10D, inventory.getCostPrice());
    }

    @Test
    @SneakyThrows
    void updateCostPriceDetailsException() {
        Inventory inventory = new Inventory();
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(),
                                             ArgumentMatchers.any())).thenThrow(new RuntimeException("Test exception"));
        RuntimeException thrown = assertThrows(
                RuntimeException.class,
                () -> movementJournalService.updateCostPriceDetails(inventory),
                "Test exception"
        );
        Assertions.assertEquals("Test exception", thrown.getMessage());
    }
    @Test
    @SneakyThrows
    void updateCostPriceDetailsEmptyList() {
        Inventory inventory = new Inventory();
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                Collections.EMPTY_LIST);
        movementJournalService.updateCostPriceDetails(inventory);
        Assertions.assertEquals(null, inventory.getCostPrice());
    }
}