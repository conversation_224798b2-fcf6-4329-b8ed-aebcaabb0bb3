//package com.lenskart.financeConsumer.service.impl;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.lenskart.financeConsumer.clients.UnicomClient;
//import com.lenskart.financeConsumer.dto.d365requests.FetchShippingPackageDetailsResponse;
//import com.lenskart.financeConsumer.dto.d365requests.FetchSummaryResponse;
//import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
//import com.lenskart.financeConsumer.exceptions.SyncNotRequiredException;
//import com.lenskart.financeConsumer.model.unireports.UniReportD365SyncDetails;
//import com.lenskart.financeConsumer.model.unireports.UnireportSaleOrderDetails;
//import com.lenskart.financeConsumer.service.GenericClientService;
//import com.lenskart.financeConsumer.unireports.readOnlyRepository.UniReportD365SyncDetailsReadRepository;
//import com.lenskart.financeConsumer.unireports.readOnlyRepository.UnireportSaleOrderSyncReadRepository;
//import com.lenskart.financeConsumer.util.ManualForwardFlowUtil;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.*;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.test.util.ReflectionTestUtils;
//import org.springframework.util.concurrent.ListenableFuture;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyString;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.mock;
//
//@ExtendWith(MockitoExtension.class)
//public class ManualSaleOrderServiceImplTest {
//
//    @Spy
//    @InjectMocks
//    ManualSaleOrderServiceImpl manualSaleOrderService;
//
//    @Mock
//    UnireportSaleOrderSyncReadRepository unireportSaleOrderSyncReadRepository;
//
//    @Mock
//    ManualForwardFlowUtil manualForwardFlowUtil;
//
//    @Mock
//    GenericClientService genericClientService;
//
//    @Mock
//    @Qualifier("kafkaProducerTemplate")
//    private KafkaTemplate kafkaProducerTemplate;
//
//    @Mock
//    UnicomClient unicomClient;
//
//    @Mock
//    UniReportD365SyncDetailsReadRepository uniReportD365SyncDetailsReadRepository;
//
//    UnireportSaleOrderDetails unireportSaleOrderDetails1 = new UnireportSaleOrderDetails();
//
//    @BeforeEach
//    public void setUp() throws Exception {
//        MockitoAnnotations.initMocks(this);
//        ReflectionTestUtils.setField(manualSaleOrderService,"d365UnireportPackingSlipKafkaTopic","D365-Unicom-PackingSlip");
//        List<String> values = new ArrayList<>();
//        values.add("NXS1");
//        values.add("NXS2");
//        ReflectionTestUtils.setField(manualSaleOrderService,"disabledCustomerFacility",values);
//        unireportSaleOrderDetails1.setFacilityCode("LKH03");
//        unireportSaleOrderDetails1.setShippingPackageCode("LKH03SP131616");
//        unireportSaleOrderDetails1.setSaleOrderCode("1025");
//    }
//
//    @Test
//    void createSaleOrderTest() throws Exception {
//        Mockito.when(unireportSaleOrderSyncReadRepository.findTopByShippingPackageCode(anyString())).thenReturn(unireportSaleOrderDetails1);
//        Mockito.doReturn(new SalesOrderHeader()).when(manualSaleOrderService).generateSaleOrderPayload(anyString(),any());
//        doNothing().when(manualForwardFlowUtil).updateResponseInUnireportsDB(anyString(),anyString(),anyString(),anyString(),anyString());
//        doNothing().when(genericClientService).saveLog(anyString(),anyString(),any(),anyString(),anyString(),anyString());
//        Mockito.when(kafkaProducerTemplate.send(any(),any(),any())).thenReturn(mock(ListenableFuture.class));
//        HashMap<String,String> responseBody = new HashMap<>();
//        responseBody.put("Status","Success");
//        responseBody.put("Message","Record generated for sale order LKH03SP131616_LKH03");
//        ResponseEntity responseEntity = new ResponseEntity<HashMap>(
//                responseBody,
//                new HttpHeaders(),
//                HttpStatus.OK
//        );
//        Mockito.when(genericClientService.forwardRequest(any(),any(),any(),any())).thenReturn(responseEntity);
//        ResponseEntity response = manualSaleOrderService.createSalesOrder(anyString());
//        assertEquals("Record generated for sale order LKH03SP131616_LKH03",((HashMap)response.getBody()).get("Message"));
//    }
//
//    @Test
//    void generateSaleOrderPayloadInsertTest() throws Exception {
//        String saleOrderCode = "1025";
//        String fetchSummaryResponseString = "{\"successful\":true,\"message\":null,\"errors\":null,\"warnings\":null,\"saleOrderSummary\":{\"code\":\"1025\",\"displayOrderCode\":\"23-LKH03-0QNXS-2282\",\"priority\":0,\"status\":\"COMPLETE\",\"customerCode\":null,\"customerName\":\"0QNXS\",\"channel\":\"BulkToVendor\",\"thirdPartyShipping\":false,\"paymentMethod\":\"PREPAID\",\"paymentInstrument\":null,\"currencyCode\":\"INR\",\"totalPrice\":799.20,\"displayOrderDateTime\":1697622151000,\"fulfillmentTat\":1697794951000,\"created\":1697622151000,\"updated\":1697623920000,\"cFormProvided\":false,\"taxExempted\":false,\"billingAddress\":{\"id\":\"7850466\",\"name\":\"0QNXS\",\"addressLine1\":\"0/6,LavellleeRoad\",\"addressLine2\":\"ShanthalaNagar,AshokNagar\",\"city\":\"BANGALORE\",\"state\":\"KA\",\"country\":\"IN\",\"pincode\":\"560001\",\"phone\":\"9898989898\",\"email\":null},\"shippingAddress\":{\"id\":\"7850466\",\"name\":\"0QNXS\",\"addressLine1\":\"0/6,LavellleeRoad\",\"addressLine2\":\"ShanthalaNagar,AshokNagar\",\"city\":\"BANGALORE\",\"state\":\"KA\",\"country\":\"IN\",\"pincode\":\"560001\",\"phone\":\"9898989898\",\"email\":null},\"addressEditable\":false,\"podRequired\":false,\"customFieldValues\":[{\"fieldName\":\"alternatePhone\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AlternatePhone\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"assignedAt\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"AssignedAt\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"assignedMgr\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AssignedMgr\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"assignedTo\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AssignedTo\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"associatePONumber\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AssociatePONumber\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"beginComment\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"BeginComment\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"courierAmount\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"CourierAmount\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"customerServiceComments\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"CustomerServiceComments\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"customer_Comments\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"customer_Comments\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"deals_order\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"deals_order\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"donation_charge\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"donation_charge\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"emi_charge\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"emi_charge\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_amount_Offline\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_amount_Offline\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_amount_Online\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_amount_Online\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_amount_SC\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_amount_SC\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_date\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"Fast_refund_date\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_reason\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_reason\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"finishComment\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"finishcomment\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"finishTime\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"FinishTime\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"finishUser\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"finishuser\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"franchiseeCode\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"FranchiseeCode\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Gift_From\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Gift_From\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Gift_To\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Gift_To\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"HTO\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"HTO\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"is_dual_comp_s\",\"fieldValue\":false,\"valueType\":\"checkbox\",\"displayName\":\"IsDualCompS\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"jitordernumber\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"JITOrderNumber\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"lkCustomerCode\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"lkCustomerCode\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"lkPaymentMethod\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"LKPaymentMethod\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"merchant_key\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"MerchantKey\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"nav_channel\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"nav_channel\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"OldOrderID\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"OldOrderID\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"onHoldBeginTime\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"onholdbegintime\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"onHoldBeginUser\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"onholdbeginuser\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"orderType\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"orderType\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Payment_Capture\",\"fieldValue\":\"1\",\"valueType\":\"text\",\"displayName\":\"Payment_Capture\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"payment_method\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"payment_method\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"prepaidWeb\",\"fieldValue\":null,\"valueType\":\"text\",\"displayName\":\"prepaidWeb\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"prepaidWebPG\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"prepaidWebPG\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"retrun_awb\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"retrun_awb\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"saleOrderShelf\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"SaleOrderShelf\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"service_charge\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"service_charge\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"specialOrderFlag\",\"fieldValue\":null,\"valueType\":\"checkbox\",\"displayName\":\"SpecialOrder\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"splOrderFlag\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"SplOrderFlag\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"storeId\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"StoreId\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"team\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Team\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"voucher_value_order\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"voucher_value_order\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"vsmStatus\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"VSMStatus\",\"required\":false,\"possibleValues\":[\"\"]}],\"notificationEmail\":null,\"notificationMobile\":null,\"itemStatus\":{\"totalItems\":6,\"inProcess\":0,\"cancellable\":0,\"unfulfillable\":0,\"dispatched\":2,\"cancelled\":4,\"returned\":0,\"replaced\":0,\"reshipped\":0},\"saleOrderItemCount\":6,\"shipmentCount\":1,\"invoiceCount\":1,\"returnsCount\":4,\"activityCount\":20},\"invalidFacility\":false,\"validFacilities\":null}";
//        FetchSummaryResponse fetchSummary = new ObjectMapper().readValue(fetchSummaryResponseString, FetchSummaryResponse.class);
//        Mockito.when(unicomClient.fetchSummary(anyString(),anyString())).thenReturn(fetchSummary);
//        FetchShippingPackageDetailsResponse fetchShippingPackageDetailsResponse = new FetchShippingPackageDetailsResponse();
//        List<FetchShippingPackageDetailsResponse.ShippingPackage> shippingPackageArrayList= new ArrayList<>();
//        FetchShippingPackageDetailsResponse.ShippingPackage shippingPackage = new FetchShippingPackageDetailsResponse.ShippingPackage();
//        shippingPackageArrayList.add(shippingPackage);
//        fetchShippingPackageDetailsResponse.setShippingPackages(shippingPackageArrayList);
//        Mockito.when(unicomClient.fetchShippingPackageDetails(anyString(),anyString())).thenReturn(fetchShippingPackageDetailsResponse);
//        Mockito.when(uniReportD365SyncDetailsReadRepository.findByIdentifierAndPayloadType(anyString(), anyString())).thenReturn(null);
//        Exception exception =  assertThrows(SyncNotRequiredException.class, () -> manualSaleOrderService.generateSaleOrderPayload(saleOrderCode,unireportSaleOrderDetails1));
//        String actualMessage = exception.getMessage();
//        assertTrue(actualMessage.contains("Manual sale order is not applicable for"));
//    }
//
//    @Test
//    void generateSaleOrderPayloadUpdateTest() throws Exception {
//        String saleOrderCode = "1025";
//        String fetchSummaryResponseString = "{\"successful\":true,\"message\":null,\"errors\":null,\"warnings\":null,\"saleOrderSummary\":{\"code\":\"1025\",\"displayOrderCode\":\"23-LKH03-0QNXS-2282\",\"priority\":0,\"status\":\"COMPLETE\",\"customerCode\":null,\"customerName\":\"0QNXS\",\"channel\":\"BulkToVendor\",\"thirdPartyShipping\":false,\"paymentMethod\":\"PREPAID\",\"paymentInstrument\":null,\"currencyCode\":\"INR\",\"totalPrice\":799.20,\"displayOrderDateTime\":1697622151000,\"fulfillmentTat\":1697794951000,\"created\":1697622151000,\"updated\":1697623920000,\"cFormProvided\":false,\"taxExempted\":false,\"billingAddress\":{\"id\":\"7850466\",\"name\":\"0QNXS\",\"addressLine1\":\"0/6,LavellleeRoad\",\"addressLine2\":\"ShanthalaNagar,AshokNagar\",\"city\":\"BANGALORE\",\"state\":\"KA\",\"country\":\"IN\",\"pincode\":\"560001\",\"phone\":\"9898989898\",\"email\":null},\"shippingAddress\":{\"id\":\"7850466\",\"name\":\"0QNXS\",\"addressLine1\":\"0/6,LavellleeRoad\",\"addressLine2\":\"ShanthalaNagar,AshokNagar\",\"city\":\"BANGALORE\",\"state\":\"KA\",\"country\":\"IN\",\"pincode\":\"560001\",\"phone\":\"9898989898\",\"email\":null},\"addressEditable\":false,\"podRequired\":false,\"customFieldValues\":[{\"fieldName\":\"alternatePhone\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AlternatePhone\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"assignedAt\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"AssignedAt\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"assignedMgr\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AssignedMgr\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"assignedTo\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AssignedTo\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"associatePONumber\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"AssociatePONumber\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"beginComment\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"BeginComment\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"courierAmount\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"CourierAmount\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"customerServiceComments\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"CustomerServiceComments\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"customer_Comments\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"customer_Comments\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"deals_order\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"deals_order\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"donation_charge\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"donation_charge\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"emi_charge\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"emi_charge\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_amount_Offline\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_amount_Offline\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_amount_Online\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_amount_Online\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_amount_SC\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_amount_SC\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_date\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"Fast_refund_date\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Fast_refund_reason\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Fast_refund_reason\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"finishComment\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"finishcomment\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"finishTime\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"FinishTime\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"finishUser\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"finishuser\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"franchiseeCode\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"FranchiseeCode\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Gift_From\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Gift_From\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Gift_To\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Gift_To\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"HTO\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"HTO\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"is_dual_comp_s\",\"fieldValue\":false,\"valueType\":\"checkbox\",\"displayName\":\"IsDualCompS\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"jitordernumber\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"JITOrderNumber\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"lkCustomerCode\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"lkCustomerCode\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"lkPaymentMethod\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"LKPaymentMethod\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"merchant_key\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"MerchantKey\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"nav_channel\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"nav_channel\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"OldOrderID\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"OldOrderID\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"onHoldBeginTime\",\"fieldValue\":null,\"valueType\":\"date\",\"displayName\":\"onholdbegintime\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"onHoldBeginUser\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"onholdbeginuser\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"orderType\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"orderType\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"Payment_Capture\",\"fieldValue\":\"1\",\"valueType\":\"text\",\"displayName\":\"Payment_Capture\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"payment_method\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"payment_method\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"prepaidWeb\",\"fieldValue\":null,\"valueType\":\"text\",\"displayName\":\"prepaidWeb\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"prepaidWebPG\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"prepaidWebPG\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"retrun_awb\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"retrun_awb\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"saleOrderShelf\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"SaleOrderShelf\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"service_charge\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"service_charge\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"specialOrderFlag\",\"fieldValue\":null,\"valueType\":\"checkbox\",\"displayName\":\"SpecialOrder\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"splOrderFlag\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"SplOrderFlag\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"storeId\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"StoreId\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"team\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"Team\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"voucher_value_order\",\"fieldValue\":0.00,\"valueType\":\"text\",\"displayName\":\"voucher_value_order\",\"required\":false,\"possibleValues\":[\"\"]},{\"fieldName\":\"vsmStatus\",\"fieldValue\":\"\",\"valueType\":\"text\",\"displayName\":\"VSMStatus\",\"required\":false,\"possibleValues\":[\"\"]}],\"notificationEmail\":null,\"notificationMobile\":null,\"itemStatus\":{\"totalItems\":6,\"inProcess\":0,\"cancellable\":0,\"unfulfillable\":0,\"dispatched\":2,\"cancelled\":4,\"returned\":0,\"replaced\":0,\"reshipped\":0},\"saleOrderItemCount\":6,\"shipmentCount\":1,\"invoiceCount\":1,\"returnsCount\":4,\"activityCount\":20},\"invalidFacility\":false,\"validFacilities\":null}";
//        FetchSummaryResponse fetchSummary = new ObjectMapper().readValue(fetchSummaryResponseString, FetchSummaryResponse.class);
//        Mockito.when(unicomClient.fetchSummary(anyString(),anyString())).thenReturn(fetchSummary);
//        FetchShippingPackageDetailsResponse fetchShippingPackageDetailsResponse = new FetchShippingPackageDetailsResponse();
//        List<FetchShippingPackageDetailsResponse.ShippingPackage> shippingPackageArrayList= new ArrayList<>();
//        FetchShippingPackageDetailsResponse.ShippingPackage shippingPackage = new FetchShippingPackageDetailsResponse.ShippingPackage();
//        shippingPackageArrayList.add(shippingPackage);
//        fetchShippingPackageDetailsResponse.setShippingPackages(shippingPackageArrayList);
//        Mockito.when(unicomClient.fetchShippingPackageDetails(anyString(),anyString())).thenReturn(fetchShippingPackageDetailsResponse);
//        Mockito.when(uniReportD365SyncDetailsReadRepository.findByIdentifierAndPayloadType(anyString(), anyString())).thenReturn(new UniReportD365SyncDetails());
//        Exception exception =  assertThrows(SyncNotRequiredException.class, () -> manualSaleOrderService.generateSaleOrderPayload(saleOrderCode,unireportSaleOrderDetails1));
//        String actualMessage = exception.getMessage();
//        assertTrue(actualMessage.contains("Manual sale order is not applicable for"));
//    }
//}