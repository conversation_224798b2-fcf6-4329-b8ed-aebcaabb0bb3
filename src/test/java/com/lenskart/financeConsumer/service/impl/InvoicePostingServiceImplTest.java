package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.dto.d365requests.D365ResponseDto;
import com.lenskart.financeConsumer.service.InvoicePostingNexsService;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.GetInvoiceRequestDTO;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.InvoiceDTO;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.InvoiceRequestDTO;
import com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO.InvoiceResponseDTO;
import com.lenskart.financeConsumer.exceptions.InvoiceNotFoundException;
import com.lenskart.financeConsumer.exceptions.InvoiceNumberAlreadyExistsException;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.InvoicePostingStatus;
import com.lenskart.financeConsumer.model.enums.InvoicePostingSubStatus;
import com.lenskart.financeConsumer.model.enums.SortOrderType;
import com.lenskart.financeConsumer.model.financeDb.Invoice;
import com.lenskart.financeConsumer.service.InvoicePostingRepositoryService;
import com.lenskart.financeConsumer.util.FinanceAdaptorClientUtil;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class InvoicePostingServiceImplTest {

    @InjectMocks
    InvoicePostingServiceImpl invoicePostingService;
    @Mock
    FinanceAdaptorClientUtil financeAdaptorClientUtil;
    @Mock
    InvoicePostingRepositoryService invoicePostingRepositoryService;
    @Mock
    InvoicePostingNexsService invoicePostingNexsService;

    private Invoice invoice;
    private InvoiceDTO invoiceDTO;

    private List<InvoiceDTO> invoiceDTOList = new ArrayList<>();
    private List<Invoice> invoiceList = new ArrayList<>();

    private Map<String, String> responseMap = new HashMap<>();

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this); //without this you will get NPE
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .grnCodes("a,b,c")
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        invoiceDTO = InvoiceDTO.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount("100.000")
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        invoiceList.add(invoice);
        invoiceDTOList.add(invoiceDTO);
    }


    @Test
    public void getAllInvoice() {
        Pageable tempPageable = PageRequest.of(0, 1);
        Page<Invoice> invoicePage = new PageImpl<>(invoiceList, tempPageable, 1);
        GetInvoiceRequestDTO getInvoiceRequestDTO = new GetInvoiceRequestDTO();
        getInvoiceRequestDTO.setPageNumber(0);
        getInvoiceRequestDTO.setPageSize(2);
        getInvoiceRequestDTO.setSortKey("createdAt");
        getInvoiceRequestDTO.setSortOrder(SortOrderType.ASCENDING);
        Pageable pageable = PageRequest.of(getInvoiceRequestDTO.getPageNumber(), getInvoiceRequestDTO.getPageSize(),
                                           Sort.by(getInvoiceRequestDTO.getSortKey()).ascending());
        when(invoicePostingRepositoryService.getAllInvoice(pageable)).thenReturn(invoicePage);
        GenericResponseDto<InvoiceResponseDTO> responseDTO = invoicePostingService.getAllInvoice(getInvoiceRequestDTO);
        Assertions.assertEquals(1, responseDTO.getData().getTotalCount());
        Assertions.assertEquals(0, responseDTO.getData().getSuccessCount());
        Assertions.assertEquals(0, responseDTO.getData().getFailedCount());
        Assertions.assertEquals(0, responseDTO.getData().getInProgressCount());
        Assertions.assertEquals(invoiceList.size(), responseDTO.getData().getInvoiceDTOList().size());
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).getAllInvoice(pageable);
    }

    @Test
    public void getInvoiceById() {
        long invoiceId = 1L;
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        InvoiceDTO invoiceDTO = InvoiceDTO.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount("100.000")
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        when(invoicePostingRepositoryService.getInvoiceById(invoiceId)).thenReturn(invoice);
        InvoicePostingServiceImpl spy = spy(invoicePostingService);
        Mockito.doReturn(invoiceDTO).when(spy).buildInvoiceDTO(invoice);
        GenericResponseDto<InvoiceDTO> actualInvoiceDTO = spy.getInvoiceById(invoiceId);
        Assertions.assertEquals(1, actualInvoiceDTO.getData().getId());
    }


    @Test
    public void createInvoice() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .vendorInvoiceQuantity(1)
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .boeNumber("test-BOE")
                .vendorInvoiceQuantity(1).build();

        InvoiceRequestDTO invoiceRequestDTO = new InvoiceRequestDTO();
        invoiceRequestDTO.setVendorInvoiceNumber("1");
        invoiceRequestDTO.setVendorName("Vendor1");
        invoiceRequestDTO.setVendorCode("V1");
        invoiceRequestDTO.setVendorInvoiceQuantity(10);
        invoiceRequestDTO.setVendorInvoiceAmount(new BigDecimal(100.000));
        invoiceRequestDTO.setCurrencyCode("INR");
        invoiceRequestDTO.setUrl("https://url1.com");
        invoiceRequestDTO.setPoNumber("test-PO");
        invoiceRequestDTO.setBoeNumber("test-BOE");

        responseMap.put("Success", Boolean.TRUE.toString());
        responseMap.put("Message", "Valid Classification ID present in this PO");

        when(invoicePostingRepositoryService.saveInvoice(any())).thenReturn(invoice);
        when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(Mockito.anyString())).thenReturn(null);
        when(invoicePostingNexsService.hasValidClassificationId(Mockito.any())).thenReturn(responseMap);
        GenericResponseDto<InvoiceDTO> response = invoicePostingService.createInvoice(invoiceRequestDTO);
        Assertions.assertTrue(response.getMeta().getDisplayMessage().contains("Successfully created invoice"));
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).getInvoiceByVendorInvoiceNumber(invoiceRequestDTO.getVendorInvoiceNumber());
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).saveInvoice(Mockito.any(Invoice.class));
        Mockito.verifyNoMoreInteractions(invoicePostingRepositoryService);
    }

    @Test(expected = InvoiceNumberAlreadyExistsException.class)
    public void createInvoice_InvoiceNumberAlreadyExists() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .vendorInvoiceQuantity(1)
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .boeNumber("test-BOE")
                .vendorInvoiceQuantity(1).build();

        InvoiceRequestDTO invoiceRequestDTO = new InvoiceRequestDTO();
        invoiceRequestDTO.setVendorInvoiceNumber("1");
        invoiceRequestDTO.setVendorName("Vendor1");
        invoiceRequestDTO.setVendorCode("V1");
        invoiceRequestDTO.setVendorInvoiceQuantity(10);
        invoiceRequestDTO.setVendorInvoiceAmount(new BigDecimal(100.000));
        invoiceRequestDTO.setCurrencyCode("INR");
        invoiceRequestDTO.setUrl("https://url1.com");
        invoiceRequestDTO.setPoNumber("test-PO");
        invoiceRequestDTO.setBoeNumber("test-BOE");

        when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(Mockito.anyString())).thenReturn(invoice);
        GenericResponseDto<InvoiceDTO> response = invoicePostingService.createInvoice(invoiceRequestDTO);
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).getInvoiceByVendorInvoiceNumber(invoiceRequestDTO.getVendorInvoiceNumber());
    }

    @Test
    public void updateInvoice() {
        InvoiceRequestDTO invoiceRequestDTO = new InvoiceRequestDTO();
        invoiceRequestDTO.setVendorInvoiceNumber("1");
        invoiceRequestDTO.setVendorName("Vendor1");
        invoiceRequestDTO.setVendorCode("V1");
        invoiceRequestDTO.setCurrencyCode("INR");
        invoiceRequestDTO.setUrl("https://url1.com");
        invoiceRequestDTO.setVendorInvoiceQuantity(1);
        Invoice oldInvoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("Old_1")
                .poNumber("Old_p1")
                .vendorCode("Old_V1")
                .vendorName("Old_Vendor1")
                .currencyCode("Old_INR")
                .url("Old_https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        Invoice newInvoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("New_1")
                .poNumber("New_p1")
                .vendorCode("New_V1")
                .vendorName("New_Vendor1")
                .currencyCode("New_INR")
                .url("New_https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(10).build();

        responseMap.put("Success", Boolean.TRUE.toString());
        responseMap.put("Message", "Valid Classification ID present in this PO");

        when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(invoiceRequestDTO.getVendorInvoiceNumber())).thenReturn(oldInvoice);
        when(invoicePostingRepositoryService.saveInvoice(Mockito.any(Invoice.class))).thenReturn(newInvoice);
        when(invoicePostingNexsService.hasValidClassificationId(Mockito.any())).thenReturn(responseMap);
        GenericResponseDto<InvoiceDTO> result = invoicePostingService.updateInvoice(invoiceRequestDTO);
        Assertions.assertTrue(result.getMeta().getDisplayMessage().contains("Successfully updated invoice"));
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).getInvoiceByVendorInvoiceNumber(invoiceRequestDTO.getVendorInvoiceNumber());
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).saveInvoice(Mockito.any(Invoice.class));
        Mockito.verifyNoMoreInteractions(invoicePostingRepositoryService);
    }

    @Test(expected = InvoiceNotFoundException.class)
    public void updateInvoice_NotFound() {
        InvoiceRequestDTO invoiceRequestDTO = new InvoiceRequestDTO();
        invoiceRequestDTO.setVendorInvoiceNumber("1");
        invoiceRequestDTO.setVendorName("Vendor1");
        invoiceRequestDTO.setVendorCode("V1");
        invoiceRequestDTO.setVendorInvoiceQuantity(10);
        invoiceRequestDTO.setVendorInvoiceAmount(new BigDecimal(100.000));
        invoiceRequestDTO.setCurrencyCode("INR");
        invoiceRequestDTO.setUrl("https://url1.com");

        when(invoicePostingRepositoryService.getInvoiceByVendorInvoiceNumber(invoiceRequestDTO.getVendorInvoiceNumber())).thenReturn(null);
        GenericResponseDto<InvoiceDTO> result = invoicePostingService.updateInvoice(invoiceRequestDTO);
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).getInvoiceByVendorInvoiceNumber(invoiceRequestDTO.getVendorInvoiceNumber());
        Mockito.verifyNoMoreInteractions(invoicePostingRepositoryService);
    }

    @Test
    public void deleteInvoice() {
        long invoiceId = 1L;
        ReflectionTestUtils.setField(invoicePostingService, "invoicePostingDeleteFlag", true);
        GenericResponseDto<String> result = invoicePostingService.deleteInvoice(invoiceId);
        Assertions.assertTrue(result.getData().contains("Successfully deleted invoice with id"));
        Mockito.verify(invoicePostingRepositoryService, Mockito.times(1)).deleteInvoice(invoiceId);
        Mockito.verifyNoMoreInteractions(invoicePostingRepositoryService);
    }

    @Test
    public void deleteInvoice_Disabled() {
        long invoiceId = 1L;
        ReflectionTestUtils.setField(invoicePostingService, "invoicePostingDeleteFlag", false);
        GenericResponseDto<String> result = invoicePostingService.deleteInvoice(invoiceId);
        Assertions.assertTrue(result.getData().contains("Invoice deletion currently disabled."));
        Mockito.verifyNoInteractions(invoicePostingRepositoryService);
    }

    @Test
    public void buildInvoiceDTO() {
        Invoice invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .vendorInvoiceQuantity(10)
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();

        InvoiceDTO invoiceDTO = InvoiceDTO.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount("100.000")
                .vendorInvoiceQuantity(10)
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .vendorInvoiceQuantity(1).build();
        InvoiceDTO responseDTO = invoicePostingService.buildInvoiceDTO(invoice);
        Assertions.assertEquals(invoiceDTO.getId(), responseDTO.getId());
        Assertions.assertEquals(invoiceDTO.getVendorInvoiceNumber(), responseDTO.getVendorInvoiceNumber());
        Assertions.assertEquals(invoiceDTO.getVendorInvoiceQuantity(), responseDTO.getVendorInvoiceQuantity());
        Assertions.assertEquals(invoiceDTO.getVendorCode(), responseDTO.getVendorCode());
        Assertions.assertEquals(invoiceDTO.getVendorName(), responseDTO.getVendorName());
        Assertions.assertEquals(invoiceDTO.getCurrencyCode(), responseDTO.getCurrencyCode());
        Assertions.assertEquals(invoiceDTO.getUrl(), responseDTO.getUrl());
        Assertions.assertEquals(invoiceDTO.getInvoiceStatus(), responseDTO.getInvoiceStatus());
        Assertions.assertEquals(invoiceDTO.getInvoiceSubStatus(), responseDTO.getInvoiceSubStatus());
        Assertions.assertEquals(invoiceDTO.getTriggerCount(), responseDTO.getTriggerCount());
        Assertions.assertEquals(invoiceDTO.getCreatedBy(), responseDTO.getCreatedBy());
        Assertions.assertEquals(invoiceDTO.getUpdatedBy(), responseDTO.getUpdatedBy());
    }

    @Test
    public void buildInvoiceDTO_nullInvoice() {
        Invoice invoice = Invoice.builder().vendorInvoiceQuantity(1).build();
        InvoiceDTO responseDTO = invoicePostingService.buildInvoiceDTO(invoice);
        Assertions.assertNull(responseDTO.getId());
        Assertions.assertNull(responseDTO.getVendorInvoiceNumber());
        Assertions.assertNull(responseDTO.getVendorName());
        Assertions.assertNull(responseDTO.getVendorCode());
        Assertions.assertNull(responseDTO.getCurrencyCode());
        Assertions.assertNull(responseDTO.getUrl());
        Assertions.assertNull(responseDTO.getInvoiceStatus());
        Assertions.assertNull(responseDTO.getInvoiceSubStatus());
        Assertions.assertNull(responseDTO.getTriggerCount());
        Assertions.assertNull(responseDTO.getCreatedBy());
        Assertions.assertNull(responseDTO.getUpdatedBy());
    }

    @Test
    public void syncVendorInvoice() {
        ErpResponseDto erpResponseDto = ErpResponseDto.builder().id(2L).response("Successfully created").d365SyncStatus(
                D365ResponseType.SUCCESS).build();
        invoice = Invoice.builder()
                .id(1L)
                .vendorInvoiceNumber("1")
                .poNumber("p1")
                .vendorCode("V1")
                .vendorName("Vendor1")
                .currencyCode("INR")
                .url("https://url1.com")
                .vendorInvoiceAmount(new BigDecimal(100.000))
                .invoiceStatus(InvoicePostingStatus.CREATED)
                .invoiceSubStatus(InvoicePostingSubStatus.CREATED)
                .triggerCount(1)
                .createdBy("NEXS-UI")
                .updatedBy("NEXS-UI")
                .boeNumber("BOE")
                .grnCodes("a,b,c")
                .createdAt(LocalDateTime.now())
                .vendorInvoiceQuantity(1).build();
        when(financeAdaptorClientUtil.callFinanceAdaptor(ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.anyLong())).thenReturn(erpResponseDto);
        when(financeAdaptorClientUtil.getD365ResponseDto(ArgumentMatchers.anyLong(), ArgumentMatchers.anyString(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(D365ResponseDto.builder()
                                                                                                                                                                            .d365SyncStatus(D365ResponseType.SUCCESS.name())
                                                                                                                                                                            .event(FinanceSourceSystemSyncEvent.VENDOR_INVOICE)
                                                                                                                                                                            .errorMessage("Successfully created")
                                                                                                                                                                            .id(2L)
                                                                                                                                                                            .build());
        D365ResponseDto d365ResponseDto=invoicePostingService.syncVendorInvoice(invoice);
        Assertions.assertEquals("Successfully created",d365ResponseDto.getErrorMessage());
        Assertions.assertEquals(D365ResponseType.SUCCESS.name(),d365ResponseDto.getD365SyncStatus());
    }
}
