package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.clients.JunoClient;
import com.lenskart.financeConsumer.connector.impl.InventoryAdaptorConnectorImpl;
import com.lenskart.financeConsumer.dao.ReturnEinvoicingRepository;
import com.lenskart.financeConsumer.dao.ReturnOrderItemRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardSyncDto;
import com.lenskart.financeConsumer.dto.d365requests.InvoiceDetailsResponse;
import com.lenskart.financeConsumer.dto.d365requests.giftCardD365Request.GeneralJournalHeader;
import com.lenskart.financeConsumer.dto.d365requests.giftCardD365Request.JournalLineDto;
import com.lenskart.financeConsumer.dto.d365requests.juno.giftCard.Data;
import com.lenskart.financeConsumer.dto.d365requests.juno.giftCard.JunoGiftCardResponse;
import com.lenskart.financeConsumer.dto.d365requests.juno.giftCard.ProgramBreakup;
import com.lenskart.financeConsumer.financeDb.inventory.read.OrderReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.GiftCardMetaDataRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.GiftCardSyncRepository;
import com.lenskart.financeConsumer.model.ReturnEinvoicing;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.financeDb.GiftCard;
import com.lenskart.financeConsumer.model.financeDb.GiftCardMetaData;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.FinanceAdaptorClientUtil;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import com.lenskart.orderops.model.ReturnOrder;
import com.lenskart.orderops.model.ReturnOrderItem;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Array;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;


@ExtendWith(MockitoExtension.class)
class GiftCardServiceTest {


    @Spy
    @InjectMocks
    private GiftCardService giftCardService;

    @Mock
    private JunoClient junoClient;

    @Mock
    private GiftCardSyncRepository giftCardRepository;

    @Mock
    private GiftCardMetaDataRepository giftCardMetaRepository;

    @Mock
    private UwOrdersReadRepositoryFinance uwOrdersReadRepository;

    @Mock
    private OrderReadRepository orderReadRepository;

    @Mock
    private GenericClientService genericClientService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private DateUtils dateUtils;

    @Mock
    Iterable<GiftCard> iterable;

    @Mock
    Iterator<GiftCard> iterator;

    @Mock
    private KafkaTemplate kafkaProducerTemplate;

    @Mock
    private FinanceAdaptorClientUtil financeAdaptorClientUtil;

    @Mock
    private InventoryAdaptorConnectorImpl inventoryAdaptorConnector;
    @Mock
    private  ReturnOrderItemRepository returnOrderItemRepository;
    @Mock
    private  ReturnEinvoicingRepository returnEinvoicingRepository;
    @Mock
    private  UwOrdersRepository uwOrdersRepository;





    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(giftCardService,"giftCardMaxSyncLimit",100);
        ReflectionTestUtils.setField(giftCardService,"giftCardTopic","card");
        giftCardService = Mockito.spy(new GiftCardService( junoClient,
                                                           giftCardRepository,
                                                           giftCardMetaRepository,
                                                           uwOrdersReadRepository,
                                                           orderReadRepository,
                                                           financeAdaptorClientUtil,
                                                           objectMapper,
                                                           inventoryAdaptorConnector,
                                                           returnOrderItemRepository,
                                                           returnEinvoicingRepository,
                                                           uwOrdersRepository,
                                                           dateUtils));
    }


    @Test
    void processGiftCardWhenGiftCardAndMetaDataAlreadyPresent() {

        GiftCard giftCard =new GiftCard();
        giftCard.setCreatedBy("finance-consumer");
        giftCard.setUpdatedBy("finance-consumer");


        GiftCardMetaData giftCardMetaData =new GiftCardMetaData();
        giftCardMetaData.setCreatedBy("finance-consumer");
        giftCardMetaData.setUpdatedBy("finance-consumer");

        GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                legalEntity("LKIN").
                shipmentId("SHIP").
                facilityCode("NXS1").
                uwItemIds(Arrays.asList(1,2)).
                build();

        when(giftCardRepository.findByUwItemIdsAndSource(any(),any())).thenReturn(Arrays.asList(giftCard));
        when(giftCardMetaRepository.findByReferenceIdIn(anyList())).thenReturn(Arrays.asList(giftCardMetaData));
        ReflectionTestUtils.setField(giftCardService,"d365GiftCardSyncEnabled",false);

        giftCardService.processGiftCard(giftCardSyncDto);
        verify(giftCardMetaRepository,times(1)).findByReferenceIdIn(anyList());

    }

    @Test
    void processGiftCardWhenGiftCardNotPresentAlready() {

        GiftCard giftCard =new GiftCard();
        giftCard.setCreatedBy("finance-consumer");
        giftCard.setUpdatedBy("finance-consumer");
        giftCard.setUwItemId(1);


        GiftCardMetaData giftCardMetaData =new GiftCardMetaData();
        giftCardMetaData.setCreatedBy("finance-consumer");
        giftCardMetaData.setUpdatedBy("finance-consumer");

        GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                legalEntity("LKIN").
                shipmentId("SHIP").
                facilityCode("NXS1").
                uwItemIds(Arrays.asList(1,2)).
                build();

        UwOrder uwOrder = new UwOrder();
        uwOrder.setUwItemId(1);
        uwOrder.setItemId(10);
        uwOrder.setShippingPackageId("SHIP");
        List<UwOrder> uwOrderList = Arrays.asList(uwOrder);

        Order order =new Order();
        order.setMagentoItemId(111);
        order.setItemId(10);

        Data data = new Data();
        List<ProgramBreakup> programBreakupList =new ArrayList<>();
        ProgramBreakup programBreakup =new ProgramBreakup();
        programBreakup.setProgramId("program_1");
        programBreakup.setPercentageAmount(10.15);
        programBreakupList.add(programBreakup);

        data.setProgramBreakup(programBreakupList);
        data.setAmount(100.0);
        data.setItemId(111L);

        List<Data> dataList =new ArrayList<>();
        dataList.add(data);

        JunoGiftCardResponse junoGiftCardResponse = JunoGiftCardResponse.builder().
                status(200).
                message("response fetched successfully").
                data(dataList).
                build();

        List<GiftCard> giftCards = Arrays.asList(giftCard);
        when(giftCardRepository.findByUwItemIdsAndSource(any(),any())).thenReturn(null);  // not present already
        when(giftCardMetaRepository.findByReferenceIdIn(anyList())).thenReturn(null);   // not present already
        ReflectionTestUtils.setField(giftCardService,"d365GiftCardSyncEnabled",false);
        when(giftCardRepository.saveAll(anyList())).thenReturn(giftCards);

        when(uwOrdersReadRepository.findByUwItemIdIn(any())).thenReturn(uwOrderList);
        when(orderReadRepository.findByItemIdIn(any())).thenReturn(Arrays.asList(order));
        when(junoClient.getGiftCardDetails(any(),any())).thenReturn(junoGiftCardResponse);
        when(giftCardMetaRepository.save(any())).thenReturn(giftCardMetaData);
        giftCardService.processGiftCard(giftCardSyncDto);
        verify(giftCardMetaRepository,times(1)).findByReferenceIdIn(anyList());
    }


    @Test
    void processGiftCardWhenGiftCardNotPresentAlreadyAndJunoResponseNullData() {

        GiftCard giftCard =new GiftCard();
        giftCard.setCreatedBy("finance-consumer");
        giftCard.setUpdatedBy("finance-consumer");
        giftCard.setUwItemId(1);


        GiftCardMetaData giftCardMetaData =new GiftCardMetaData();
        giftCardMetaData.setCreatedBy("finance-consumer");
        giftCardMetaData.setUpdatedBy("finance-consumer");

        GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                legalEntity("LKIN").
                shipmentId("SHIP").
                facilityCode("NXS1").
                uwItemIds(Arrays.asList(1,2)).
                build();

        UwOrder uwOrder = new UwOrder();
        uwOrder.setUwItemId(1);
        uwOrder.setItemId(10);
        uwOrder.setShippingPackageId("SHIP");
        List<UwOrder> uwOrderList = Arrays.asList(uwOrder);

        Order order =new Order();
        order.setMagentoItemId(111);
        order.setItemId(10);

        Data data = null;
        List<ProgramBreakup> programBreakupList =new ArrayList<>();
        ProgramBreakup programBreakup =new ProgramBreakup();
        programBreakup.setProgramId("program_1");
        programBreakup.setPercentageAmount(10.15);
        programBreakupList.add(programBreakup);


        List<Data> dataList =new ArrayList<>();
        dataList.add(data);

        JunoGiftCardResponse junoGiftCardResponse = JunoGiftCardResponse.builder().
                status(200).
                message("response fetched successfully").
                data(null).
                build();

        List<GiftCard> giftCards = Arrays.asList(giftCard);
        when(giftCardRepository.findByUwItemIdsAndSource(any(),any())).thenReturn(null);  // not present already
        when(giftCardMetaRepository.findByReferenceIdIn(anyList())).thenReturn(null);   // not present already
        ReflectionTestUtils.setField(giftCardService,"d365GiftCardSyncEnabled",false);
        when(giftCardRepository.saveAll(anyList())).thenReturn(giftCards);

        when(uwOrdersReadRepository.findByUwItemIdIn(any())).thenReturn(uwOrderList);
        when(orderReadRepository.findByItemIdIn(any())).thenReturn(Arrays.asList(order));
        when(junoClient.getGiftCardDetails(any(),any())).thenReturn(junoGiftCardResponse);
        assertThrows(RuntimeException.class, () -> giftCardService.processGiftCard(giftCardSyncDto));
        verify(giftCardMetaRepository,times(1)).findByReferenceIdIn(anyList());
    }
    @Test
    void persistGiftCard() {
        GiftCardSyncDto dto = new GiftCardSyncDto();
        dto.setUwItemIds(Arrays.asList(1, 2));
        dto.setSource("SCM");

        GiftCard giftCard = new GiftCard();
        giftCard.setD365SyncStatus("SUCCESS");

        when(giftCardRepository.findByUwItemIdsAndSource(dto.getUwItemIds(), dto.getSource()))
                .thenReturn(Arrays.asList(giftCard));

        giftCardService.processGiftCard(dto);

        verify(giftCardRepository, never()).saveAll(any());
    }

    @Test
    void persistGiftCardMetaData() {
        GiftCardSyncDto dto = new GiftCardSyncDto();
        dto.setUwItemIds(Arrays.asList(1, 2));
        dto.setSource("SCM");

        when(giftCardRepository.findByUwItemIdsAndSource(dto.getUwItemIds(), dto.getSource()))
                .thenReturn(new ArrayList<>());

        List<GiftCard> savedGiftCards = Arrays.asList(new GiftCard());
        when(giftCardRepository.saveAll(any())).thenReturn(savedGiftCards);

        List<GiftCard> result = giftCardService.persistGiftCard(dto);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void syncGiftCardByIdsScm() {


        GiftCard giftCard =new GiftCard();
        giftCard.setCreatedBy("finance-consumer");
        giftCard.setUpdatedBy("finance-consumer");
        giftCard.setSource("SCM");

        giftCard.setUwItemId(1);
        when(giftCardRepository.findByShipmentIdAndFacilityCodeAndSource(any(),any(),any())).thenReturn(Arrays.asList(1));
        when(giftCardRepository.findByIdIn(anyList())).thenReturn(Arrays.asList(giftCard));
       // doNothing().when(kafkaProducerTemplate.send(any(),any(),any()));
        ReflectionTestUtils.setField(giftCardService, "giftCardMaxSyncLimit", 10);
        giftCardService.syncGiftCardByIds(Collections.singletonList(1L));
    }

    @Test
    public void syncToD365NotSuccessReturn(){
        List<GiftCard> giftCards=new ArrayList<>();
        List<GiftCardMetaData> giftCardMetaDat;
        GiftCard giftCard =new GiftCard();
        giftCard.setId(2L);
        giftCard.setCreatedBy("finance-consumer");
        giftCard.setUpdatedBy("finance-consumer");
        giftCard.setSource("RETURN");
        giftCards.add(giftCard);

        when(giftCardRepository.findByUwItemIdsAndSource(any(),any())).thenReturn(giftCards);
//        when(genericClientService.forwardRequest(any(),any(),any(),any())).thenReturn(new ResponseEntity<>("posted successfully!", HttpStatus.OK));

        assertNull(ReflectionTestUtils.invokeMethod(giftCardService,"syncGiftCardToD365",giftCards,null)); // not success
    }

    @Test
    public void syncToD365SuccessReturn(){
        List<GiftCard> giftCards=new ArrayList<>();
        List<GiftCardMetaData> giftCardMetaDat;
        GiftCard giftCard =new GiftCard();
        giftCard.setId(2L);
        giftCard.setCreatedBy("finance-consumer");
        giftCard.setUpdatedBy("finance-consumer");
        giftCard.setD365SyncStatus("SUCCESS");
        giftCard.setSource("RETURN");
        giftCards.add(giftCard);

        ErpResponseDto responseDto = ErpResponseDto.builder()
                .response("Posted successfully")
                .id(giftCard.getId())
                .d365SyncStatus(D365ResponseType.SUCCESS)
                .entityId(String.valueOf(giftCard.getId()))
                .build();

        ReturnOrderItem returnOrderItem = new ReturnOrderItem();
        returnOrderItem.setReturnId(1);

        when(giftCardRepository.findByUwItemIdsAndSource(any(),any())).thenReturn(giftCards);
        when(financeAdaptorClientUtil.callFinanceAdaptor(anyString(), any(), anyLong())).thenReturn(responseDto);
        doReturn(new GeneralJournalHeader()).when(giftCardService).getGiftCardPayload(any(), any());
        when(giftCardRepository.saveAll(any())).thenReturn(giftCards);

        ResponseEntity responseEntity = ReflectionTestUtils.invokeMethod(giftCardService,"syncGiftCardToD365",giftCards,null); // not success

    }


    @Test
    void getGiftCardPayloadSuccessSCM() {
        GiftCard giftCard = new GiftCard();
        giftCard.setShipmentId("TestShipment");
        giftCard.setId(1L);
        giftCard.setSource("SCM");
        giftCard.setFacilityCode("TestFacility");
        giftCard.setOrderId(1);
        giftCard.setUwItemId(100);
        giftCard.setLegalEntity("LKIN");


        GiftCardMetaData giftCardMetaData = new GiftCardMetaData();
        giftCardMetaData.setId(1L);
        giftCardMetaData.setMagentoId(1);

        doReturn(Arrays.asList(new JournalLineDto())).when(giftCardService).getJournalLines(any(), any(), anyString());
        ReflectionTestUtils.setField(giftCardService, "giftCardJournalName", "Test Journal");

        GeneralJournalHeader response = giftCardService.getGiftCardPayload(Arrays.asList(giftCard),
                                                                           Arrays.asList(giftCardMetaData));

        Assertions.assertEquals(String.valueOf(giftCard.getOrderId()) + "_" + giftCard.getShipmentId() + "_" + giftCard.getFacilityCode()
                , response.getDocumentNo());

    }

    @Test
    void getGiftCardPayloadSuccessReturn() {
        GiftCard giftCard = new GiftCard();
        giftCard.setShipmentId("TestShipment");
        giftCard.setId(1L);
        giftCard.setSource("RETURN");
        giftCard.setFacilityCode("TestFacility");
        giftCard.setOrderId(1);
        giftCard.setUwItemId(100);
        giftCard.setLegalEntity("LKIN");


        GiftCardMetaData giftCardMetaData = new GiftCardMetaData();
        giftCardMetaData.setId(1L);
        giftCardMetaData.setMagentoId(1);

        ReturnOrderItem returnOrderItem  =new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        when(returnOrderItemRepository.findByUwItemId(any())).thenReturn(returnOrderItem);
        doReturn(Arrays.asList(new JournalLineDto())).when(giftCardService).getJournalLines(any(), any(), anyString());
        ReflectionTestUtils.setField(giftCardService, "giftCardJournalName", "Test Journal");

        GeneralJournalHeader response = giftCardService.getGiftCardPayload(Arrays.asList(giftCard),
                                                                           Arrays.asList(giftCardMetaData));

        Assertions.assertEquals(String.valueOf(giftCard.getOrderId()) + "_" + String.valueOf(returnOrderItem.getReturnId()) + "_" + String.valueOf(giftCard.getUwItemId())
                , response.getDocumentNo());

    }

    @Test
    void getGiftCardPayloadFailure() {
        GiftCard giftCard = new GiftCard();
        giftCard.setShipmentId("TestShipment");
        giftCard.setId(1L);
        giftCard.setSource("RETURN");
        giftCard.setFacilityCode("TestFacility");
        giftCard.setOrderId(1);
        giftCard.setUwItemId(100);
        giftCard.setLegalEntity("LKIN");


        GiftCardMetaData giftCardMetaData = new GiftCardMetaData();
        giftCardMetaData.setId(1L);
        giftCardMetaData.setMagentoId(1);

        ReturnOrderItem returnOrderItem  =new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        when(returnOrderItemRepository.findByUwItemId(any())).thenThrow(RuntimeException.class);

        assertThrows(RuntimeException.class, () -> {
            giftCardService.getGiftCardPayload(Arrays.asList(giftCard), Arrays.asList(giftCardMetaData));
        });
    }


    @Test
    void getInvoiceCreationDateSuccess() throws Exception {
        // Arrange
        int uwItemId = 123;
        UwOrder uwOrder = new UwOrder();
        uwOrder.setUnicomOrderCode("UNICOM123");
        uwOrder.setFacilityCode("FAC001");

        InvoiceDetailsResponse invoiceDetailsResponse = new InvoiceDetailsResponse();
        InvoiceDetailsResponse.Invoice invoice = new InvoiceDetailsResponse.Invoice();
        invoice.created = String.valueOf(Instant.now().toEpochMilli());
        invoice.code = "INV123";
        ArrayList<InvoiceDetailsResponse.Invoice> invoices = new ArrayList<>();
        invoices.add(invoice);
        invoiceDetailsResponse.setInvoices(invoices);

        when(uwOrdersRepository.findByUwItemId(uwItemId)).thenReturn(uwOrder);
        when(inventoryAdaptorConnector.fetchInvoiceDetails(anyString(), anyString(), anyString()))
                .thenReturn(invoiceDetailsResponse);
        DateTimeFormatter isoDateFormatter = DateTimeFormatter.ISO_DATE_TIME;
        ReflectionTestUtils.setField(giftCardService, "isoDateFormatter", isoDateFormatter);

        // Act
        LocalDateTime result = giftCardService.getInvoiceCreationDate(uwItemId);

        // Assert
        assertNotNull(result);
        assertEquals(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(invoice.created)), ZoneOffset.UTC),
                     result);
    }

    @Test
    void getInvoiceCreationDateFailure() throws Exception {
        int uwItemId = 123;
        UwOrder uwOrder = new UwOrder();
        uwOrder.setUnicomOrderCode("UNICOM123");
        uwOrder.setFacilityCode("FAC001");

        InvoiceDetailsResponse invoiceDetailsResponse = new InvoiceDetailsResponse();
        InvoiceDetailsResponse.Invoice invoice = new InvoiceDetailsResponse.Invoice();
        invoice.created = String.valueOf(Instant.now().toEpochMilli());
        invoice.code = "INV123";
        ArrayList<InvoiceDetailsResponse.Invoice> invoices = new ArrayList<>();
        invoices.add(invoice);
        invoiceDetailsResponse.setInvoices(invoices);

        when(uwOrdersRepository.findByUwItemId(uwItemId)).thenThrow(RuntimeException.class);
        // Act
        LocalDateTime result = giftCardService.getInvoiceCreationDate(uwItemId);
    }

    @Test
    void getReturnCreationDateSuccess() {
        LocalDateTime createdAt = LocalDateTime.now();
        ReturnOrderItem returnOrderItem = new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        ReturnEinvoicing returnEinvoicing = new ReturnEinvoicing();
        returnEinvoicing.setCreatedAt(createdAt);

        when(returnOrderItemRepository.findByUwItemId(anyInt())).thenReturn(returnOrderItem);
        when(returnEinvoicingRepository.findByReturnId(anyInt())).thenReturn(returnEinvoicing);
        LocalDateTime returnCreationDate = giftCardService.getReturnCreationDate(1);
        assertEquals(createdAt, returnCreationDate);
    }

    @Test
    void getReturnCreationDateFailure() {
        LocalDateTime createdAt = LocalDateTime.now();
        ReturnOrderItem returnOrderItem = new ReturnOrderItem();
        returnOrderItem.setReturnId(1);
        ReturnEinvoicing returnEinvoicing = new ReturnEinvoicing();
        returnEinvoicing.setCreatedAt(createdAt);

        when(returnOrderItemRepository.findByUwItemId(anyInt())).thenReturn(returnOrderItem);
        when(returnEinvoicingRepository.findByReturnId(anyInt())).thenThrow(RuntimeException.class);
        LocalDateTime returnCreationDate = giftCardService.getReturnCreationDate(1);
    }

    @Test
    void getJournalLinesSuccessSCM() {
        GiftCard giftCard = new GiftCard();
        giftCard.setUwItemId(1);
        giftCard.setId(100L);

        GiftCardMetaData giftCardMetaData = new GiftCardMetaData();
        giftCardMetaData.setMagentoId(1);
        giftCardMetaData.setExpenseAmount(1.00d);
        giftCardMetaData.setExpenseReversal(1.00d);
        giftCardMetaData.setId(1L);
        giftCardMetaData.setReferenceId(100L);

        LocalDateTime createdAt = LocalDateTime.now();
        doReturn(createdAt).when(giftCardService).getInvoiceCreationDate(anyInt());
        List<JournalLineDto> response = giftCardService.getJournalLines(Arrays.asList(giftCard),
                                                                        Arrays.asList(giftCardMetaData),
                                                                        "SCM");
    }

    @Test
    void getJournalLinesSuccessReturn() {
        GiftCard giftCard = new GiftCard();
        giftCard.setUwItemId(1);
        giftCard.setId(100L);

        GiftCardMetaData giftCardMetaData = new GiftCardMetaData();
        giftCardMetaData.setMagentoId(1);
        giftCardMetaData.setExpenseAmount(1.00d);
        giftCardMetaData.setExpenseReversal(1.00d);
        giftCardMetaData.setId(1L);
        giftCardMetaData.setReferenceId(100L);

        LocalDateTime createdAt = LocalDateTime.now();
        doReturn(createdAt).when(giftCardService).getReturnCreationDate(anyInt());
        List<JournalLineDto> response = giftCardService.getJournalLines(Arrays.asList(giftCard),
                                                                        Arrays.asList(giftCardMetaData),
                                                                        "RETURN");
    }

    @Test
    void getJournalLinesFailure() {
        GiftCard giftCard = new GiftCard();
        giftCard.setUwItemId(1);
        giftCard.setId(100L);

        GiftCardMetaData giftCardMetaData = new GiftCardMetaData();
        giftCardMetaData.setMagentoId(1);
        giftCardMetaData.setId(1L);

        assertThrows(Exception.class, () -> {
            giftCardService.getJournalLines(Arrays.asList(giftCard),
                                            Arrays.asList(giftCardMetaData),
                                            "SCM");
        });
    }

}