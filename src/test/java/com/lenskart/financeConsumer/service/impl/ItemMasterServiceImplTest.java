package com.lenskart.financeConsumer.service.impl;

import com.lenskart.core.model.Product;
import com.lenskart.financeConsumer.constant.Common;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemSyncDto;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.financeDb.writeRepository.ForwardSourceSystemSyncRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.ItemD365SyncRepository;
import com.lenskart.financeConsumer.model.finance.ForwardSourceSystemSync;
import com.lenskart.financeConsumer.model.financeDb.ItemD365Sync;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;

import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ItemMasterServiceImplTest {

    @Spy
    @InjectMocks
    ItemMasterServiceImpl itemMasterService;
    @Mock
    GenericClientService genericClientService;
    @Mock
    FinanceSourceSystemToItemD365SyncDataSyncService financeSourceSystemToItemD365SyncDataSyncService;

    @Mock
    ForwardSourceSystemSyncRepository forwardSourceSystemSyncRepository;

    @Mock
    ProductService productService;
    @Mock
    ItemD365SyncRepository itemD365SyncRepository;
    static FinanceSourceSystemSyncDto financeSystemSync;

    @Mock
    ItemD365RepositoryService itemD365RepositoryService;

     @BeforeAll
     static void init(){
         financeSystemSync =new FinanceSourceSystemSyncDto();
         financeSystemSync.setId(1L);
         financeSystemSync.setEvent(FinanceSourceSystemSyncEvent.ITEM_MASTER);
         financeSystemSync.setEntityType(FinanceSourceSystemSyncEntityType.PRODUCT_ID);
         financeSystemSync.setEntityId("1222");
         financeSystemSync.setPayload("1222");
         financeSystemSync.setSyncedToFinanceSystem(Boolean.FALSE);
         financeSystemSync.setRetryCount(0);
         financeSystemSync.setEventTime(new Date());
         financeSystemSync.setCreatedAt(new Date());
         financeSystemSync.setUpdatedAt(new Date());
         financeSystemSync.setCreatedBy("product-create");
         financeSystemSync.setUpdatedBy("product-create");
     }

    void syncToD365NotPresentInForwardSyncTable() throws RecordNotFoundException {
        Integer productId = 101;
        Product product =null;
        HashMap<String,String> d365Response = new HashMap<>();
        d365Response.put("Message","Created Successfully!");
        d365Response.put("Success","true");

        ForwardSourceSystemSync forwardSourceSystemSync = ForwardSourceSystemSync.builder()
                .id(1211L).
                referenceId(0L).
                event(FinanceSourceSystemSyncEvent.ITEM_MASTER).
                entityType(FinanceSourceSystemSyncEntityType.PRODUCT_ID).
                entityId(productId.toString()).
                payloadJson(productId.toString()).
                d365SyncStatus(Common.CREATED).
                createdAt(new Date()).
                createdBy(Constants.Common.financeConsumer).
                updatedAt(new Date()).
                updatedBy(Constants.Common.financeConsumer).
                eventTime(new Date()).build();
        Mockito.when(productService.getProduct(Mockito.anyInt())).thenReturn(product);
        Mockito.when(forwardSourceSystemSyncRepository.save(Mockito.any())).thenReturn(forwardSourceSystemSync);
        Mockito.when(genericClientService.forwardRequest(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ResponseEntity(d365Response, HttpStatus.OK));

        ResponseEntity responseFromD365 =  itemMasterService.syncToD365(productId);
        HashMap responseBody = (HashMap) responseFromD365.getBody();
        String message = (String) responseBody.get("Message");
        assertEquals("Created Successfully!",message);
    }



    @Test
    void persistAndGetIdFromD365ForwardSyncTableForProduct(){
         when(itemD365RepositoryService.findItemByProductId(anyInt())).thenReturn(null);
         ItemD365Sync itemD365Sync =new ItemD365Sync();
         itemD365Sync.setId(1L);
         when(itemD365RepositoryService.save(any())).thenReturn(itemD365Sync);
         long id =ReflectionTestUtils.invokeMethod(itemMasterService,"persisteAndGetIdFromD365ForwardSyncTableForProduct",1);
         assertEquals(1L,id);
    }

    @Test
    void syncToD365WithNullProductId() {
        Integer productId = null;
        ResponseEntity responseEntity = itemMasterService.syncToD365(productId);
        assertEquals("null productId cannot be processed!",responseEntity.getBody().toString());
    }

    @Test
    void persistItemAndSyncToD365ItemDoesntExistAlready(){

         ItemD365Sync itemD365Sync =new ItemD365Sync();

        try(MockedStatic dateUtilsMockedStatic = mockStatic(DateUtils.class)) {
            when(DateUtils.getLocalDateObjectFromDate(any())).thenReturn(LocalDateTime.now());
            when(itemD365RepositoryService.findItemByProductId(anyInt())).thenReturn(null);
            when(itemD365RepositoryService.save(any())).thenReturn(itemD365Sync);
            doReturn(new ResponseEntity<>("", HttpStatus.OK)).when(itemMasterService).syncToD365(anyInt());
            itemMasterService.persistItemAndSyncToD365(financeSystemSync);
            ReflectionTestUtils.invokeMethod(itemMasterService, "generateItemD365DtoFromFinanceSourceSystemDto", financeSystemSync);
            verify(itemD365RepositoryService, times(1)).save(any());
        }
    }

    @Test
    void persistItemAndSyncToD365ItemExistAlready(){

              ItemD365Sync itemD365Sync =new ItemD365Sync();
             when(itemD365RepositoryService.findItemByProductId(anyInt())).thenReturn(new ItemD365Sync());
//        when(itemD365SyncRepository.save(any())).thenReturn(itemD365Sync);
             doReturn(new ResponseEntity<>("",HttpStatus.OK)).when(itemMasterService).syncToD365(anyInt());
             itemMasterService.persistItemAndSyncToD365(financeSystemSync);
             verify(itemD365RepositoryService,times(1)).findItemByProductId(anyInt());
    }

    @Test
    void generateItemD365DtoFromFinanceSourceSystemSync(){
        FinanceSourceSystemSync financeSourceSystemSync1 = new FinanceSourceSystemSync();

        financeSourceSystemSync1.setId(1L);
        financeSourceSystemSync1.setEvent(FinanceSourceSystemSyncEvent.ITEM_MASTER);
        financeSourceSystemSync1.setEntityType(FinanceSourceSystemSyncEntityType.PRODUCT_ID);
        financeSourceSystemSync1.setEntityId(String.valueOf("1222"));
        financeSourceSystemSync1.setPayload(String.valueOf("1222"));
        financeSourceSystemSync1.setSyncedToFinanceSystem(Boolean.FALSE);
        financeSourceSystemSync1.setRetryCount(0);
        financeSourceSystemSync1.setEventTime(new Date());
        financeSourceSystemSync1.setCreatedAt(new Date());
        financeSourceSystemSync1.setUpdatedAt(new Date());
        financeSourceSystemSync1.setCreatedBy("product-create");
        financeSourceSystemSync1.setUpdatedBy("product-create");
        ItemD365Sync itemD365Sync =  itemMasterService.generateItemD365DtoFromFinanceSourceSystemSync(financeSourceSystemSync1);
        assertEquals(Integer.parseInt(financeSourceSystemSync1.getEntityId()),itemD365Sync.getProductId());

    }

    @Test
    void saveD365ResponseToItemD365Sync(){
        ItemD365Sync itemD365Sync =new ItemD365Sync();
        when(itemD365RepositoryService.findItemByProductId(anyInt())).thenReturn(itemD365Sync);
        itemMasterService.saveD365ResponseToItemD365Sync(financeSystemSync);
        verify(itemD365RepositoryService,times(1)).save(any());


    }
}