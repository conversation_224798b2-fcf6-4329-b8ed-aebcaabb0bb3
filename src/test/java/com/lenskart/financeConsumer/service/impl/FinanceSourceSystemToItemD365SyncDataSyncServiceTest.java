package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.model.financeDb.ItemD365Sync;
import com.lenskart.financeConsumer.service.ItemMasterService;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FinanceSourceSystemToItemD365SyncDataSyncServiceTest {

    @Mock
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @Mock
    private DateUtils dateUtils;

    @Mock
    private ItemMasterService itemMasterService;

    @Mock
    private ItemD365RepositoryService itemD365RepositoryService;

    @Spy
    @InjectMocks
    private FinanceSourceSystemToItemD365SyncDataSyncService syncService;

    private Calendar fromTime;
    private Calendar toTime;
    private Calendar fromTimeForSyncedEvents;

    @BeforeEach
    public void setUp() {
        fromTime = Calendar.getInstance();
        toTime = Calendar.getInstance();
        fromTimeForSyncedEvents = Calendar.getInstance();
    }

    @Test
    public void testSyncFinanceSourceSystemItemEventToFinanaceDB_NoUnsyncedData() {

        when(dateUtils.getDateFromCalendar(any(Calendar.class))).thenReturn(new Date());
        when(itemD365RepositoryService.fetchSetOfReferenceIdsFromDate(any(Date.class))).thenReturn(new HashSet<>());


        syncService.SyncFinanceSourceSystemItemEventToFinanaceDB(fromTime, toTime, fromTimeForSyncedEvents);


        verify(dateUtils, times(3)).getDateFromCalendar(any(Calendar.class)); // 3 times: fromDate, toDate, fromDateForSyncedEvents
        verify(itemD365RepositoryService, times(1)).fetchSetOfReferenceIdsFromDate(any(Date.class));
        verifyNoInteractions(financeSystemSyncRepository); // No interactions with financeSystemSyncRepository
    }

    @Test
    public void testSyncFinanceSourceSystemItemEventToFinanaceDB_WithUnsyncedData() {

        Date fromDate = new Date();
        Date toDate = new Date();
        Date fromDateForSyncedEvents = new Date();
        when(dateUtils.getDateFromCalendar(any(Calendar.class))).thenReturn(fromDate, toDate, fromDateForSyncedEvents);

        Set<Long> mockReferenceIds = new HashSet<>(Arrays.asList(1L, 2L));
        when(itemD365RepositoryService.fetchSetOfReferenceIdsFromDate(any(Date.class))).thenReturn((HashSet<Long>) mockReferenceIds);

        List<FinanceSourceSystemSync> mockUnsyncedData = createMockUnsyncedData();
        when(financeSystemSyncRepository.findInventoryDataForTime(any(Date.class), any(Date.class), anyString(), any(Pageable.class)))
                .thenReturn(mockUnsyncedData).thenReturn(new ArrayList<>());

//        doNothing().when(syncService).extractAndSyncItemD365SyncData(any());


        syncService.SyncFinanceSourceSystemItemEventToFinanaceDB(fromTime, toTime, fromTimeForSyncedEvents);
        verify(financeSystemSyncRepository, atLeastOnce()).findInventoryDataForTime(any(Date.class), any(Date.class), eq("ITEM_MASTER"), any(Pageable.class));
    }

    @Test
    public void testExtractAndSyncItemD365SyncData() {

        FinanceSourceSystemSync mockFinanceSourceSystemSync = new FinanceSourceSystemSync();
        mockFinanceSourceSystemSync.setId(1L);
        mockFinanceSourceSystemSync.setEntityId("1222");

        ItemD365Sync mockItemD365Sync = new ItemD365Sync();
        mockItemD365Sync.setId(1L);
        mockItemD365Sync.setProductId(1222);

        when(itemMasterService.generateItemD365DtoFromFinanceSourceSystemSync(any())).thenReturn(mockItemD365Sync);
        when(itemD365RepositoryService.save(any())).thenReturn(new ItemD365Sync());

        syncService.extractAndSyncItemD365SyncData(mockFinanceSourceSystemSync);

        verify(itemMasterService, times(1)).generateItemD365DtoFromFinanceSourceSystemSync(mockFinanceSourceSystemSync);
        verify(itemD365RepositoryService, times(1)).save(mockItemD365Sync);
    }



    @Test
    public void testIsAlreadySynced_True() {
        HashSet<Long> setOfReferenceIds = new HashSet<>(Arrays.asList(1L, 2L, 3L));

        boolean result = syncService.isAlreadySynced(2L, setOfReferenceIds);

        assert(result);
    }

    @Test
    public void testIsAlreadySynced_False() {
        HashSet<Long> setOfReferenceIds = new HashSet<>(Arrays.asList(1L, 2L, 3L));

        boolean result = syncService.isAlreadySynced(4L, setOfReferenceIds);

        assert(!result);
    }

    private List<FinanceSourceSystemSync> createMockUnsyncedData() {
        FinanceSourceSystemSync data1 = new FinanceSourceSystemSync();
        data1.setId(1L);
        data1.setEntityId("ENTITY001");

        FinanceSourceSystemSync data2 = new FinanceSourceSystemSync();
        data2.setId(2L);
        data2.setEntityId("ENTITY002");

        return Arrays.asList(data1, data2);
    }
}
