package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.financeDb.writeRepository.ItemD365SyncRepository;
import com.lenskart.financeConsumer.model.financeDb.ItemD365Sync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ItemD365RepositoryServiceTest {

    @Mock
    private ItemD365SyncRepository itemD365SyncRepository;
    @InjectMocks
    ItemD365RepositoryService itemD365RepositoryService;
    @BeforeEach
    void setUp() {
    }

    @Test
    void save() {
        ItemD365Sync itemD365Sync =new ItemD365Sync();
        itemD365Sync.setProductId(121);
        when(itemD365SyncRepository.save(any())).thenReturn(itemD365Sync);
        assertEquals(121,itemD365RepositoryService.save(new ItemD365Sync()).getProductId()) ;


    }

    @Test
    void findItemByProductId() {
        ItemD365Sync itemD365Sync =new ItemD365Sync();
        itemD365Sync.setProductId(121);
        when(itemD365SyncRepository.findByProductId(anyInt())).thenReturn(itemD365Sync);
        assertEquals(121,itemD365RepositoryService.findItemByProductId(121).getProductId()) ;

    }

    @Test
    public void testMarkSyncAsFailure_ItemNotFound() {

        when(itemD365SyncRepository.findByProductId(anyInt())).thenReturn(null);
        itemD365RepositoryService.markSyncAsFailure("123", FinanceSourceSystemSyncEvent.ITEM_MASTER, FinanceSourceSystemSyncEntityType.PRODUCT_ID, "Test failure");

        verify(itemD365SyncRepository, times(1)).findByProductId(anyInt());
        verifyNoMoreInteractions(itemD365SyncRepository); // No further interactions expected
    }

    @Test
    public void testMarkSyncAsFailure_Success() {

        ItemD365Sync mockItemD365Sync = new ItemD365Sync();
        mockItemD365Sync.setId(1L);
        mockItemD365Sync.setProductId(122);
        when(itemD365SyncRepository.findByProductId(anyInt())).thenReturn(mockItemD365Sync);
        when(itemD365SyncRepository.save(any())).thenReturn(new ItemD365Sync());

        itemD365RepositoryService.markSyncAsFailure("123", FinanceSourceSystemSyncEvent.ITEM_MASTER, FinanceSourceSystemSyncEntityType.PRODUCT_ID, "Test failure");


        verify(itemD365SyncRepository, times(1)).findByProductId(anyInt());
        verify(itemD365SyncRepository, times(1)).save(mockItemD365Sync);
    }

    @Test
    void findFailedItemRecords() {
        when(itemD365SyncRepository.findFailedItemRecords(anyList(),anyInt(),anyInt(),any(LocalDateTime.class))).thenReturn(new ArrayList<>());
        itemD365RepositoryService.findFailedItemRecords(Arrays.asList("Failure"),1,1, LocalDateTime.now());
    }

    @Test
    void findNonsyncedItems() {
        when(itemD365SyncRepository.findNonsyncedItems(any(),any())).thenReturn(new ArrayList<>());
        itemD365RepositoryService.findNonsyncedItems("",PageRequest.of(1,1));

    }
}