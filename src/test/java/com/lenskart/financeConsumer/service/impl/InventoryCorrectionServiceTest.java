package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dto.InventoryCorrectionRequest;
import com.lenskart.financeConsumer.dto.InventoryCorrectionResponse;
import com.lenskart.financeConsumer.dto.d365requests.CostPriceResponseBody;
import com.lenskart.financeConsumer.financeDb.writeRepository.InventoryCorrectionWriteRepository;
import com.lenskart.financeConsumer.model.financeDb.InventoryCorrection;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.InventoryCorrectionService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.MovementType;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.D365SyncSubStatusEnum;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class InventoryCorrectionServiceTest {
    @Spy
    @InjectMocks
    private InventoryCorrectionService inventoryCorrectionService;

    @Mock
    private InventoryCorrectionWriteRepository inventoryCorrectionWriteRepository;

    @Mock
    Optional<InventoryCorrection > inventoryCorrectionMock;

    @Mock
    NexsClient nexsClient;

    @Mock
    private GenericClientService genericClientService;

    @Mock
    private KafkaTemplate kafkaProducerTemplate;

    @Mock
    private DateUtils dateUtils;

    @Mock
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    InventoryCorrection inventoryCorrection = new InventoryCorrection();
    Map<Pattern, String> patternMovementJournalFailureTypeMap;
    Map<MovementType, Integer> movementTypeQtyMap;

    @BeforeEach
    public void setup() {
        inventoryCorrectionService.init();

        patternMovementJournalFailureTypeMap = inventoryCorrectionService.getPatternMovementJournalFailureTypeMap();
        movementTypeQtyMap = inventoryCorrectionService.getMovementTypeQtyMap();

        Pattern barcodePattern = Pattern.compile("Serial number (\\S+)", Pattern.CASE_INSENSITIVE);
        ReflectionTestUtils.setField(inventoryCorrectionService, "barcodePattern", barcodePattern);

        Pattern pidPattern = Pattern.compile("item number (\\d+)", Pattern.CASE_INSENSITIVE);
        ReflectionTestUtils.setField(inventoryCorrectionService, "pidPattern", pidPattern);

        ReflectionTestUtils.setField(inventoryCorrectionService, "serialNumberKafkaTopic", "serialNumberKafkaTopic");

        inventoryCorrection.setBarcode("8967vgvi");
        inventoryCorrection.setD365Barcode("8967vgvi_1");
        inventoryCorrection.setProductId("85446546");
        inventoryCorrection.setMovementType(MovementType.NEG);
        inventoryCorrection.setEntityId("LKH03SP146837");
        inventoryCorrection.setFacilityCode("QNXS2");
        inventoryCorrection.setSource("SCM");
        inventoryCorrection.setRetryCount(0);
        inventoryCorrection.setLegalEntity("LKIN");
        inventoryCorrection.setTransactionDate(java.time.LocalDateTime.now());
        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setCreatedBy("finance-consumer");
        inventoryCorrection.setUpdatedBy("finance-consumer");

        ReflectionTestUtils.setField(inventoryCorrectionService, "maxCount", 5);
        ReflectionTestUtils.setField(inventoryCorrectionService, "lastHour", 5);
        ReflectionTestUtils.setField(inventoryCorrectionService, "lastMinutes", 5);
        ReflectionTestUtils.setField(inventoryCorrectionService, "pageLimit", 5);
        ReflectionTestUtils.setField(inventoryCorrectionService, "maxRetry", 5);
        ReflectionTestUtils.setField(inventoryCorrectionService, "dateUtils", dateUtils);
        ReflectionTestUtils.setField(inventoryCorrectionService,"dummyBarCodeSuffixValue","_1");
        ReflectionTestUtils.setField(inventoryCorrectionService,"maxSyncCount",20);
        ReflectionTestUtils.setField(inventoryCorrectionService,"dummyErrorMessageRegex","^Serial number");
        ReflectionTestUtils.setField(inventoryCorrectionService,"syncPageLimit",20);
        ReflectionTestUtils.setField(inventoryCorrectionService,"syncMaxRetry",20);
        ReflectionTestUtils.setField(inventoryCorrectionService,"returnDummyBarcodeSuffixLimit",3);

    }

    @Test
    void testMovementTypeQtyMapInitialization() {
        assertEquals(1, movementTypeQtyMap.get(MovementType.POS));
        assertEquals(-1, movementTypeQtyMap.get(MovementType.NEG));
    }

    @Test
    void isErrorStringValidForInventoryCorrection() {

        String errorString = "Serial number DYL000046647 has not been created for item number 70236090..";
        String return_errorString = "Serial number CCC050688737 already exists physically in the inventory..Operation has been cancelled.";

        assertTrue(StringUtils.isNotBlank(inventoryCorrectionService.extractBarCodeFromErrorString(return_errorString)));
        assertTrue(StringUtils.isNotBlank(inventoryCorrectionService.extractBarCodeFromErrorString(errorString)));
        assertTrue(StringUtils.isNotBlank(inventoryCorrectionService.extractProductIdFromErrorString(errorString)));
    }

    @Test
    void isErrorStringInValidForInventoryCorrection() {
        String errorString_scm = "Serial DYL000046647 has not been created for item number 70236090..";
        String errorString_return = "Serial CCC050688737 already exists physically in the inventory..Operation has been cancelled.";

        assertFalse(StringUtils.isNotBlank(inventoryCorrectionService.extractBarCodeFromErrorString(errorString_scm)));
        assertFalse(StringUtils.isNotBlank(inventoryCorrectionService.extractBarCodeFromErrorString(errorString_return)));
    }

    @Test
    void isValidSourcesForInventoryCorrection() {
        assertTrue(inventoryCorrectionService.isSourceValid("SCM"));
        assertTrue(inventoryCorrectionService.isSourceValid("RETURN"));
    }

    @Test
    void isInValidSourcesForInventoryCorrection() {
        assertFalse(inventoryCorrectionService.isSourceValid("SC"));
        assertFalse(inventoryCorrectionService.isSourceValid("RE"));
    }

    @Test
    void isEmptyValidSourcesForInventoryCorrection() {
        assertFalse(inventoryCorrectionService.isSourceValid(""));
    }

    @Test
    void persistInventoryCorrectionForSCM() {

        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(), any(), any(), any())).thenReturn((Optional.empty()));
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        InventoryCorrectionResponse result = inventoryCorrectionService.findOrCreateInventoryCorrection(inventoryCorrection.getEntityId(),
                inventoryCorrection.getFacilityCode(), inventoryCorrection.getProductId(), inventoryCorrection.getBarcode(), inventoryCorrection.getSource(),
                inventoryCorrection.getLegalEntity(), inventoryCorrection.getRetryCount(), inventoryCorrection.getTransactionDate());

        assertNotNull(result);
        assertEquals(true, result.getStatus());
        assertEquals("LKH03SP146837", ((InventoryCorrection) result.getData()).getEntityId());
        assertEquals(1, ((InventoryCorrection) result.getData()).getQty());
    }

    @Test
    public void testfindOrCreateInventoryCorrection(){

        InventoryCorrection obj = new InventoryCorrection();
        obj.setD365Barcode("barocode_1");
        obj.setBarcode("barocode");


        when(inventoryCorrectionMock.get()).thenReturn(new InventoryCorrection());
        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(),any(),any(),any()))
                .thenReturn(inventoryCorrectionMock);
        when(inventoryCorrectionMock.isPresent()).thenReturn(true);
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(obj);

        InventoryCorrectionResponse inventoryCorrectionReceived = inventoryCorrectionService.findOrCreateInventoryCorrection( "entityId", "facilityCode",
                "productId", "barCode_2", "RETURN",
                "legalEntity",3, LocalDateTime.now());

        assertEquals(true,inventoryCorrectionReceived.getStatus());

    }


    @Test
    void persistInventoryCorrectionForRETRUN() {
        inventoryCorrection.setQty(-1);
        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);

        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(), any(), any(), any())).thenReturn((Optional.empty()));
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrectionResponse result = inventoryCorrectionService.findOrCreateInventoryCorrection(inventoryCorrection.getEntityId(),
                inventoryCorrection.getFacilityCode(), inventoryCorrection.getProductId(), inventoryCorrection.getBarcode(), inventoryCorrection.getSource(),
                inventoryCorrection.getLegalEntity(), inventoryCorrection.getRetryCount(), inventoryCorrection.getTransactionDate());

        assertNotNull(result);
        assertEquals(true, result.getStatus());
        assertEquals("LKH03SP146837", ((InventoryCorrection) result.getData()).getEntityId());
        assertEquals(-1, ((InventoryCorrection) result.getData()).getQty());
    }

    @SneakyThrows
    @Test
    void fetchCostPrice() {
        CostPriceResponseBody costPriceResponseBody = new CostPriceResponseBody();
        costPriceResponseBody.setPrice("20");
        List<CostPriceResponseBody> costPriceResponseBodyList = Collections.singletonList(costPriceResponseBody);
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                costPriceResponseBodyList);

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.fetchCostPriceDetails(any(), any());
        Assertions.assertEquals(true, inventoryCorrectionResponse.getStatus());
        Assertions.assertEquals(20d, inventoryCorrectionResponse.getData());
    }

    @Test
    @SneakyThrows
    void fetchCostPriceWithException() {
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(),
                ArgumentMatchers.any())).thenThrow(new RuntimeException("Test exception"));

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.fetchCostPriceDetails(any(), any());

        Assertions.assertEquals(false, inventoryCorrectionResponse.getStatus());
        assertNull(inventoryCorrectionResponse.getData());

    }

    @SneakyThrows
    @Test
    void testFetchCostPriceWithEmptyResponse() {


        List<CostPriceResponseBody> costPriceResponseBodyList = Collections.emptyList();
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                costPriceResponseBodyList);

        String barCode = "12345";
        String facilityCode = "FAC001";
        InventoryCorrection inventoryCorrection = new InventoryCorrection();

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.addCostPrice(barCode, facilityCode, inventoryCorrection);
        Assertions.assertEquals(false, inventoryCorrectionResponse.getStatus());
        assertNull(inventoryCorrectionResponse.getData());
    }

    @Test
    void generateAndProcessMovementJournalForValidRequest() {

        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        HashMap responseMap = new HashMap();
        responseMap.put("Message", "sycned successfully");
        responseMap.put("Success", true);

        ResponseEntity responseEntity = new ResponseEntity(responseMap, HttpStatus.OK);
        when(genericClientService.forwardRequest(anyString(), any(), any(), any())).thenReturn(responseEntity);
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrection result = inventoryCorrectionService.generateAndProcessMovementJournal(inventoryCorrection);
        assertNotNull(result);
        assertEquals("Success", result.getD365SyncStatus());
    }

    @Test
    void generateAndProcessMovementJournalFailed() {

        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        HashMap responseMap = new HashMap();
        responseMap.put("Message", "sycned successfully");
        responseMap.put("Success", true);

        ResponseEntity responseEntity = new ResponseEntity(responseMap, HttpStatus.BAD_GATEWAY);
        when(genericClientService.forwardRequest(anyString(), any(), any(), any())).thenReturn(responseEntity);
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrection result = inventoryCorrectionService.generateAndProcessMovementJournal(inventoryCorrection);
        assertNotNull(result);
        assertEquals("Failure", result.getD365SyncStatus());
    }

    @Test
    void testGenerateAndProcessMovementJournalAlreadyProcessed() {

        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        HashMap responseMap = new HashMap();
        responseMap.put("Message", "The document no POS_LKH03SP146710_137935_LKH0323SI20802255_54 is  already exists.");
        responseMap.put("Success", false);

        ResponseEntity responseEntity = new ResponseEntity(responseMap, HttpStatus.OK);
        when(genericClientService.forwardRequest(anyString(), any(), any(), any())).thenReturn(responseEntity);

        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrection result = inventoryCorrectionService.generateAndProcessMovementJournal(inventoryCorrection);
        assertNotNull(result);
        assertEquals("Success", result.getD365SyncStatus());
    }

    @Test
    void testGenerateAndProcessMovementJournalExceptionHandling() {

        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        HashMap responseMap = new HashMap();
        responseMap.put("Message", "sycned successfully");
        responseMap.put("Success", true);

        ResponseEntity responseEntity = new ResponseEntity(responseMap, HttpStatus.INTERNAL_SERVER_ERROR);
        when(genericClientService.forwardRequest(anyString(), any(), any(), any())).
                thenThrow(new RuntimeException("Unknown Error Occurred"));

        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrection result = inventoryCorrectionService.generateAndProcessMovementJournal(inventoryCorrection);
        assertNotNull(result);
        assertEquals("Failure", result.getD365SyncStatus());
    }

    @SneakyThrows
    @Test
    void processMovementJournalForSerialNumberErrorForSCM() {
        String errorString_scm = "Serial number DYL000046647 has not been created for item number 70236090..";
        inventoryCorrection.setMovementType(MovementType.POS);
        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(), any(), any(), any())).thenReturn((Optional.empty()));
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        CostPriceResponseBody costPriceResponseBody = new CostPriceResponseBody();
        costPriceResponseBody.setPrice("20");
        List<CostPriceResponseBody> costPriceResponseBodyList = Collections.singletonList(costPriceResponseBody);
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                costPriceResponseBodyList);

        HashMap responseMap = new HashMap();
        responseMap.put("Message", "sycned successfully");
        responseMap.put("Success", true);

        ResponseEntity responseEntity = new ResponseEntity(responseMap, HttpStatus.OK);
        when(genericClientService.forwardRequest(anyString(), any(), any(), any())).thenReturn(responseEntity);
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.processMovementJournalForSerialNumberError(
                errorString_scm, inventoryCorrection.getEntityId(), inventoryCorrection.getFacilityCode(), java.time.LocalDateTime.now(), inventoryCorrection.getLegalEntity(), "70236090", "SCM");

        assertNotNull(inventoryCorrectionResponse);
        assertEquals(true, inventoryCorrectionResponse.getStatus());
        assertEquals(20d, ((InventoryCorrection) inventoryCorrectionResponse.getData()).getCostPrice());
        assertEquals("Success", ((InventoryCorrection) inventoryCorrectionResponse.getData()).getD365SyncStatus());
    }


    @SneakyThrows
    @Test
    void processMovementJournalForSerialNumberErrorFailedForSCM() {
        String errorString_scm = "Serial number DYL000046647 has not been created for item number 70236090..";
        inventoryCorrection.setMovementType(MovementType.POS);
        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(), any(), any(), any())).thenReturn((Optional.empty()));
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        CostPriceResponseBody costPriceResponseBody = new CostPriceResponseBody();
        costPriceResponseBody.setPrice("20");
        List<CostPriceResponseBody> costPriceResponseBodyList = Collections.singletonList(costPriceResponseBody);
        Mockito.when(nexsClient.getCostPrice(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(
                costPriceResponseBodyList);

        HashMap responseMap = new HashMap();
        responseMap.put("Message", "Something went wrong.");
        responseMap.put("Success", true);

        ResponseEntity responseEntity = new ResponseEntity(responseMap, HttpStatus.INTERNAL_SERVER_ERROR);
        when(genericClientService.forwardRequest(anyString(), any(), any(), any())).thenReturn(responseEntity);
        when(inventoryCorrectionWriteRepository.save(any())).thenReturn(inventoryCorrection);

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.processMovementJournalForSerialNumberError(
                errorString_scm, inventoryCorrection.getEntityId(), inventoryCorrection.getFacilityCode(), inventoryCorrection.getTransactionDate(), inventoryCorrection.getLegalEntity(), "70236090", "SCM");

        assertNotNull(inventoryCorrectionResponse);
        assertEquals(false, inventoryCorrectionResponse.getStatus());
        assertEquals("Something went wrong.", inventoryCorrectionResponse.getMessage());
    }

    @Test
    void processMovementJournalForSerialNumberErrorException() {
        String errorString_scm = "Serial number DYL000046647 has not been created for item number 70236090..";
        inventoryCorrection.setD365SyncStatus(Constants.Common.CREATED);
        inventoryCorrection.setQty(1);

        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(), any(), any(), any())).
                thenThrow(new RuntimeException("Exception occurred while persisting the inventoryCorrection."));

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.processMovementJournalForSerialNumberError(
                errorString_scm, inventoryCorrection.getEntityId(), inventoryCorrection.getFacilityCode(), inventoryCorrection.getTransactionDate(), inventoryCorrection.getLegalEntity(), "70236090", "SCM");

        assertNotNull(inventoryCorrectionResponse);
        assertEquals(false, inventoryCorrectionResponse.getStatus());
        assertEquals("Exception occurred while persisting the inventoryCorrection.", inventoryCorrectionResponse.getMessage());
    }
    @Test
    void testCaseProcessMovementJournalForSerialNumberError() {
        String errorString = "Serial number DYL000046647 has not been created for item number 70236090..";

        doReturn(null).when(inventoryCorrectionService).extractBarCodeFromErrorString(errorString);

        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.processMovementJournalForSerialNumberError(
                errorString, inventoryCorrection.getEntityId(), inventoryCorrection.getFacilityCode(), java.time.LocalDateTime.now(), inventoryCorrection.getLegalEntity(), "70236090", "SCM");

        assertNotNull(inventoryCorrectionResponse);
        assertFalse(inventoryCorrectionResponse.getStatus());

        verify(inventoryCorrectionService, times(1)).extractBarCodeFromErrorString(errorString);

    }

    @Test
    void testGenerateAndSaveInventoryCorrectionEntityWithValidInputs() {

        String barcode = "8967vgvi";
        String productId = "85446546";
        String source = "SCM";
        String entityId = "LKH03SP146837";
        String facilityCode = "QNXS2";
        java.time.LocalDateTime eventTime = java.time.LocalDateTime.now();
        int retryCount = 0;
        String legalEntity = "LKIN";

        Mockito.when(inventoryCorrectionWriteRepository.save(any(InventoryCorrection.class))).thenReturn(new InventoryCorrection());


        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.generateAndSaveInventoryCorrectionEntity(
                barcode, productId, source, entityId, facilityCode, eventTime, retryCount, legalEntity);

        Assertions.assertEquals(true, inventoryCorrectionResponse.getStatus());
        assertNotNull(inventoryCorrectionResponse.getData());
        Assertions.assertNotNull(inventoryCorrectionResponse);
        Assertions.assertNull(inventoryCorrectionResponse.getMessage());
        verify(inventoryCorrectionWriteRepository, times(1)).save(any(InventoryCorrection.class));
    }

    @Test
    void testGenerateAndSaveInventoryCorrectionEntityWithInValidInputs() {

        String barcode = "8967vgvi";
        String productId = "85446546";
        String source = "SC";
        String entityId = "LKH03SP146837";
        String facilityCode = "QNXS2";
        java.time.LocalDateTime eventTime = java.time.LocalDateTime.now();
        int retryCount = 0;
        String legalEntity = "LKIN";

        Mockito.when(inventoryCorrectionWriteRepository.save(any(InventoryCorrection.class))).
                thenThrow(new RuntimeException("Exception occurred while persisting the inventoryCorrection."));


        InventoryCorrectionResponse inventoryCorrectionResponse = inventoryCorrectionService.generateAndSaveInventoryCorrectionEntity(
                barcode, productId, source, entityId, facilityCode, eventTime, retryCount, legalEntity);

        Assertions.assertEquals(false, inventoryCorrectionResponse.getStatus());
        assertNull(inventoryCorrectionResponse.getData());
        assertNotNull(inventoryCorrectionResponse);
        Assertions.assertNotNull(inventoryCorrectionResponse.getMessage());
        verify(inventoryCorrectionWriteRepository, times(1)).save(any(InventoryCorrection.class));
    }

    @Test
    void testInventoryCorrectionPersistenceWhenAlreadySuccessful() {

        inventoryCorrection.setD365SyncStatus(Constants.Common.SUCCESS);
        when(inventoryCorrectionWriteRepository.findByEntityIdAndFacilityCodeAndProductIdAndBarcode(any(), any(), any(), any())).thenReturn((Optional.of(inventoryCorrection)));

        InventoryCorrectionResponse result = inventoryCorrectionService.findOrCreateInventoryCorrection(inventoryCorrection.getEntityId(),
                inventoryCorrection.getFacilityCode(), inventoryCorrection.getProductId(), inventoryCorrection.getBarcode(), inventoryCorrection.getSource(),
                inventoryCorrection.getLegalEntity(), inventoryCorrection.getRetryCount(), inventoryCorrection.getTransactionDate());

        assertNotNull(result);
        assertEquals(true, result.getStatus());
        assertEquals("LKH03SP146837", ((InventoryCorrection) result.getData()).getEntityId());
    }

    @Test
    void testProcessFailedMovementJournalEntriesValidEntry() {
        List<InventoryCorrection> failedEntries = Arrays.asList(inventoryCorrection);
        inventoryCorrectionService.processFailedMovementJournalEntries(failedEntries);
        verify(kafkaProducerTemplate, times(1)).send(eq("serialNumberKafkaTopic"), eq(inventoryCorrection.getEntityId()), anyString());
    }

    @Test
    void testProcessFailedMovementJournalEntriesErrorHandling() {
        List<InventoryCorrection> failedEntries = Arrays.asList(inventoryCorrection);
        doThrow(new RuntimeException("Kafka error"))
                .when(kafkaProducerTemplate)
                .send(anyString(), anyString(), anyString());
        inventoryCorrectionService.processFailedMovementJournalEntries(failedEntries);
    }

    @Test
    void testRetryFailureMovementJournalRecordsSuccess() {
        Calendar calendar = Calendar.getInstance();
        when(dateUtils.getPastCalendarDate(anyInt(), anyInt())).thenReturn(calendar);

        InventoryCorrection existedFailedinventoryCorrection = InventoryCorrection.builder()
                .id(1l)
                .barcode("TST000545464")
                .movementType(MovementType.POS)
                .productId("85446546")
                .build();
        existedFailedinventoryCorrection.setD365SyncStatus(Constants.Common.FAILURE);
        InventoryCorrection existedFailedInventoryCorrectionEntity = InventoryCorrection.builder()
                .id(2l)
                .barcode("TST000545464")
                .movementType(MovementType.POS)
                .productId("85446546")
                .build();
        InventoryCorrection existedFailedInventoryCorrection = InventoryCorrection.builder()
                .id(2l)
                .barcode("TST000545464")
                .movementType(MovementType.NEG)
                .productId("85446546")
                .build();
        existedFailedInventoryCorrectionEntity.setD365SyncStatus(Constants.Common.FAILURE);

        List<InventoryCorrection> failedInventoryCorrectionList =
                Arrays.asList(existedFailedinventoryCorrection, existedFailedInventoryCorrectionEntity, existedFailedInventoryCorrection);

        when(inventoryCorrectionWriteRepository.findEligibleFailureEvent(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        )).thenReturn(failedInventoryCorrectionList);

        inventoryCorrectionService.retryFailureMovementJournalRecords("SCM");
        verify(inventoryCorrectionWriteRepository, times(2)).findEligibleFailureEvent(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        );
    }

    @Test
    void testRetryFailureMovementJournalRecordsErrorHandling() {
        Calendar calendar = Calendar.getInstance();
        when(dateUtils.getPastCalendarDate(anyInt(), anyInt())).thenReturn(calendar);

        when(inventoryCorrectionWriteRepository.findEligibleFailureEvent(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        )).thenThrow(new RuntimeException("Connection Not available in the pool."));
        inventoryCorrectionService.retryFailureMovementJournalRecords("SCM");
        verify(inventoryCorrectionWriteRepository, times(1)).findEligibleFailureEvent(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        );
    }

    @Test
    void testCaseRetryFailureMovementJournalRecordsEmptyResult() {
        Calendar calendar = Calendar.getInstance();
        when(dateUtils.getPastCalendarDate(anyInt(), anyInt())).thenReturn(calendar);
        when(inventoryCorrectionWriteRepository.findEligibleFailureEvent(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        )).thenReturn(new ArrayList<>());
        inventoryCorrectionService.retryFailureMovementJournalRecords("SCM");
        verify(inventoryCorrectionWriteRepository, times(1)).findEligibleFailureEvent(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        );
    }


    @Test
    void testGetFailedMovementJournalDetails() {
        InventoryCorrection inventoryCorrectionFailed = new InventoryCorrection();
        inventoryCorrectionFailed.setId(1L);

        InventoryCorrection inventoryCorrectionFailedEntity = new InventoryCorrection();
        inventoryCorrectionFailedEntity.setId(2L);

        List<InventoryCorrection> inventoryCorrectionList = new ArrayList<>();
        inventoryCorrectionList.add(inventoryCorrectionFailed);
        inventoryCorrectionList.add(inventoryCorrectionFailedEntity);

        when(inventoryCorrectionWriteRepository.findEligibleFailureEvent(
                eq(Constants.Common.FAILURE),
                eq(MovementType.POS.name()),
                any(),
                any(),
                any(),
                anyInt(),
                any(Pageable.class)
        )).thenReturn(inventoryCorrectionList);

        List<InventoryCorrection> result = inventoryCorrectionService.getFailedMovementJournalDetails(MovementType.POS.name(), new Date(), new Date(), 0);

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(inventoryCorrectionWriteRepository, times(1)).findEligibleFailureEvent(
                eq(Constants.Common.FAILURE),
                eq(MovementType.POS.name()),
                any(),
                any(),
                any(),
                anyInt(),
                any(Pageable.class)
        );
    }


    @Test
    void shouldReturnErrorWhenBothIdsAndDateRangeAreProvided() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        Set<Long> ids= new HashSet<>();
        ids.add(3965L);
        ids.add(3966L);
        inventoryCorrectionRequest.setIds(ids);
        inventoryCorrectionRequest.setFromDate(new Date());
        inventoryCorrectionRequest.setToDate(new Date());

        InventoryCorrectionResponse response = inventoryCorrectionService.syncInventoryCorrection(inventoryCorrectionRequest);
        assertNotNull(response);
        assertFalse(response.getStatus());
        assertEquals("Both IDs and Date Range are provided. Please provide only one of them",response.getMessage());
    }

    @Test
    void shouldReturnErrorWhenFromDateIsMissing() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        inventoryCorrectionRequest.setIds(null);
        inventoryCorrectionRequest.setFromDate(null);
        inventoryCorrectionRequest.setToDate(new Date());
        InventoryCorrectionResponse response = inventoryCorrectionService.syncInventoryCorrection(inventoryCorrectionRequest);

        assertNotNull(response);
        assertFalse(response.getStatus());
        assertEquals("fromDate is missing. Please provide fromDate to complete the date range.",response.getMessage());
    }

    @Test
    void shouldReturnErrorWhenToDateIsMissing() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        inventoryCorrectionRequest.setIds(null);
        inventoryCorrectionRequest.setFromDate(new Date());
        inventoryCorrectionRequest.setToDate(null);
        InventoryCorrectionResponse response = inventoryCorrectionService.syncInventoryCorrection(inventoryCorrectionRequest);

        assertNotNull(response);
        assertFalse(response.getStatus());
        assertEquals("toDate is missing. Please provide toDate to complete the date range.",response.getMessage());
    }

    @Test
    void shouldReturnErrorWhenNeitherIdsNorDateRangeAreProvided() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        inventoryCorrectionRequest.setIds(null);
        inventoryCorrectionRequest.setFromDate(null);
        inventoryCorrectionRequest.setToDate(null);
        InventoryCorrectionResponse response = inventoryCorrectionService.syncInventoryCorrection(inventoryCorrectionRequest);
        assertNotNull(response);
        assertFalse(response.getStatus());
        assertEquals("Neither IDs nor Date Range is provided. Please provide either IDs or a Date Range.",response.getMessage());
    }

    @Test
    void shouldSyncByDateRangeWhenNoIdsAreProvided() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        inventoryCorrectionRequest.setIds(null);
        inventoryCorrectionRequest.setFromDate(new Date());
        inventoryCorrectionRequest.setToDate(new Date());
        doNothing().when(inventoryCorrectionService).syncInventoryCorrectionByDateRange(inventoryCorrectionRequest);
        InventoryCorrectionResponse response = inventoryCorrectionService.syncInventoryCorrection(inventoryCorrectionRequest);
        assertNotNull(response);
        assertTrue(response.getStatus());
        assertEquals("Successfully completed",response.getMessage());
        verify(inventoryCorrectionService, times(1)).syncInventoryCorrectionByDateRange(inventoryCorrectionRequest);
    }

    @Test
    void shouldSyncByIdsWhenIdsAreProvided() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        Set<Long> ids= new HashSet<>();
        ids.add(3965L);
        ids.add(3966L);
        inventoryCorrectionRequest.setIds(ids);
        inventoryCorrectionRequest.setFromDate(null);
        inventoryCorrectionRequest.setToDate(null);

        doNothing().when(inventoryCorrectionService).syncInventoryCorrectionByIds(inventoryCorrectionRequest);

        InventoryCorrectionResponse response = inventoryCorrectionService.syncInventoryCorrection(inventoryCorrectionRequest);
        assertNotNull(response);
        assertTrue(response.getStatus());
        assertEquals("Successfully completed",response.getMessage());
        verify(inventoryCorrectionService, times(1)).syncInventoryCorrectionByIds(inventoryCorrectionRequest);
    }

    @Test
    void testCaseSyncInventoryCorrectionByIdsSuccessfully() {

        InventoryCorrectionRequest inventoryCorrectionRequest = new InventoryCorrectionRequest();
        Set<Long> ids = new HashSet<>();
        ids.add(3965L);
        ids.add(3966L);
        inventoryCorrectionRequest.setIds(ids);
        inventoryCorrectionRequest.setFromDate(null);
        inventoryCorrectionRequest.setToDate(null);

        List<FinanceSourceSystemSync> financeSourceSystemSyncList = new ArrayList<>();
        FinanceSourceSystemSync financeSourceSystemSync = new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setD365SyncStatus(Constants.Common.FAILURE);
        financeSourceSystemSync.setEvent(FinanceSourceSystemSyncEvent.PACKING_SLIP);
        financeSourceSystemSync.setD365SyncSubStatus(D365SyncSubStatusEnum.COMPLETED);
        financeSourceSystemSyncList.add(financeSourceSystemSync);

        Pageable pageable = PageRequest.of(0, 20, Sort.by("updated_at").ascending());

        when(financeSystemSyncRepository.findEligibleFailureEventsByIds(
                eq(Constants.Common.FAILURE),
                eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                eq(ids),
                eq(20),
                eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                eq("^Serial number"),
                eq(pageable)
        )).thenReturn(financeSourceSystemSyncList);

        doNothing().when(inventoryCorrectionService).processFinanceSourceSystem(anyList());

        inventoryCorrectionService.syncInventoryCorrectionByIds(inventoryCorrectionRequest);

        verify(financeSystemSyncRepository, times(1))
                .findEligibleFailureEventsByIds(
                        eq(Constants.Common.FAILURE),
                        eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                        eq(ids),
                        eq(20),
                        eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                        eq("^Serial number"),
                        eq(pageable)
                );

        verify(inventoryCorrectionService, times(1)).processFinanceSourceSystem(financeSourceSystemSyncList);
    }

    @Test
    void testCaseEmptyResultsWhenSyncingInventoryCorrectionByIds() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        Set<Long> ids = new HashSet<>(Arrays.asList(3965L, 3966L));
        inventoryCorrectionRequest.setIds(ids);
        List<FinanceSourceSystemSync> financeSourceSystemSyncList = new ArrayList<>();

        Pageable pageable = PageRequest.of(0, 20, Sort.by("updated_at").ascending());

        when(financeSystemSyncRepository.findEligibleFailureEventsByIds(
                eq(Constants.Common.FAILURE),
                eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                eq(ids),
                eq(20),
                eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                eq("^Serial number"),
                eq(pageable)
        )).thenReturn(financeSourceSystemSyncList);


        inventoryCorrectionService.syncInventoryCorrectionByIds(inventoryCorrectionRequest);

        verify(financeSystemSyncRepository, times(1))
                .findEligibleFailureEventsByIds(any(), any(), eq(ids), anyInt(), any(), anyString(), any());

    }

    @Test
    void testCaseExceptionWhenSyncingInventoryCorrectionByIds() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        Set<Long> ids = new HashSet<>(Arrays.asList(3965L, 3966L));
        inventoryCorrectionRequest.setIds(ids);

        Pageable pageable = PageRequest.of(0, 20, Sort.by("updated_at").ascending());

        when(financeSystemSyncRepository.findEligibleFailureEventsByIds(
                eq(Constants.Common.FAILURE),
                eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                eq(ids),
                eq(20),
                eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                eq("^Serial number"),
                eq(pageable)
        )).thenThrow(new RuntimeException("Connection closed"));

        inventoryCorrectionService.syncInventoryCorrectionByIds(inventoryCorrectionRequest);

        verify(financeSystemSyncRepository, times(1))
                .findEligibleFailureEventsByIds(any(), any(), eq(ids), anyInt(), any(), anyString(), any());
    }

    @Test
    void testCaseSyncInventoryCorrectionByDateRangeSuccessfully() {
        Date fxedDate= new Date();
        InventoryCorrectionRequest inventoryCorrectionRequest = new InventoryCorrectionRequest();
        inventoryCorrectionRequest.setFromDate(fxedDate);
        inventoryCorrectionRequest.setToDate( fxedDate);

        List<FinanceSourceSystemSync> financeSourceSystemSyncList = new ArrayList<>();
        FinanceSourceSystemSync financeSourceSystemSync = new FinanceSourceSystemSync();
        financeSourceSystemSync.setId(1L);
        financeSourceSystemSync.setD365SyncStatus(Constants.Common.FAILURE);
        financeSourceSystemSync.setEvent(FinanceSourceSystemSyncEvent.PACKING_SLIP);
        financeSourceSystemSync.setD365SyncSubStatus(D365SyncSubStatusEnum.COMPLETED);
        financeSourceSystemSyncList.add(financeSourceSystemSync);

        Pageable pageable = PageRequest.of(0, 20, Sort.by("updated_at").ascending());

        when(financeSystemSyncRepository.findEligibleFailureEventsByDateRange(
                eq(Constants.Common.FAILURE),
                eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                eq(inventoryCorrectionRequest.getFromDate()),
                eq(inventoryCorrectionRequest.getToDate()),
                eq(20),
                eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                eq("^Serial number"),
                eq(pageable)
        )).thenReturn(financeSourceSystemSyncList);

        doNothing().when(inventoryCorrectionService).processFinanceSourceSystem(anyList());

        inventoryCorrectionService.syncInventoryCorrectionByDateRange(inventoryCorrectionRequest);

        verify(financeSystemSyncRepository, times(1))
                .findEligibleFailureEventsByDateRange(
                        eq(Constants.Common.FAILURE),
                        eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                        eq(fxedDate),
                        eq(fxedDate),
                        eq(20),
                        eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                        eq("^Serial number"),
                        eq(pageable)
                );

        verify(inventoryCorrectionService, times(1)).processFinanceSourceSystem(financeSourceSystemSyncList);
    }

    @Test
    void testCaseEmptyResultsWhenSyncingInventoryCorrectionByDateRange() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        Set<Long> ids = new HashSet<>(Arrays.asList(3965L, 3966L));
        inventoryCorrectionRequest.setIds(ids);
        List<FinanceSourceSystemSync> financeSourceSystemSyncList = new ArrayList<>();

        Pageable pageable = PageRequest.of(0, 20, Sort.by("updated_at").ascending());

        when(financeSystemSyncRepository.findEligibleFailureEventsByDateRange(
                eq(Constants.Common.FAILURE),
                eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                eq(inventoryCorrectionRequest.getFromDate()),
                eq(inventoryCorrectionRequest.getToDate()),
                eq(20),
                eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                eq("^Serial number"),
                eq(pageable)
        )).thenReturn(financeSourceSystemSyncList);


        inventoryCorrectionService.syncInventoryCorrectionByDateRange(inventoryCorrectionRequest);

        verify(financeSystemSyncRepository, times(1))
                .findEligibleFailureEventsByDateRange(any(), any(), any(), any(), any(), any(), any(),any());

    }

    @Test
    void testCaseExceptionWhenSyncingInventoryCorrectionByDateRange() {
        InventoryCorrectionRequest inventoryCorrectionRequest= new InventoryCorrectionRequest();
        Set<Long> ids = new HashSet<>(Arrays.asList(3965L, 3966L));
        inventoryCorrectionRequest.setIds(ids);

        Pageable pageable = PageRequest.of(0, 20, Sort.by("updated_at").ascending());

        when(financeSystemSyncRepository.findEligibleFailureEventsByDateRange(
                eq(Constants.Common.FAILURE),
                eq(FinanceSourceSystemSyncEvent.PACKING_SLIP.name()),
                eq(inventoryCorrectionRequest.getFromDate()),
                eq(inventoryCorrectionRequest.getToDate()),
                eq(20),
                eq(D365SyncSubStatusEnum.IN_PROGRESS.name()),
                eq("^Serial number"),
                eq(pageable)
        )).thenThrow(new RuntimeException("Connection closed"));

        inventoryCorrectionService.syncInventoryCorrectionByDateRange(inventoryCorrectionRequest);

        verify(financeSystemSyncRepository, times(1))
                .findEligibleFailureEventsByDateRange(any(), any(), any(), any(), any(), any(), any(),any());
    }


    @Test
    public void testReturnPidCannotBeNullWhenSourceIsReturn() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "LKH03SP146845", "LKH03", LocalDateTime.now(), "LKIN", "", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("returnPid cannot be null when source is RETURN.", response.getMessage());
    }

    @Test
    public void testReturnPidShouldNotBeNullWhenSourceIsScm() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "LKH03SP146845", "LKH03", LocalDateTime.now(), "LKIN", "70236090", "SCM");

        assertTrue(response.getStatus());
    }

    @Test
    public void testFacilityCodeCannotBeNull() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "LKH03SP146845", "", LocalDateTime.now(), "LKIN", "70236090", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("FacilityCode cannot be null.", response.getMessage());
    }

    @Test
    public void testLegalEntityCannotBeNull() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "LKH03SP146845", "LKH03", LocalDateTime.now(), "", "70236090", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("LegalEntity cannot be null.", response.getMessage());
    }

    @Test
    public void testEventTimeCannotBeNull() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "LKH03SP146845", "LKH03", null, "LKIN", "70236090", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("EventTime cannot be null.", response.getMessage());
    }

    @Test
    public void testErrorStringCannotBeNull() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "", "LKH03SP146845", "LKH03", LocalDateTime.now(), "LKIN", "70236090", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("ErrorString cannot be null.", response.getMessage());
    }

    @Test
    public void testEntityIdCannotBeNull() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "", "LKH03", LocalDateTime.now(), "LKIN", "70236090", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("EntityId cannot be null.", response.getMessage());
    }

    @Test
    public void testInvalidErrorStringForSerialNumberError() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "SerialNumber has not been created for item number 70236090..", "LKH03SP146845", "LKH03", LocalDateTime.now(), "LKIN", "70236090", "RETURN");

        assertFalse(response.getStatus());
        assertEquals("Invalid ErrorString", response.getMessage());
    }

    @Test
    public void testValidationSuccess() {
        InventoryCorrectionResponse response = inventoryCorrectionService.isValidPayload(
                "Serial number DYL000046647 has not been created for item number 70236090..", "LKH03SP146845", "LKH03", LocalDateTime.now(), "LKIN", "70236090", "RETURN");

        assertTrue(response.getStatus());
        assertEquals("Validation successful.", response.getMessage());
    }


}



