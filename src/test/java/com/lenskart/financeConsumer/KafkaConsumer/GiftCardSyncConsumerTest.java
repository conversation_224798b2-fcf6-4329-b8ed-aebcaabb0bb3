package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardSyncDto;
import com.lenskart.financeConsumer.dto.d365requests.juno.giftCard.JunoGiftCardResponse;
import com.lenskart.financeConsumer.service.impl.GiftCardService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
class GiftCardSyncConsumerTest {


    @Mock
    GiftCardService giftCardService;

    @Mock
    ObjectMapper objectMapper;

    @InjectMocks
    GiftCardSyncConsumer giftCardSyncConsumer;

    @Mock
    Acknowledgment ack;

    @BeforeEach
    public void setup(){
        ReflectionTestUtils.setField(giftCardSyncConsumer,"isGiftCardEnabled",true);
    }
    @Test
    void persistAndSyncGiftCard() throws JsonProcessingException {

        GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                legalEntity("LKIN").
                shipmentId("SHIP").
                facilityCode("NXS1").
                uwItemIds(Arrays.asList(1,2)).
                build();
        Mockito.when(objectMapper.readValue("giftCard///", GiftCardSyncDto.class)).thenReturn(giftCardSyncDto);

        giftCardSyncConsumer.persistAndSyncGiftCard("giftCard///", ack);

    }
    @Test
    void persistAndSyncGiftCardEmptyMessage() throws JsonProcessingException {

        GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                legalEntity("LKIN").
                shipmentId("SHIP").
                facilityCode("NXS1").
                uwItemIds(Arrays.asList(1,2)).
                build();
     //   Mockito.when(objectMapper.readValue("giftCard///", GiftCardSyncDto.class)).thenReturn(giftCardSyncDto);

        giftCardSyncConsumer.persistAndSyncGiftCard("", ack);

    }


    @Test
    void persistAndSyncGiftCardNullcheckExceptionHandling() throws JsonProcessingException {

        GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                legalEntity("LKIN").
                shipmentId("SHIP").
                facilityCode("NXS1").
                uwItemIds(Arrays.asList(1,2)).
                build();
        Mockito.when(objectMapper.readValue("giftCard///", GiftCardSyncDto.class)).thenThrow(NullPointerException.class);

        assertThrows(RuntimeException.class, () -> giftCardSyncConsumer.persistAndSyncGiftCard("giftCard///", ack));
    }
}