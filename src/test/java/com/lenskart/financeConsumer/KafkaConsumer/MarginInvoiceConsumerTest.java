package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lenskart.financeConsumer.dto.MarginSyncDTO;
import com.lenskart.financeConsumer.dto.SoLine;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.MarginInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertNotNull;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Slf4j
class MarginInvoiceConsumerTest {

    @InjectMocks
    private MarginInvoiceConsumer marginInvoiceConsumer;

    @Mock
    private MarginInvoiceService marginInvoiceService;

    @Mock
    private GenericClientService genericClientService;

    @Mock
    private Acknowledgment acknowledgment;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testListenValidMessage() throws Exception {
        String message = "{ \"requestPayload\": { \"_contract\": { \"_salesOrderHeader\": { \"salesOrderNumber\": \"12345\" } } } }";

        MarginSyncDTO marginSyncDTO = new MarginSyncDTO();
        marginSyncDTO.setSalesOrderNumber("12345");

        doNothing().when(marginInvoiceService).d365MarginSync(any(MarginSyncDTO.class));

        marginInvoiceConsumer.listen(message, acknowledgment);

        verify(marginInvoiceService, times(1)).d365MarginSync(any(MarginSyncDTO.class));
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    public void testListenEmptyMessage() throws Exception {
        String message = "";

        marginInvoiceConsumer.listen(message, acknowledgment);

        verify(marginInvoiceService, never()).d365MarginSync(any(MarginSyncDTO.class));
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    public void testListenInvalidMessage() throws Exception {
        String message = "{ \"requestPayload\": { } }";

        marginInvoiceConsumer.listen(message, acknowledgment);

        verify(marginInvoiceService, never()).d365MarginSync(any(MarginSyncDTO.class));
        verify(acknowledgment, times(1)).acknowledge();
    }

    @Test
    public void testProcessMessageException() throws Exception {
        String message = "{ \"requestPayload\": { \"_contract\": { \"_salesOrderHeader\": { \"salesOrderNumber\": \"12345\" } } } }";

        ObjectMapper objectMapper = new ObjectMapper();
        MarginSyncDTO marginSyncDTO = new MarginSyncDTO();
        marginSyncDTO.setSalesOrderNumber("12345");

        doThrow(new RuntimeException("Test exception")).when(marginInvoiceService).d365MarginSync(any(MarginSyncDTO.class));

        marginInvoiceConsumer.processMessage(message);

        verify(marginInvoiceService, times(1)).d365MarginSync(any(MarginSyncDTO.class));
    }
}
