package com.lenskart.financeConsumer.KafkaConsumer;

import com.lenskart.financeConsumer.service.ItemMasterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.Acknowledgment;

@ExtendWith(MockitoExtension.class)
class ItemPersistAndSyncConsumerTest {

    @InjectMocks
    ItemPersistAndSyncConsumer itemPersistAndSyncConsumer;

    @Mock
    private ItemMasterService itemMasterService;


    @Mock
    Acknowledgment acknowledgment;

    @BeforeEach
    void setUp() {
    }

    @Test
    void listen() {
        doNothing().when(itemMasterService).persistItemAndSyncToD365(any());
        itemPersistAndSyncConsumer.listen(
                "{message}",acknowledgment);
    }
    @Test
    void listenException()  {
        doThrow(new RuntimeException("some error occured")).when(itemMasterService).persistItemAndSyncToD365(any());
        itemPersistAndSyncConsumer.listen(
                "{message}",acknowledgment);
    }
}