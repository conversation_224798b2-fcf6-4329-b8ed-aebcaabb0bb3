<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Email</title>
    <link rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>

    </style>
</head>
<body>
<div class="contaier-fluid">
    <div class="row justify-content-center">
        <div class="col-8 shadow rounded p-3">
            <p class="h10" >Hi Team</p>
            <p class="h10">The following <span th:text="${eventName}"></span> events in the last 24 hours have failed validation or have received error while syncing to D365</p>
            <p>
            <div class="container mt-4">
                <div class="row justify-content-center">
                    <div class="col-12">
                        <table border='1' width='700' cellpadding='10' cellspacing='0' style='border-collapse: collapse; text-align: left;'>
                            <thead>
                            <tr style='background-color: #f4c542; font-weight: bold; text-align: center;'>
                                <th style='padding: 10px;'>Report Name</th>
                                <th style='padding: 10px;'>Download Link</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td style='padding: 10px; text-align: center;'><span th:text="${eventName}"></span></td>
                                <td style='padding: 10px; text-align: center;'><span th:text="${reportUrl}"></span></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            </p>
            <br>
            <p>Note: Please connect to Lenskart VPN before downloading.</p>
            <p>Thank You <br> Finance Tech Team.
            </p>
        </div>
    </div>
</div>
</body>
</html>