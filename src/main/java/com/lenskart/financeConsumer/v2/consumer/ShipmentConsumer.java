package com.lenskart.financeConsumer.v2.consumer;

import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.v2.dto.ShipmentConsumerDto;
import com.lenskart.financeConsumer.v2.factory.SourceFactory;
import com.lenskart.financeConsumer.v2.service.ShipmentUtils;
import com.newrelic.api.agent.Trace;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Generated
public class ShipmentConsumer {

    @Autowired
    ShipmentUtils shipmentUtils;

    @Trace(dispatcher = true, metricName = "saleOrderV2SyncConsumer")
    @Retryable(value = RuntimeException.class, maxAttempts = 4)
    @KafkaListener(topics = "${sale.order.v2.sync.topic}", groupId = "${sale.order.v2.sync.topic.group}")
    public void saleOrderConsumer(String message, Acknowledgment ack) {
        log.info("[ShipmentConsumer] [saleOrderConsumer] Sale order sync message received :{}", message);
        try {
            ShipmentConsumerDto shipmentConsumerDto = ObjectHelper.readValue(message, ShipmentConsumerDto.class);
            log.info("[ShipmentConsumer] [saleOrderConsumer] Sale order sync shipmentConsumerDto received :{}", shipmentConsumerDto);
            shipmentUtils.processShipment(shipmentConsumerDto);
        } catch (RecordNotFoundException e) {
            log.error("[ShipmentConsumer] [saleOrderConsumer] RecordNotFoundException occurred while processing sale order", e);
            throw new RuntimeException(e.getMessage());
        } catch (RuntimeException e) {
            log.error("[ShipmentConsumer] [saleOrderConsumer] RuntimeException occurred while processing sale order", e);
            throw new RuntimeException(e.getMessage());
        }
        ack.acknowledge();
    }
}
