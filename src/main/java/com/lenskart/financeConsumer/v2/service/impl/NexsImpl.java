package com.lenskart.financeConsumer.v2.service.impl;

import com.lenskart.financeConsumer.dto.d365requests.D365ResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.exceptions.OrderSenseiIntegrationException;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.financeConsumer.v2.clients.NexsOrderSenseiClient;
import com.lenskart.financeConsumer.v2.constant.Constants;
import com.lenskart.financeConsumer.v2.dto.ErpRequestDto;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.lenskart.financeConsumer.v2.model.Shipment;
import com.lenskart.financeConsumer.v2.model.enums.Source;
import com.lenskart.financeConsumer.v2.repository.ShipmentRepository;
import com.lenskart.financeConsumer.v2.service.ShipmentUtils;
import com.lenskart.financeConsumer.v2.service.SourceService;
import com.lenskart.financeConsumer.v2.util.CommonUtil;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class NexsImpl implements SourceService {

    @Autowired
    NexsOrderSenseiClient nexsOrderSenseiClient;
    @Autowired
    ShipmentUtils shipmentUtils;
    @Autowired
    CommonUtil commonUtil;
    @Value("${order-sensei-url}")
    private String nexsOrderSenseiUrl;
    @Value("${non-sbrt.item.flag}")
    private String nonSBRTItemFlag;
    @Autowired
    private ClassificationService classificationService;
    @Autowired
    ShipmentRepository shipmentRepository;


    @Override
    public D365ResponseDto createPackingSlip(List<Integer> uwItemIds, long id, String shippingPackageId, String facilityCode) {
        try {

            PackingSlip payload = callOrderSenseiPS(shippingPackageId);

            addSuffixForPackingSlip(id,payload);

            ErpRequestDto erpRequestDto = commonUtil.buildAndGetErpRequestDto(id,
                                                                              payload,
                                                                              shippingPackageId,
                                                                              facilityCode);
            log.info("[NexsImpl][createPackingSlip] Request : {}", erpRequestDto);
            ErpResponseDto erpResponseDto = shipmentUtils.callFinanceAdaptor(Constants.PACKING_SLIP_CREATE_URL,
                                                                             erpRequestDto,
                                                                             id);
            log.info("[NexsImpl][createPackingSlip] Response : {}", erpResponseDto);
            return commonUtil.getD365ResponseDto(id,
                                                 erpResponseDto.getResponse(),
                                                 erpResponseDto.getD365SyncStatus(),
                                                 FinanceSourceSystemSyncEvent.PACKING_SLIP);
        } catch (Exception e) {
            log.error("Exception occurred while creating packing slip for id :{}", id, e);
            return commonUtil.getD365ResponseDto(id,
                                                 e.getMessage(),
                                                 D365ResponseType.FAILED,
                                                 FinanceSourceSystemSyncEvent.PACKING_SLIP);
        }
    }

    private void addSuffixForPackingSlip(long id, PackingSlip payload) {
        String suffixValue = "";
        Shipment saleOrderShipment = shipmentRepository.findById(id).orElseThrow(() -> new RecordNotFoundException(
                "[addSuffixForPackingSlip]Sale order record not found"));
        if (Objects.nonNull(saleOrderShipment) && Objects.nonNull(saleOrderShipment.getSuffix())) {
            suffixValue = saleOrderShipment.getSuffix();
        }
        payload.setSalesId(payload.getSalesId() + suffixValue);
    }

    @Override
    public D365ResponseDto createSalesOrder(List<Integer> uwItemIds, long id, String shippingPackageId, String facilityCode) {
        try {
            SalesOrderHeader payload = callOrderSensei(shippingPackageId);

            updateAdditionalFields(payload,shippingPackageId,id);

            ErpRequestDto erpRequestDto = commonUtil.buildAndGetErpRequestDto(id,
                                                                              payload,
                                                                              shippingPackageId,
                                                                              facilityCode);
            log.info("[NexsImpl][createSalesOrder] Request : {}", erpRequestDto);
            ErpResponseDto erpResponseDto = shipmentUtils.callFinanceAdaptor(Constants.SALE_ORDER_CREATE_URL,
                                                                             erpRequestDto,
                                                                             id);
            log.info("[NexsImpl][createSalesOrder] Response : {}", erpResponseDto);

            return commonUtil.getD365ResponseDto(id,
                                                 erpResponseDto.getResponse(),
                                                 erpResponseDto.getD365SyncStatus(),
                                                 FinanceSourceSystemSyncEvent.SALE_ORDER);
        } catch (Exception e) {
            log.error("Exception while sale order creation for  id :{}", id, e);
            return commonUtil.getD365ResponseDto(id,
                                                 e.getMessage(),
                                                 D365ResponseType.FAILED,
                                                 FinanceSourceSystemSyncEvent.SALE_ORDER);
        }
    }

    @SneakyThrows
    private void updateAdditionalFields(SalesOrderHeader payload, String shippingPackageId,long id) {
        String suffixValue = "";
        Shipment saleOrderShipment = shipmentRepository.findById(id).orElseThrow(() -> new RecordNotFoundException(
                "[updateAdditionalFields] Sale order record not found"));
        if (Objects.nonNull(saleOrderShipment) && Objects.nonNull(saleOrderShipment.getSuffix())) {
            suffixValue = saleOrderShipment.getSuffix();
        }
        payload.setSalesOrderNumber(shippingPackageId + "_" + payload.getSalesChannel()+suffixValue);
        payload.setExportReason("yes");
        payload.setIsWithTax("yes");
        payload.setCustomerGroup("B2CDomestic");
        setClassificationIdForProduct(payload);
    }

    @SneakyThrows
    private void setClassificationIdForProduct(SalesOrderHeader payload) {
        payload.getSoLines().forEach(soLine -> {
            try {
                String displayName = classificationService.getClassification(Integer.parseInt(soLine.itemClassification)).getDisplayName();
                ProductUtil.getClassificationDisplayName(Integer.parseInt(soLine.itemClassification), displayName);
                soLine.setItemClassification(displayName);
                soLine.setUnits(nonSBRTItemFlag);
            } catch (Exception e) {
                log.error("Error fetching classification id for classification id :{}", soLine.itemClassification);
                throw new RuntimeException("Error fetching classification id for classification id " + soLine.itemClassification);
            }

        });
    }

    @Override
    public Source getSource() {
        return Source.NEXS;
    }

    private SalesOrderHeader callOrderSensei(String shippingPackageId) {
        ResponseEntity<GenericResponseDto> responseEntity = nexsOrderSenseiClient.orderSenseiApiCall(nexsOrderSenseiUrl + Constants.GET_PAYLOAD_SALE_ORDER + shippingPackageId,
                                                                                                     HttpMethod.GET);
        log.info("[NexsImpl][callOrderSensei] Sale order response for shipping ID {} : {}", shippingPackageId, responseEntity);
        if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
            return ObjectHelper.convertValue(responseEntity.getBody().getData(),SalesOrderHeader.class); //NOSONAR
        } else {
            throw new OrderSenseiIntegrationException("Following error occurred while calling order sensei: " + responseEntity.getBody());
        }
    }

    private PackingSlip callOrderSenseiPS(String shippingPackageId) {
        ResponseEntity<GenericResponseDto> responseEntity = nexsOrderSenseiClient.orderSenseiApiCall(nexsOrderSenseiUrl + Constants.GET_PAYLOAD_PACKING_SLIP + shippingPackageId,
                                                                                                     HttpMethod.GET);
        log.info("[NexsImpl][callOrderSenseiPS] Packing slip response for shipping ID {} : {}", shippingPackageId, responseEntity);
        if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
            return ObjectHelper.convertValue(responseEntity.getBody().getData(), PackingSlip.class);//NOSONAR
        } else {
            throw new OrderSenseiIntegrationException("Following error occurred while calling order sensei: " + responseEntity.getBody());
        }
    }
}
