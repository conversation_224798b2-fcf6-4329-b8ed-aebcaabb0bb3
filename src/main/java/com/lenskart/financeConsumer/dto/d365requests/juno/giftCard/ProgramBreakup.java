package com.lenskart.financeConsumer.dto.d365requests.juno.giftCard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ProgramBreakup {

    @JsonProperty("programId")
    private String programId;

    @JsonProperty("amount")
    private Double amount;

    @JsonProperty("percentage")
    private Integer percentage;

    @JsonProperty("percentageAmount")
    private Double percentageAmount;

}