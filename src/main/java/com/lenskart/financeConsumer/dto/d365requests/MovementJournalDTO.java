package com.lenskart.financeConsumer.dto.d365requests;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MovementJournalDTO implements Serializable {

    private final static long serialVersionUID = 6358088437558417966L;
    @JsonProperty("LegalEntity")
    public String legalEntity;

    @JsonProperty("JournalNameId")
    public String journalNameId;

    @JsonProperty("DocumentNo")
    public String documentNumber;

    @JsonProperty("MovJourLines")
    public List<MovJourLine> movJourLines;

    @JsonProperty("type")
    public String type;

}