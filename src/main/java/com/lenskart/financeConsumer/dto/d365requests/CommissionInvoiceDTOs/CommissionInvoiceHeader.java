package com.lenskart.financeConsumer.dto.d365requests.CommissionInvoiceDTOs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommissionInvoiceHeader {

    @JsonProperty("LegalEntity")
    private String legalEntity;

    @JsonProperty("VendorNumber")
    private String vendorNumber;

    @JsonProperty("Import")
    private String importOrder;

    @JsonProperty("DeliveryName")
    private String deliveryName;

    @JsonProperty("DeliveryAddress")
    private String deliveryAddress;

    @JsonProperty("PurchaseOrderNumber")
    private String purchaseOrderNumber;

    @JsonProperty("PurchaseType")
    private String purchaseType;

    @JsonProperty("Currency")
    private String currency;

    @JsonProperty("InvoiceAccount")
    private String invoiceAccount;

    @JsonProperty("DeliveryDate")
    private String deliveryDate;

    @JsonProperty("Warehouse")
    private String warehouse;

    @JsonProperty("LKStatus")
    private String lKStatus;

    @JsonProperty("Created_at")
    private String createdAt;

    @JsonProperty("Created_by")
    private String createdBy;

    @JsonProperty("Brand")
    private String brand;

    @JsonProperty("CostCentre")
    private String costCentre;

    @JsonProperty("Units")
    private String units;

    @JsonProperty("PurchaseLineList")
    private List<CommissionInvoiceLine> commissionInvoiceLineList;
}
