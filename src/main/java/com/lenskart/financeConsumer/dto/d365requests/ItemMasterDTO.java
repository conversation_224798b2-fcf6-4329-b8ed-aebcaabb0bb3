package com.lenskart.financeConsumer.dto.d365requests;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


import java.io.Serializable;

@Data
public class ItemMasterDTO implements Serializable {

    @JsonProperty("ItemNumber")
    private String itemNumber;

    @JsonProperty("ProductType")
    private String productType;

    @JsonProperty("ProductSubType")
    private String productSubType;

    @JsonProperty("ProductSearchName")
    private String productSearchName;

    @JsonProperty("ProductName")
    private String productName;

    @JsonProperty("Description")
    private String description;
    @JsonProperty("ItemModelGroup")
    private String itemModelGroup;
    @JsonProperty("TrackingDimensionGroup")
    private String trackingDimensionGroup;
    @JsonProperty("StorageDimensionGroup")
    private String storageDimensionGroup;
    @JsonProperty("productDimensionGroup")
    private String productDimensionGroup;
    @JsonProperty("InventoryUnitId")
    private String inventoryUnitId;
    @JsonProperty("PurchaseUnitId")
    private String purchaseUnitId;
    @JsonProperty("SalesUnitId")
    private String salesUnitId;
    @JsonProperty("BOMUnitId")
    private String bomUnitId;
    @JsonProperty("HSNCode")
    private String hsnCode;

    @JsonProperty("Height")
    private Double height;

    @JsonProperty("Width")
    private Double width;

    @JsonProperty("Depth")
    private Double depth;

    @JsonProperty("TaxRateType")
    private String taxRateType;

    @JsonProperty("ItemGroup")
    private String itemGroup;
    @JsonProperty("CountingGroup")
    private String countingGroup;
    @JsonProperty("CostGroup")
    private String costGroup;
    @JsonProperty("CalculationGroup")
    private String calculationGroup;
    @JsonProperty("Ean")
    private String ean;
    @JsonProperty("Isbn")
    private String isbn;
    @JsonProperty("Upc")
    private String upc;
    @JsonProperty("Metal_Purity")
    private String metalPurity;
    @JsonProperty("color")
    private String color;
    @JsonProperty("brand")
    private String brand;
    @JsonProperty("size")
    private String size;
    @JsonProperty("itemDetailFields")
    private String itemDetailFields;
    @JsonProperty("ap")
    private String ap;
    @JsonProperty("pd")
    private String pd;
    @JsonProperty("bd")
    private String bd;
    @JsonProperty("td")
    private String td;
    @JsonProperty("ed")
    private String ed;
    @JsonProperty("near_pd")
    private String nearPd;
    @JsonProperty("effective_dia")
    private String effectiveDia;

    @JsonProperty("axis")
    private String axis;
    @JsonProperty("base_curve")
    private String baseCurve;
    @JsonProperty("lensColor")
    private String lensColor;
    @JsonProperty("sph")
    private String sph;
    @JsonProperty("lens_index")
    private String lensIndex;
    @JsonProperty("lens_name")
    private String lensName;
    @JsonProperty("lens_type")
    private String lensType;
    @JsonProperty("lens_vendor_pkg_name")
    private String lensVendorPkgName;
    @JsonProperty("with_ap")
    private String withAp;
    @JsonProperty("cost_average")
    private String costAverage;
    @JsonProperty("enable")
    private String enable;
    @JsonProperty("frame_type")
    private String frameType;
    @JsonProperty("msl")
    private String msl;
    @JsonProperty("sku")
    private String sku;
    @JsonProperty("completeName")
    private String completeName;
    @JsonProperty("barcodeScan")
    private String barcodeScan;
    @JsonProperty("kart")
    private String kart;
    @JsonProperty("lens_side")
    private String lensSide;
    @JsonProperty("transportation_flag")
    private String transportationFlag;
    @JsonProperty("mrp")
    private Double mrp;
    @JsonProperty("dkMar")
    private Double dkMar;
    @JsonProperty("lkPrice")
    private Double lkPrice;
    @JsonProperty("marketPrice")
    private Double marketPrice;
    @JsonProperty("minOrderSize")
    private Integer minOrderSize;
    @JsonProperty("taxTypeCode")
    private Integer taxTypeCode;
    @JsonProperty("jitBuffer")
    private Integer jitBuffer;
    @JsonProperty("internalBuffer")
    private Integer internalBuffer;
    @JsonProperty("gstTaxTypeCode")
    private String gstTaxTypeCode;
    @JsonProperty("features")
    private String features;
    @JsonProperty("imageURL")
    private String imageUrl;
    @JsonProperty("productPageURL")
    private String productPageUrl;
    @JsonProperty("cyl")
    private String cyl;
    @JsonProperty("LegalEntity")
    private String LegalEntity;
    @JsonProperty("SACCode")
    private String SACCode;
    @JsonProperty("ProductionType")
    private String productionType;
    @JsonProperty("model")
    private String model;
    @JsonProperty("item_type_Test")
    private String itemTypeTest;
    @JsonProperty("CustomsImportTariffCode")
    private Integer customsImportTariffCode;
    @JsonProperty("CustomsExportTariffCode")
    private Integer customsExportTariffCode;
    @JsonProperty("Site")
    private String site;
    @JsonProperty("Units")
    private String units;
    @JsonProperty("CostCentre")
    private String costCentre;
    @JsonProperty("Stores")
    private String stores;
    @JsonProperty("SalesChannel")
    private String salesChannel;
    @JsonProperty("SubChannel")
    private String subChannel;
    @JsonProperty("PartnerType")
    private String partnerType;
    @JsonProperty("ItemClassification")
    private String itemClassification;
    @JsonProperty("Brand")
    private String FinancialDimensionBrand;
    @JsonProperty("Employee")
    private String employee;

    public ItemMasterDTO(){
        this.itemNumber ="";
      //  this.productType = "";
        //  this.productSubType = "";
        this.productSearchName = "";
      //  this.productName = "";
        this.description = "";
      //  this.itemModelGroup = "";
      //  this.trackingDimensionGroup = "";
      //  this.storageDimensionGroup = "";
       // this.productDimensionGroup = "";
        this.inventoryUnitId = "";
        this.purchaseUnitId = "";
        this.salesUnitId = "";
        this.bomUnitId = "";
        this.hsnCode = "";
        this.height = 0.0D;
        this.width = 0.0D;
        this.depth = 0.0D;
        this.taxRateType = "";
        // this.itemGroup = "";
        //  this.countingGroup = "";
    //    this.costGroup = "";
        this.calculationGroup = "";
       // this.ean = "";
       // this.isbn = "";
       // this.upc = "";
        //  this.metalPurity = "";
        this.color = "";
        this.brand = "";
        this.features="";
        // this.itemDetailFields = "";
        this.ap = "";
        this.pd = "";
        this.bd = "";
        this.td = "";
        this.ed = "";
        this.nearPd = "";
        this.effectiveDia = "";
        this.axis = "";
        this.baseCurve = "";
        this.lensColor = "";
        this.sph = "";
        this.lensIndex = "";
        this.lensName = "";
        this.lensType = "";
        this.lensVendorPkgName = "";
        this.withAp = "";
        //     this.costAverage = "";
        this.enable = "";
        this.frameType = "";
        this.msl = "";
        this.sku = "";
        //   this.completeName = "";
        //    this.barcodeScan = "";
        // this.kart = "";
        //   this.lensSide = "";
        this.transportationFlag = "";
        this.cyl = "";
        this.gstTaxTypeCode="";
        //  this.productionType = "";
        //    this.model = "";
        //   this.itemTypeTest = "";
        //   this.customsImportTariffCode = 0;
        //   this.customsExportTariffCode = 0;
        // this.site = "";
     /*   this.units = "";
        this.costCentre = "";
        this.stores = "";
        this.salesChannel = "";
        this.subChannel = "";
        this.partnerType = "";
        this.employee = "";*/
        this.mrp = 0.0D;
        this.dkMar=0.0D;
        this.lkPrice=0.0D;
        this.marketPrice = 0.0D;
        this.minOrderSize=0;
        this.taxRateType="";
        this.jitBuffer=0;
        this.internalBuffer=0;
        this.taxTypeCode = 0;
        this.itemClassification = "";
    }


}
