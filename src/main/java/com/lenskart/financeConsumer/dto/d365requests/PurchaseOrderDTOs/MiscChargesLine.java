package com.lenskart.financeConsumer.dto.d365requests.PurchaseOrderDTOs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MiscChargesLine {

    @JsonProperty("PurchaseOrderLineNumber")
    private String purchaseOrderLineNumber;
    @JsonProperty("ChargeLineNumber")
    private Integer chargeLineNumber;
    @JsonProperty("PurchaseChargeCode")
    private String purchaseChargeCode;
    @JsonProperty("ChargeCategory")
    private String chargeCategory;
    @JsonProperty("FixedChargeAmount")
    private Double fixedChargeAmount;
    @JsonProperty("ChargeAccountingCurrencyCode")
    private String chargeAccountingCurrencyCode;

}
