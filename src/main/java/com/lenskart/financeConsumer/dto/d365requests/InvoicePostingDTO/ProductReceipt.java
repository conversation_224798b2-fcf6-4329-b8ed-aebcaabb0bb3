package com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Generated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Valid
@Generated
public class ProductReceipt {
    @JsonProperty("ProductReceipt")
    String productReceipt;
}
