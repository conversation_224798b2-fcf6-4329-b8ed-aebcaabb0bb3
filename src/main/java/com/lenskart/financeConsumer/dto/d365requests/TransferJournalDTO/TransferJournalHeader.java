package com.lenskart.financeConsumer.dto.d365requests.TransferJournalDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TransferJournalHeader {
    @JsonProperty("LegalEntity")
    private String legalEntity;
    @JsonProperty("JournalName")
    private String journalName;
    @JsonProperty("Description")
    private String description;
    @JsonProperty("TransferJourLines")
    private List<TransferJourLines> transferJourLines;
    @JsonProperty("DocumentNo")
    private String documentNumber;
}