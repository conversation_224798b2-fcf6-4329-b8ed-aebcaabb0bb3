package com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class InvoiceRequestDTO {

    private String vendorCode;

    private String vendorName;

    private String currencyCode;

    private String url;

    @NotBlank
    private String vendorInvoiceNumber;

    @NotBlank
    private String poNumber;

    @NotNull
    private BigDecimal vendorInvoiceAmount;

    @NotNull
    private Integer vendorInvoiceQuantity;

    @NotBlank
    private String boeNumber;
}