package com.lenskart.financeConsumer.dto.d365requests.MovementJournalDTOs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetBarcodeDetailsRequestModel {
    @JsonProperty("DocumentNo")
    String documentNumber;
    @JsonProperty("LegalEntity")
    String legalEntity;
}
