package com.lenskart.financeConsumer.dto.d365requests.InvoicePostingDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.financeConsumer.model.enums.InvoicePostingStatus;
import com.lenskart.financeConsumer.model.enums.InvoicePostingSubStatus;
import lombok.Builder;
import lombok.Generated;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Builder
@Generated
public class InvoiceDTO {

    private Long id;

    private String vendorInvoiceNumber;

    private String poNumber;

    private String vendorInvoiceAmount;

    private String nexsInvoiceAmount;

    private int vendorInvoiceQuantity;

    private String vendorName;

    private String vendorCode;

    private String currencyCode;

    private String url;

    private String grnCodes;

    private String boeNumber;

    private InvoicePostingStatus invoiceStatus;

    private InvoicePostingSubStatus invoiceSubStatus;

    private Integer triggerCount;

    private String errorMessage;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    private Date invoiceCreationDate;
}

