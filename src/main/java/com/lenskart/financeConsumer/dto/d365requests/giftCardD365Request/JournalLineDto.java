package com.lenskart.financeConsumer.dto.d365requests.giftCardD365Request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class JournalLineDto {
    @JsonProperty(value = "TransDate")
    private LocalDate transDate;
    @JsonProperty(value = "Account")
    private String account;
    @JsonProperty(value = "OffsetAccount")
    private String offsetAccount;
    @JsonProperty(value = "Description")
    private String description;
    @JsonProperty(value = "ItemId")
    private String itemId;
    @JsonProperty(value = "Amount")
    private String amount;
    @JsonProperty(value = "Currency")
    private String currency;
    @JsonProperty(value = "Qty")
    private String qty;
    @JsonProperty(value = "DocumentNo")
    private String documentNo;
    @JsonProperty(value = "DocumentDate")
    private LocalDate documentDate;
}
