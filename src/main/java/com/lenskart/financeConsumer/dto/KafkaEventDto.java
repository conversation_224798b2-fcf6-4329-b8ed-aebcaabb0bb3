package com.lenskart.financeConsumer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@Builder
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class KafkaEventDto {
    String eventType;
    String documentPayload;
    String shipmentId;
    Integer incrementId;
    String facilityCode;
    String instanceType;
}
