package com.lenskart.financeConsumer.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MovementJournalResponse {

    @JsonProperty("DocumentNo")
    private String DocumentNo;

    @JsonProperty("Message")
    private String Message;

    @JsonProperty("Barcode")
    private String Barcode;

    @JsonProperty("MovJourLines")
    private List<Object> MovJourLines;

    @JsonProperty("Success")
    private String Success;

    @JsonProperty("shippingPackageId")
    private String shippingPackageId;
}
