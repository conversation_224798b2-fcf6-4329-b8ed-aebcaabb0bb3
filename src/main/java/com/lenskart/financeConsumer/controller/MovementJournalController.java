package com.lenskart.financeConsumer.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.dao.ShippingStatusRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.d365requests.MovementJournalRequest;
import com.lenskart.financeConsumer.service.AdjustmentProcessor;
import com.lenskart.financeConsumer.service.MovementJournalService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.service.impl.MovementJournalServiceImpl;
import com.lenskart.financeConsumer.util.MovementJournalUtils;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping("/movement-journal")
public class MovementJournalController {

    @Autowired
    UwOrdersRepository uwOrdersRepository;
    @Autowired
    ShippingStatusRepository shippingStatusRepository;
    @Autowired
    private MovementJournalService movementJournalService;
    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;
    @Value("${d365.movementjournal.kafka.topic}")
    private String movementJournalTopic;
    @Autowired
    private UwOrdersService uwOrdersService;

    @Autowired
    private SaleOrderUtil saleOrderUtil;

    @Autowired
    MovementJournalServiceImpl movementJournalServiceImpl;


    @Autowired
    private MovementJournalUtils movementJournalUtils;


    @PostMapping("/shipment/kafka/create")
    public Map<String, Object> pushMovementJournalToKafka(@RequestBody List<MovementJournalRequest> movementJournalRequests) throws Exception {
        Map<String, Object> response = new HashMap<>();
        if (!valdateMovJournalKafkaPayload(movementJournalRequests)) {
            response.put("", "Size is to high or empty payload");
        }

        for (MovementJournalRequest movementJournalRequest : movementJournalRequests) {
            log.info("Inside [MovementJournalController][pushMovementJournalToKafka] " + movementJournalRequest);
            try {
                if (movementJournalService.validateMovementJournalRequest(movementJournalRequest)) {
                    if (CollectionUtils.isEmpty(movementJournalRequest.getBarcodes())) {
                        List<String> barcodes =
                                uwOrdersRepository.getBarcodeFromShipment(movementJournalRequest.getShippingPackageId());
                        movementJournalRequest.setBarcodes(barcodes);
                    }
                    kafkaProducerTemplate.send(movementJournalTopic,
                            new ObjectMapper().writeValueAsString(movementJournalRequest));
                    response.put(movementJournalRequest.getShippingPackageId(), "Pushed to kafka");
                } else {
                    response.put(movementJournalRequest.getShippingPackageId(), "Invalid movement journal request");
                }
            } catch (Exception e) {
                response.put(movementJournalRequest.getShippingPackageId(), e.getMessage());
                log.error(" [MovementJournalController][pushMovementJournalToKafka] {} error {} ",
                        movementJournalRequest, e);
            }
        }
        return response;
    }

    private boolean valdateMovJournalKafkaPayload(List<MovementJournalRequest> movementJournalRequests) {
        if (CollectionUtils.isEmpty(movementJournalRequests) || movementJournalRequests.size() > 20) {
            return false;
        }
        for (MovementJournalRequest movementJournalRequest : movementJournalRequests) {
            if (StringUtils.isEmpty(movementJournalRequest))
                return false;
        }
        return true;
    }

    @PostMapping("/shipment/create")
    public Map<String, Object> createMovementJournal(@RequestBody List<MovementJournalRequest> movementJournalRequests) throws Exception {
        log.info("[createMovementJournal] :{}", movementJournalRequests);
        Map<String, Object> response = new HashMap<>();
        if (movementJournalRequests.size() > 20) {
            response.put("Message", "Request has more than 20 requests in list");
            response.put("Success", false);
            return response;
        }
        for (MovementJournalRequest movementJournalRequest : movementJournalRequests) {
            if (movementJournalService.validateMovementJournalRequest(movementJournalRequest)) {
                List<String> barcodes = movementJournalRequest.getBarcodes();
                if (CollectionUtils.isEmpty(movementJournalRequest.getBarcodes())) {
                    barcodes = uwOrdersRepository.getBarcodeFromShipment(movementJournalRequest.getShippingPackageId());
                }
                for (int i = 0; i < barcodes.size(); i++) {
                    movementJournalRequest.setBarcodes(Arrays.asList(barcodes.get(i)));
                    String entityIdForDocumentNo = saleOrderUtil.getSalesOrderNo((movementJournalRequest.getShippingPackageId())) + "_" + movementJournalRequest.getBarcodes().get(0);
                    AdjustmentProcessor adjustmentProcessor = movementJournalServiceImpl.getAdjustmentProcessor(movementJournalRequest);
                    String documentNo = adjustmentProcessor.generateDocumentNumber(entityIdForDocumentNo);
                    ResponseEntity responseEntity = movementJournalServiceImpl.createMovementJournalBarcodeWise(movementJournalRequest, documentNo);
                    log.info("[MovementJournalConsumer] response:{}", ObjectHelper.writeValue(responseEntity));
                    if (Objects.nonNull(responseEntity)  && Objects.nonNull(responseEntity.getBody())) {
                        HashMap responseBody = (HashMap) responseEntity.getBody();
                        response.put(documentNo, responseBody);
                    }
                }
            } else {
                response.put(movementJournalRequest.getShippingPackageId(), "Invalid movement journal request");
                response.put("Success", false);
                return response;
            }
        }
        response.put("Success", true);
        return response;
    }
    @PostMapping("/v2/shipment/create")
    public Map<String, Object> createMovementJournalForBarcodes(@RequestBody List<MovementJournalRequest> movementJournalRequests) throws Exception {
        log.info("[createMovementJournal] :{}", movementJournalRequests);
        return movementJournalService.createMovementJournalWithMultipleBarcodes(movementJournalRequests);
    }

    @PostMapping("/barcodes")
    public Map<String, Object> movementJournalViaBarcode(@RequestBody List<String> barcodes) throws Exception {
        log.info("[movementJournalViaBarcode] :{}", barcodes);
        Map<String, Object> response = new HashMap<>();
        if (barcodes.size() > 20) {
            response.put("Message", "Request has more than 20 requests in list");
            response.put("Success", false);
            return response;
        }
//TODO expose API on barcode level sync
//        if (!movementJournalService.validateMovementJournalBarcodesRequest(barcodes)) {
//            response.put("Message", "Request has more than 20 requests in list");
//            response.put("Success", false);
//            return response;
//        }
//        for (String barcode : barcodes) {
//            log.info("Inside [movementJournalViaBarcode][addItemListKafka] " + barcodes);
//            boolean newFlow = Boolean.valueOf(enableNewItemFlow);
//            if (newFlow) {
//                kafkaProducerTemplate.send(Constants.ItemMaster.ItemNewKafkaTopic, String.valueOf(productId));
//            } else {
//                kafkaProducerTemplate.send(itemMasterTopic, String.valueOf(productId));
//            }
//        }
        return response;
    }

    @PostMapping("/barcodes/kafka")
    public void movementJournalKafkaPush(@RequestBody List<String> barcodes) throws Exception {
        log.info("[movementJournalKafkaPush] :{}", barcodes);
        Map<String, Object> response = new HashMap<>();
        for (String barcode : barcodes) {
            try {
                UwOrder uwOrder = uwOrdersService.getUwOrderByBarcode(barcode);
                ShippingStatus shippingStatus = shippingStatusRepository.findByShippingPackageId(uwOrder.getShippingPackageId());
                if (movementJournalUtils.isMovementJournalAllow(shippingStatus.getShipping_time(),
                        shippingStatus.getManifestDate())) {
                    MovementJournalRequest movementJournalRequest = new MovementJournalRequest();
                    movementJournalRequest.setShippingPackageId(uwOrder.getShippingPackageId());
                    movementJournalRequest.setBarcodes(Arrays.asList(barcode));
                    kafkaProducerTemplate.send(movementJournalTopic,
                            new ObjectMapper().writeValueAsString(movementJournalRequest));
                } else {
                    log.info("uwOrder createdAt: {} not allow for movement journal | barcode:{}",
                            uwOrder.getCreatedAt(),
                            barcode);
                }
            } catch (Exception ex) {
                log.error("[movementJournalKafkaPush] exception for :: {}", barcode, ex);
            }
        }

    }

    @PostMapping("/movementJournalAsync")
    public void movementJournalAsync(@RequestBody MovementJournalRequest movementJournalRequest) {
        String movementJournalRequestJson = ObjectHelper.writeValue(movementJournalRequest);
        kafkaProducerTemplate.send(movementJournalTopic,movementJournalRequestJson);
    }

    @PostMapping("/create/id-range/sync")
    public void createInventoryByRange(@RequestParam(name= "start") Long start, @RequestParam(name= "end") Long end ) throws com.lenskart.financeConsumer.exceptions.InvalidRequestException {
        movementJournalService.createInventoryByRangeSync(start, end);
    }

    @PostMapping("/create/id-range/async")
    public void createInventoryByRangeAsync(@RequestParam(name= "start") Long start, @RequestParam(name= "end") Long end ) throws com.lenskart.financeConsumer.exceptions.InvalidRequestException {
        movementJournalService.createInventoryByRangeAsync(start, end);
    }

    /****
     * @param ids are the Id (primary key) of inventory table in finance db
     */
    @PostMapping("/sync/listOfIds")
    public void syncInventoryByListOfIds(@RequestBody List<Integer> ids) {
        movementJournalService.syncInventoryWithListOfIds(ids);
    }



    @PostMapping("/ingest-file")
    public ResponseEntity ingestFile(@RequestBody MultipartFile file){
        return movementJournalService.ingestInventoryFile(file);
    }
}
