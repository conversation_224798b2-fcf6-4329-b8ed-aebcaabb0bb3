package com.lenskart.financeConsumer.controller;

import com.google.gson.Gson;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.constant.SchedulerConfigConstants;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.SaleOrderPackingSlipPayloadRequestDto;
import com.lenskart.financeConsumer.dto.d365requests.AddSuffixDtoRequest;
import com.lenskart.financeConsumer.dto.d365requests.AddSuffixListDtoNewInstanceRequest;
import com.lenskart.financeConsumer.dto.d365requests.AddSuffixListDtoRequest;
import com.lenskart.financeConsumer.dto.d365requests.DateRange;
import com.lenskart.financeConsumer.dto.d365requests.InvoiceRequest;
import com.lenskart.financeConsumer.dto.d365requests.SaleOrderPackingSlipResponseDTO;
import com.lenskart.financeConsumer.dto.d365requests.SaleOrderSync;
import com.lenskart.financeConsumer.dto.d365requests.SaleOrderSyncItem;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.NewInstanceDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.model.enums.SyncRequestType;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.service.ApiLogService;
import com.lenskart.financeConsumer.service.SalesOrderService;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.service.impl.SalesOrderServiceImplTemp;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/sale-order")
public class SaleOrderController {

    @Autowired
    private UwOrdersService uwOrdersService;
    @Autowired
    private SalesOrderServiceImplTemp salesOrderServiceImplTemp;
    @Autowired
    private SalesOrderService salesOrderService;
    @Autowired
    private UwOrdersRepository uwOrdersRepository;
    @Autowired
    private ApiLogService apiLogService;
    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;
    @Autowired
    SchedulerConfigService schedulerConfigService;
    @Value("${d365.salesorder.kafka.prod.temp.topic}")
    private String d365SaleOrderProdTempTopic;
    @Value("${kafka.topic.generate_invoice_data}")
    private String invoiceDetailsTopic;

    @PostMapping("/create")
    public SaleOrderPackingSlipResponseDTO createSale(HttpServletRequest req, @RequestBody List<Integer> uwItemIds) throws Exception {
        String shipmentId =
                String.valueOf(uwOrdersService.getUwOrderByUwItemIdForShipmentId(uwItemIds.get(0)).getShippingPackageId());
        SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO =
                salesOrderService.createSalesOrder(shipmentId, uwItemIds, Constants.SalesOrder.CREATE_PROD_URL, null);
        return saleOrderPackingSlipResponseDTO;
    }

    /***
     * Temporary controller to sync few sales order and packing slips with shipping charges payload,
     * its usage can be expanded to other cases as well in future with force sync .
     * @param req
     * @param shippingPackageIds
     * @param isForceSyncWithShippingChargeRequired
     * @return
     * @throws Exception
     */
    @PostMapping("/create/sale-order/force/shippingCharge")
    public List<SaleOrderPackingSlipResponseDTO> createSaleOrderWithShippingCharges(HttpServletRequest req, @RequestBody List<String> shippingPackageIds,
                                                                                    @RequestParam boolean isForceSyncWithShippingChargeRequired) throws Exception {
        List<SaleOrderPackingSlipResponseDTO> saleOrderPackingSlipResponseDTOList =new ArrayList<>();
        for(int i=0;i<shippingPackageIds.size();i++){
            List<String> unicomOrderIds =
                    uwOrdersService.getUnicomOrderCodeFromShippingPackageId(shippingPackageIds.get(i));
            List<Integer> uwItemIds = apiLogService.getUwItemIdsFromShipment(unicomOrderIds);
            SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO =
                    salesOrderService.createSalesOrder(shippingPackageIds.get(i), uwItemIds,
                                                       Constants.SalesOrder.CREATE_PROD_URL, null, isForceSyncWithShippingChargeRequired,
                                                       NewInstanceDto.builder().isNewInstance(false).build() );
            saleOrderPackingSlipResponseDTOList.add(saleOrderPackingSlipResponseDTO);
        }
        return saleOrderPackingSlipResponseDTOList;
    }

    @PostMapping("/getPayload")
    public SalesOrderHeader generatePayload(@RequestBody SaleOrderPackingSlipPayloadRequestDto saleOrderPackingSlipPayloadRequestDto) throws Exception {
        SalesOrderHeader payload = salesOrderService.generateSaleOrderPayloadOnly(saleOrderPackingSlipPayloadRequestDto.getShippingId(),
                                                                                  saleOrderPackingSlipPayloadRequestDto.getFacilityCode(),
                                                                                  saleOrderPackingSlipPayloadRequestDto.isNewInstance());
        return payload;
    }

    @PostMapping("/shipment/create")
    public void createSaleUsingShipment(HttpServletRequest req, @RequestBody List<String> shippingPackageIds) throws Exception {
        salesOrderService.pushShippingPackageIdsInSaleOrderKafka(shippingPackageIds);
    }
    @PostMapping("/shipment/create/id")
    public void createSaleUsingId(@RequestBody List<Long> ids) throws Exception {
        salesOrderService.processIdsForPushingShipments(ids);
    }

    @PostMapping("/create/prod")
    public void createSaleProd(HttpServletRequest req, @RequestBody List<Integer> uwItemIds) throws Exception {
        salesOrderService.pushUwItemIdsInSaleOrderKafka(uwItemIds);
    }

    @PostMapping("/shipment/create/prod")
    public void createSaleUsingShipmentProd(HttpServletRequest req, @RequestBody List<String> shippingPackageIds) throws Exception {
        salesOrderService.pushShippingPackageIdsInSaleOrderKafkaProd(shippingPackageIds);
    }

    @PostMapping("/barcode/createFwdSO")
    public void createSaleUsingBarcode(@RequestBody List<String> barcodes) throws Exception {
        List<String> shippingPackageIds = uwOrdersRepository.getShippingPackageIdsFromBarcodes(barcodes);
        salesOrderService.pushShippingPackageIdsInSaleOrderKafkaProd(shippingPackageIds);
    }

    @PostMapping("/invoice-details/kafka")
    public void createInvoiceDetails(@RequestBody List<String> shippingPackageIds) throws Exception {
        for (String shippingPackageId : shippingPackageIds) {
            InvoiceRequest invoiceRequest = new InvoiceRequest();
            List<Integer> uwItemIds = uwOrdersRepository.getUwItemIdsByShippingPackageId(shippingPackageId);
            invoiceRequest.setUwItemIds(uwItemIds);
            kafkaProducerTemplate.send(invoiceDetailsTopic, new Gson().toJson(invoiceRequest));
        }
    }

    @PostMapping("/retry")
    public void retry(@RequestBody DateRange dateRange) throws Exception {
        salesOrderService.retrySaleOrder(dateRange.getStartDate(), dateRange.getEndDate(), 0);
    }

    @PostMapping("/createSaleOrderTemp")
    public void createSaleOrderTemp(@RequestBody SaleOrderSync saleOrderSync) {
        salesOrderService.createSaleOrderTemp(saleOrderSync);
    }

    @PostMapping("/createSaleOrderTemp/sync")
    public SaleOrderPackingSlipResponseDTO createSaleOrderTempSync(@RequestBody SaleOrderSync saleOrderSync) throws Exception {
        List<Integer> uwItemIds = saleOrderSync.getItems().stream()
                .map(SaleOrderSyncItem::getUwItemId)
                .collect(Collectors.toList());
        UwOrder uwOrder = uwOrdersService.getUwOrderByUwItemIdForShipmentId(uwItemIds.get(0));
        String shipmentId = String.valueOf(uwOrder.getShippingPackageId());
        SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO =
                salesOrderServiceImplTemp.createSalesOrder(shipmentId, uwItemIds,
                        Constants.SalesOrder.CREATE_PROD_SO_URL, saleOrderSync);
        return saleOrderPackingSlipResponseDTO;
    }

    @PostMapping("/createSaleOrderTemp/list")
    public void createSaleOrderTemp(@RequestBody List<SaleOrderSync> saleOrderSyncs) {
        for (SaleOrderSync saleOrderSync : saleOrderSyncs) {
            salesOrderService.createSaleOrderTemp(saleOrderSync);
        }
    }

    @PostMapping("/shipmentId/repopulate")
    public boolean shipmentIdRepopulate(@RequestBody List<Integer> uwItemIds) throws Exception {
        return salesOrderService.shipmentIdRepopulate(uwItemIds);
    }

    @PostMapping("/shipmentId/repopulate/{unicomOrderCode}")
    public boolean shipmentIdRepopulateUnicomOrderCode(@PathVariable("unicomOrderCode") String unicomOrderCode) throws Exception {
        return salesOrderService.shipmentIdRepopulateUnicomOrderCode(unicomOrderCode);
    }

    @PostMapping("/unicomOrderCodes/shipmentId/repopulate")
    public void shipmentIdRepopulateUnicomOrderCode(@RequestBody List<String> unicomOrderCodes) {
        salesOrderService.shipmentIdRepopulateForUnicomOrderCodes(unicomOrderCodes);
    }
    @PostMapping("/sync/new/idRange")
    public void syncByIdRange(@RequestParam Long startId, @RequestParam Long endId, @RequestParam SyncRequestType syncRequestType) {
        salesOrderService.syncSaleOrder(startId, endId, syncRequestType);
    }

    @PostMapping("/sync/new/id")
    public void syncByListOfIds(@RequestBody Set<Long> listOfIds, @RequestParam SyncRequestType syncRequestType) {
        salesOrderService.syncSaleOrder(listOfIds, syncRequestType);
    }
    @PostMapping("/sync/new/date")
    public void syncByDateRange(@RequestParam(value = "fromDate in yyyy-MM-dd HH:mm:ss", required = false) String fromDate,
                                @RequestParam(value = "toDate in yyyy-MM-dd HH:mm:ss", required = false) String toDate,
                                @RequestParam SyncRequestType syncRequestType) {
        LocalDateTime startDate=DateUtils.getDateFromString(fromDate);
        LocalDateTime endDate=DateUtils.getDateFromString(toDate);
        salesOrderService.syncSaleOrder(startDate, endDate, syncRequestType);
    }

    @PostMapping("/trigger/failure-strategy/sale-order")
    public void triggerStrategyForFailureSaleOrders() {
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(
                SchedulerConfigConstants.SALE_ORDER_FAILURE_STRATEGY);
        salesOrderService.triggerFailureStrategySaleOrder(schedulerConfig);
    }

    @PostMapping("/trigger/failure-strategy/packing-slip")
    public void triggerStrategyForFailurePackingSlip() {
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(
                SchedulerConfigConstants.PACKING_SLIP_FAILURE_STRATEGY);
        salesOrderService.triggerFailureStrategyPackingSlip(schedulerConfig);
    }

    @PostMapping("/add-suffix/oldInstance")
    public void addSuffixForShippingIdOldInstance(@RequestBody AddSuffixDtoRequest addSuffixDtoRequest) {
        log.info("[SaleOrderController] Adding suffix value {} for request {}", addSuffixDtoRequest.getSuffixValue(), addSuffixDtoRequest.getShippingIdFacilityCodes().toString());
        salesOrderService.addSuffixToShippingIdOldInstance(addSuffixDtoRequest.getShippingIdFacilityCodes(),
                                                           addSuffixDtoRequest.getRequestType(),
                                                           addSuffixDtoRequest.getSuffixValue());
    }

    @PostMapping("/add-suffix/list/oldInstance")
    public void addSuffixForShippingIdListOldInstance(@RequestBody AddSuffixListDtoRequest addSuffixListDtoRequest) {
        log.info("[SaleOrderController] Adding suffix value {} for Shipment Ids {}", addSuffixListDtoRequest.getSuffixValue(), addSuffixListDtoRequest.getIds().toString());
        salesOrderService.addSuffixToShippingIdOldInstance(addSuffixListDtoRequest.getIds(),
                                                           addSuffixListDtoRequest.getSuffixValue());
    }

    @PostMapping("/add-suffix/newInstance")
    public void addSuffixForShippingIdNewInstance(@RequestBody AddSuffixDtoRequest addSuffixDtoRequest) {
        log.info("[SaleOrderController] Adding suffix value {} for request {}", addSuffixDtoRequest.getSuffixValue(), addSuffixDtoRequest.getShippingIdFacilityCodes().toString());
        salesOrderService.addSuffixToShippingIdNewInstance(addSuffixDtoRequest.getShippingIdFacilityCodes(),
                                                           addSuffixDtoRequest.getRequestType(),
                                                           addSuffixDtoRequest.getSuffixValue());
    }

    @PostMapping("/add-suffix/list/newInstance")
    public void addSuffixForShippingIdListNewInstance(@RequestBody AddSuffixListDtoNewInstanceRequest addSuffixListDtoNewInstanceRequest) {
        log.info("[SaleOrderController] Adding suffix value {} for Shipment Ids {}", addSuffixListDtoNewInstanceRequest.getSuffixValue(), addSuffixListDtoNewInstanceRequest.getIds().toString());
        salesOrderService.addSuffixToShippingIdNewInstance(addSuffixListDtoNewInstanceRequest.getIds(),
                                                           addSuffixListDtoNewInstanceRequest.getSuffixValue(),
                                                           addSuffixListDtoNewInstanceRequest.getRequestType());
    }
    @PostMapping("/update-finance-entity")
    public void updatedFinanceSourceEntityByIds(@RequestBody Set<Long> ids) {
        log.info("[SaleOrderController][updatedFinanceSourceEntityByIds] Adding payload incrementId and description  for  Ids {}", ids);
        salesOrderService.updatedFinanceSourceEntityByIds(ids);
    }


}

