package com.lenskart.financeConsumer.controller;


import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dao.mongo.FinanceConsumerLogsRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.d365requests.GenericRetryDto.ApiLogDto;
import com.lenskart.financeConsumer.dto.d365requests.GenericRetryDto.ResponseLogDto;
import com.lenskart.financeConsumer.service.ApiLogService;
import com.lenskart.financeConsumer.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/apilogs")
public class  ApiLogController {

    @Autowired
    ApiLogService apiLogService;

    @Autowired
    FinanceConsumerLogsRepository financeConsumerLogsRepository;
    @Autowired
    UwOrdersRepository uwOrdersRepository;

    @Autowired
    D365LogRepository d365LogRepository;

    @Value("${D365.forward.url}")
    private String D365BaseUrl;

    @GetMapping(value = "/allLogs/{tab_id}/{page_no}/{size_no}")
    public  ResponseEntity<ResponseLogDto>  allLogs(@PathVariable("tab_id") Integer tabId,@PathVariable ("page_no") Integer pageNo,@PathVariable ("size_no") Integer sizeNo)
    {

        Pageable pg = PageRequest.of(pageNo,sizeNo,Sort.by("created_at").descending());

        List<ApiLogDto> financeConsumerLogList = new ArrayList<ApiLogDto>();
        List<ApiLogDto>D365LogList = new ArrayList<ApiLogDto>();
        List<ApiLogDto> response=new ArrayList<ApiLogDto>();
        ResponseLogDto responseLogDto=new ResponseLogDto();

        if(1 == tabId){
            responseLogDto= apiLogService.genericCall(Constants.ItemMaster.URL,Constants.ItemMaster.OrderType,pg);
            log.info("[ApiLogController][allLogs] Item Master logs retrieved - tabId: {} | logCount: {} | page: {}",
                    tabId, responseLogDto.getCount(), pageNo);
        } else if (2 == tabId) {
            responseLogDto=apiLogService.genericCall(Constants.PackingSlip.URL,Constants.PackingSlip.OrderType,pg);
        } else if (3 == tabId) {
            responseLogDto=apiLogService.genericCall(Constants.SalesOrder.URL,Constants.SalesOrder.OrderType,pg);
        }
        ResponseLogDto page = (responseLogDto);
        return new ResponseEntity( page, HttpStatus.OK);
    }

    @GetMapping(value = "/csv/{tab_id}")
    public  void Download(@PathVariable("tab_id") Integer tabId, HttpServletResponse servletResponse) throws IOException {
        apiLogService.exportLogData(tabId,servletResponse);
    }
    @PostMapping("/retry")
    @ResponseBody
    public boolean Retry(@RequestBody List<ApiLogDto> list ) throws Exception {
        log.info("[Retry]");
        int Count_t = 0,Count_f=0;
        boolean result = false;
        for(ApiLogDto retryLog : list) {

            if(d365LogRepository.findById(retryLog.getId()).isPresent())
            {
                log.info("[Retry][D365log]");
                String id=retryLog.getId();
                result=apiLogService.retryD365Log(id);
                log.info("Result obtained is"+result);

            }
            else if(financeConsumerLogsRepository.findById(retryLog.getId()).isPresent())
            {
                log.info("[Retry][financeConsumerLogs]");
                String id=retryLog.getId();
                result=apiLogService.retryFinanceConsumerLog(id);

            }
            else
            {
                result=false;
            }

            if(result)
            {
                Count_t= Count_t +1;
            }
            else
            {
                Count_f= Count_f +1;
            }

        }
        if(Count_t>=Count_f)
        {
            result =true;
        }
        else
        {
            result=false;
        }

        log.info("result : "+result);
        return result;

    }
    @PostMapping("/skip")
    @ResponseBody
    public boolean Skip(@RequestBody List<ApiLogDto> list ) throws Exception {
        log.info("[skip]");
        int Count_t = 0,Count_f=0;
        boolean result = false;
        for(ApiLogDto skipLog : list) {
            if(d365LogRepository.findById(skipLog.getId()).isPresent())
            {
                log.info("[Skip][D365log]");
                String id=skipLog.getId();
                result=apiLogService.skipD365Log(id);
                log.info("Result obtained is"+result);

            }
            else if(financeConsumerLogsRepository.findById(skipLog.getId()).isPresent())
            {
                log.info("[Skip][financeConsumerLogs]");
                String id=skipLog.getId();
                result=apiLogService.skipFinanceConsumerLog(id);
                log.info("Result obtained is"+result);

            }
            else
            {
                result=false;
            }

            if(result)
            {
                Count_t= Count_t +1;
            }
            else
            {
                Count_f= Count_f +1;
            }

        }
        if(Count_t>=Count_f)
        {
            result =true;
        }
        else
        {
            result=false;
        }
        return result;
    }


}
