package com.lenskart.financeConsumer.config.scmdb;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import java.util.Properties;


@Configuration
@ConfigurationProperties("pos.spring.readonly.datasource")
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = {"com.lenskart.financeConsumer.pos.readOnlyRepository"},
        entityManagerFactoryRef = "posReadOnlyEntityManagerFactory",
        transactionManagerRef = "posReadOnlyTransactionManager"
)
public class ScmPosSlaveDBConfiguration extends HikariConfig {

    @Autowired
    Environment env;

    @Value("${pos.database.name}")
    String posDb;

    @Value("${pos.slave.maxAliveConnections}")
    int maxPoolSizeForDataSource;
    @Value("${pos.slave.minIdleConnections}")
    int minPoolSizeForDataSource;

    public final static Properties JPA_PROPERTIES = new Properties() {{
        put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        put("show-sql", "true");
        put("hibernate.ddl-auto","none");
    }};


    @Bean
    public HikariDataSource dataSourcePosReadOnly() {
        return new HikariDataSource(this);
    }

    @Bean(name = "posReadOnlyEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean posReadOnlyEntityManagerFactory(
            final HikariDataSource dataSourcePos) {
        dataSourcePos.setMaximumPoolSize(maxPoolSizeForDataSource);
        dataSourcePos.setMinimumIdle(minPoolSizeForDataSource);
        return new LocalContainerEntityManagerFactoryBean() {{
            setDataSource(dataSourcePos);
            setPersistenceProviderClass(HibernatePersistenceProvider.class);
            setPersistenceUnitName(posDb);
            setPackagesToScan("com.lenskart.financeConsumer.model.pos");
            setJpaProperties(JPA_PROPERTIES);
        }};
    }

    @Bean(name = "posReadOnlyTransactionManager")
    public PlatformTransactionManager posReadOnlyTransactionManager(@Qualifier("posReadOnlyEntityManagerFactory") EntityManagerFactory posReadOnlyEntityManagerFactory) {
        return new JpaTransactionManager(posReadOnlyEntityManagerFactory);
    }
}
