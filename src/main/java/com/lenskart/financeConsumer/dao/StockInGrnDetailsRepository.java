package com.lenskart.financeConsumer.dao;

import com.lenskart.financeConsumer.model.StockInGrnDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface StockInGrnDetailsRepository extends CrudRepository<StockInGrnDetails, Integer> {

    @Query(value = "Select * from stockin_invoice_details where grn_id = ?1 ", nativeQuery = true)
    public StockInGrnDetails getStockInGrnDetails(String grnCode);

}