package com.lenskart.financeConsumer.dao;

import com.lenskart.core.model.OrdersHeader;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface OrdersHeaderRepository extends CrudRepository<OrdersHeader, Integer> {

    @Query(value = "SELECT * FROM inventory.orders_header WHERE increment_id=?1 limit 1", nativeQuery=true)
    public OrdersHeader findByIncrementId(Integer incrementId);
}
