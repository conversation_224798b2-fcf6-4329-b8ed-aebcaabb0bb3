package com.lenskart.financeConsumer.dao;

import com.lenskart.core.model.D365TransferJournalTracking;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface D365TransferJournalTrackingRepository extends CrudRepository<D365TransferJournalTracking, Integer> {
    List<D365TransferJournalTracking> findByUwItemId(Integer uwItemId);
}