package com.lenskart.financeConsumer.dao;

import com.lenskart.financeConsumer.model.StockinPoItemDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface StockInPoItemDetailsRepository extends CrudRepository<StockinPoItemDetails, Integer> {

    @Query(value = "Select * from stockin_po_item_details where po_code = ?1 ",nativeQuery = true)
    public List<StockinPoItemDetails> getStockInPOItemDetailsListUsingPoCode(String poCode);

}
