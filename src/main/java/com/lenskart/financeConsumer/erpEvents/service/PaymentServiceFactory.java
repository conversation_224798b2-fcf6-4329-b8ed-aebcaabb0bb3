package com.lenskart.financeConsumer.erpEvents.service;

import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentCreationRequestDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentServiceFactory {

    @Autowired
    private    PaymentD365SyncServiceV1 paymentD365SyncServiceV1;
    @Autowired
    private   PaymentD365SyncServiceV2 paymentD365SyncServiceV2;

    private static PaymentD365SyncServiceV1 paymentService1;

    private static PaymentD365SyncServiceV2 paymentService2;


    @PostConstruct
    public void init(){
        paymentService1 = paymentD365SyncServiceV1;
        paymentService2 = paymentD365SyncServiceV2;
    }

    public static PaymentService  getPaymentServiceInstance(String version){

        switch (version){
            case "v1" : return paymentService1;
            case "v2" : return paymentService2;
        }
        return null;

    }

}
