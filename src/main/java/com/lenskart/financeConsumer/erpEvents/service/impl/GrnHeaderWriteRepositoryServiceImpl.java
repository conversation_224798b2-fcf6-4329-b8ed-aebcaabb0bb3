package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.erpEvents.dao.write.GrnHeaderWriteRepository;
import com.lenskart.financeConsumer.erpEvents.model.grn.GrnHeader;
import com.lenskart.financeConsumer.erpEvents.service.GrnHeaderWriteRepositoryService;
import com.lenskart.financeConsumer.exceptions.FinanceDataBaseException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class GrnHeaderWriteRepositoryServiceImpl implements GrnHeaderWriteRepositoryService {
    @Autowired
    private GrnHeaderWriteRepository grnHeaderWriteRepository;

    @Override
    public GrnHeader findByProductReceipt(String productReceipt) {
        try {
            return grnHeaderWriteRepository.findByProductReceipt(productReceipt);
        } catch (Exception e) {
            log.error("[GrnHeaderWriteRepositoryServiceImpl] [findByProductReceipt] exception occurred", e);
            throw new FinanceDataBaseException(e.getMessage());
        }
    }

    @Override
    @Transactional(value = "financeDbTransactionManager", rollbackFor = {Exception.class})
    public void save(GrnHeader grnHeader) {
        try {
            grnHeaderWriteRepository.save(grnHeader);
        } catch (Exception e) {
            log.error("[GrnHeaderWriteRepositoryServiceImpl] [save] exception occurred", e);
            throw new FinanceDataBaseException(e.getMessage());
        }
    }
}
