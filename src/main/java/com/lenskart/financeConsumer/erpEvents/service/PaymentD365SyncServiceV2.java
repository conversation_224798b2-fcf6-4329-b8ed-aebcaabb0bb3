package com.lenskart.financeConsumer.erpEvents.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.erpEvents.clients.FinanceAdaptorClient;
import com.lenskart.financeConsumer.erpEvents.constant.Constants;
import com.lenskart.financeConsumer.erpEvents.dao.write.PaymentD365SyncWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dao.write.PaymentHeaderRepository;
import com.lenskart.financeConsumer.erpEvents.dto.ErpRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.D365PaymentCreation;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.D365PaymentCreationDto;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.D365PaymentLine;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentCreationRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentPayloadResponse;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.finance.api.JunoPaymentPersistResponse;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.PaymentCreation;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.kafka.JunoPaymentD365Message;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.kafka.JunoPaymentD365ResponseData;
import com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.kafka.JunoPaymentD365ResponseKafkaPayload;
import com.lenskart.financeConsumer.erpEvents.model.PaymentD365Sync;
import com.lenskart.financeConsumer.erpEvents.model.PaymentHeader;
import com.lenskart.financeConsumer.erpEvents.model.PaymentLine;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.lenskart.financeConsumer.erpEvents.constant.Constants.PAYMENT_D365_RESPONSE_KAFKA_PROCESSOR;
import static com.lenskart.financeConsumer.model.enums.D365ResponseType.SUCCESS;


@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentD365SyncServiceV2 implements  PaymentService {

    private final PaymentD365SyncWriteRepository paymentD365SyncWriteRepository;

    private final ObjectMapper objectMapper;

    private final PaymentHeaderRepository paymentHeaderRepository;


    @Value("${d365.doc-to-sql.kafka.topic}")
    private String documentToSQLPersistenceTopic;

    private RestTemplate restTemplate =new RestTemplate();

    @Value("${payments.sublist.batch.size:1500}")
    private int subListBatchSize;

    @Value("${payments.cron.start-hours:24}")
    private int hoursBeforeCurrentTime;

    @Value("${payments.cron.enabled:true}")
    private boolean isPaymentCronEnabled;

    @Value("${payments.cron.page.size:2000}")
    private int cronPageSize;

    @Value("${juno.d365.response.kafka.topic}")
    private String junoD365ResponseKafkaTopic;

    @Value("${juno-orders.base.url}")
    String junoOrderBaseUrl;

    @Value("${d365.payment.kafka.topic}")
    String paymentTopic;

    @Value("${financeAdapter.url}")
    private String financeAdapterUrl;

    private final FinanceAdaptorClient financeAdaptorClient;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    public PaymentCreation fetchJunoPaymentPayload(int orderId , boolean isExchange){

        try{
            String url =junoOrderBaseUrl + "/v2/orders/d365/payment/fetchPayload" ;

            URI uri = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("orderId", orderId)
                    .queryParam("isExchange",isExchange )
                    .build()
                    .toUri();

            log.info(" syncJunoPaymentToD365 :  uri :  {} for orderId: {}" , uri , orderId);


            ResponseEntity<JunoPaymentPayloadResponse> junoPaymentCreationRequestResponse = restTemplate.
                    getForEntity(uri,JunoPaymentPayloadResponse.class);
            return junoPaymentCreationRequestResponse.getBody().getJunoPaymentCreationRequestDto().getPaymentCreation();

        }catch (Exception e){
            log.error("payload fetching failed for payment for orderId : {} ",orderId, e);
            return null;
        }
    }


    public void syncListOfPaymentsToD365(List<Long> customerAdvanceIds){
        List<PaymentD365Sync> paymentD365SyncList =null;

        if(CollectionUtils.isEmpty(customerAdvanceIds)){
            return;
        }

        int batchSizeForSublist = subListBatchSize;  // should be at least 2000
        int leftIds = customerAdvanceIds.size();
        int start = 0;
        List<Long> idsToProcess;
        while (leftIds > 0) {

            if (leftIds >= batchSizeForSublist) {
                idsToProcess = customerAdvanceIds.subList(start, start + batchSizeForSublist);
                start = start + batchSizeForSublist;
                leftIds -= batchSizeForSublist;
            } else {
                idsToProcess = customerAdvanceIds.subList(start, start + leftIds);
                start = start + idsToProcess.size();
                leftIds -= idsToProcess.size();
            }

            paymentD365SyncList = paymentD365SyncWriteRepository.findAllByIdIn(idsToProcess);

            if(CollectionUtils.isEmpty(paymentD365SyncList)){
               break;
            }
            pushBatchToKafkaForD365Sync(paymentD365SyncList);
        }// processed all ids


    }

    private void pushBatchToKafkaForD365Sync(List<PaymentD365Sync> paymentD365SyncList){

        for(PaymentD365Sync paymentD365Sync : paymentD365SyncList){
                JunoPaymentCreationRequestDto junoPaymentCreationRequestDto = JunoPaymentCreationRequestDto.builder().
                        orderId(paymentD365Sync.getOrderId()).
                        isExchange(paymentD365Sync.getIsExchange()).
                        version(paymentD365Sync.getInstanceType()).
                        build();
            pushToKafkaForD365Sync(junoPaymentCreationRequestDto);
        }
    }

    public JunoPaymentPersistResponse persistPayment(JunoPaymentCreationRequestDto junoPaymentCreationRequestDto){

       try{
           Integer orderId = junoPaymentCreationRequestDto.getOrderId();
           Integer customerId = junoPaymentCreationRequestDto.getCustomerId();
           Boolean isExchange = junoPaymentCreationRequestDto.getIsExchange();
           String legalEntity = junoPaymentCreationRequestDto.getLegalEntity();
           log.info("juno request payload for v2  : {}",junoPaymentCreationRequestDto);
           PaymentD365Sync paymentD365Sync = paymentD365SyncWriteRepository.findByOrderIdAndIsExchangeAndInstanceType(orderId,isExchange,"v2");

           if(Objects.isNull(paymentD365Sync)){
               paymentD365Sync = PaymentD365Sync.builder().
                       customerId(customerId).
                       orderId(orderId).
                       legalEntity(legalEntity).
                       isExchange(isExchange).
                       retryCount(0).
                       instanceType("v2").
                       source(junoPaymentCreationRequestDto.getSource()).
                       createdBy("finance-consumer").
                       updatedBy("finance-consumer").
                       build();

               paymentD365SyncWriteRepository.save(paymentD365Sync);
               persistPayloadInSQL(junoPaymentCreationRequestDto);
           }

           pushToKafkaForD365Sync(junoPaymentCreationRequestDto);
           return buildJunoPaymentPersistResponse(true,"payment event successfully persisted!");

       }catch (Exception e){
           log.error("Exception occured while persisting Payment event for orderId : {} ",junoPaymentCreationRequestDto.getOrderId(),e);
           return buildJunoPaymentPersistResponse(false,"payment event persistence failed!");
       }

    }

    public void pushToKafkaForD365Sync(JunoPaymentCreationRequestDto junoPaymentCreationRequestDto){
        try{
            kafkaProducerTemplate.send(
                    paymentTopic,junoPaymentCreationRequestDto.getOrderId().toString(),objectMapper.writeValueAsString(junoPaymentCreationRequestDto));

        }catch (Exception e){
            log.error("Exception occured while sending Payment event for D365 sync to kafka  for orderId : {} ",junoPaymentCreationRequestDto.getOrderId(),e);
        }
    }


    public JunoPaymentPersistResponse buildJunoPaymentPersistResponse(boolean isSuccess,String message){
        JunoPaymentPersistResponse junoPaymentPersistResponse =new JunoPaymentPersistResponse();
        junoPaymentPersistResponse.setResponse(message); //"payment event persistence failed!"
        junoPaymentPersistResponse.setSuccess(isSuccess);
        return junoPaymentPersistResponse;
    }

    public void syncJunoPaymentToD365(JunoPaymentCreationRequestDto junoPaymentCreationRequestDto){

        log.info("starting to sync juno payment for orderID : {} to D365",junoPaymentCreationRequestDto.getOrderId());

        Integer orderId = junoPaymentCreationRequestDto.getOrderId();
        PaymentCreation paymentCreation = junoPaymentCreationRequestDto.getPaymentCreation();
        PaymentD365Sync paymentD365Sync = paymentD365SyncWriteRepository.findByOrderIdAndIsExchangeAndInstanceType(orderId, junoPaymentCreationRequestDto.getIsExchange(),"v2");

        if("SUCCESS".equalsIgnoreCase(paymentD365Sync.getD365SyncStatus())){
            log.info("payment with orderID : {} is already synced to D365", junoPaymentCreationRequestDto.getOrderId());
            return;
        }


        if(Objects.isNull(paymentCreation)){
            // call to Juno and fetch payment Payload
            junoPaymentCreationRequestDto.setPaymentCreation(fetchJunoPaymentPayload(paymentD365Sync.getOrderId(), paymentD365Sync.getIsExchange()));
        }

        D365PaymentCreation d365PaymentCreation = buildD365PaymentPayload(junoPaymentCreationRequestDto.getPaymentCreation());

        if(Objects.isNull(d365PaymentCreation)){
            paymentD365Sync.setResponseMessage("failed to fetch Juno payment payload");
            paymentD365Sync.setD365SyncStatus("FAILURE");
            paymentD365SyncWriteRepository.save(paymentD365Sync);
            log.error("payload details failed to copy from juno payload : {} for orderID : {}",paymentCreation,orderId);
            return;
        }

        String url = financeAdapterUrl + Constants.PAYMENT_URL_ENDPOINT_V2;
        ErpRequestDto erpRequestDto = ErpRequestDto.builder().
                id(paymentD365Sync.getId()).
                requestPayload(d365PaymentCreation).
                entityId(paymentD365Sync.getOrderId().toString()).
                build();
        log.info("sending payment sync request to finance-adaptor for orderID : {} , request : {}", paymentD365Sync.getOrderId(),erpRequestDto);

        ResponseEntity<String> response = financeAdaptorClient.genericApiCall(url, HttpMethod.POST,erpRequestDto);

        log.info("response for  for orderID : {} , request : {} is response : {}", paymentD365Sync.getOrderId(),erpRequestDto, response);

        persistD365ResponseAndAcknowledgeJuno(paymentD365Sync,response);
    }


    private JunoPaymentD365ResponseKafkaPayload buildJunoPaymentD365KafkaResponePayload(Integer orderId,Boolean isExchange  , String d365SyncStatus, String responseMessage) {

        JunoPaymentD365ResponseKafkaPayload junoPaymentD365ResponseKafkaPayload = new JunoPaymentD365ResponseKafkaPayload();
        junoPaymentD365ResponseKafkaPayload.setCount(0);
        junoPaymentD365ResponseKafkaPayload.setKey(orderId.toString());
        junoPaymentD365ResponseKafkaPayload.setTopic(junoD365ResponseKafkaTopic);
        junoPaymentD365ResponseKafkaPayload.setType(PAYMENT_D365_RESPONSE_KAFKA_PROCESSOR);


        JunoPaymentD365ResponseData junoPaymentD365ResponseData = new JunoPaymentD365ResponseData();
        junoPaymentD365ResponseData.setD365ResponseMessage(responseMessage);
        junoPaymentD365ResponseData.setD365SyncStatus(d365SyncStatus);
        junoPaymentD365ResponseData.setOrderId(orderId);
        junoPaymentD365ResponseData.setIsExchange(isExchange);

        JunoPaymentD365Message junoPaymentD365Message = new JunoPaymentD365Message();

        junoPaymentD365Message.setData(junoPaymentD365ResponseData);
        junoPaymentD365Message.setArgs(null);
        junoPaymentD365Message.setHeader(null);

        junoPaymentD365ResponseKafkaPayload.setMessage(junoPaymentD365Message);
        return junoPaymentD365ResponseKafkaPayload;
    }


    public D365PaymentCreation  buildD365PaymentPayload(PaymentCreation paymentCreation){
        D365PaymentCreation d365PaymentCreation =new D365PaymentCreation();
        D365PaymentCreationDto d365PaymentCreationDto =new D365PaymentCreationDto();
        D365PaymentLine d365PaymentLine;
        List<D365PaymentLine> d365PaymentLines =new ArrayList<>();


        // copy payment Lines
        if(Objects.nonNull(paymentCreation)){
            for(int i=0;i<paymentCreation.getPaymentHeader().getPaymentLines().size();i++){
                d365PaymentLine =new D365PaymentLine();
                BeanUtils.copyProperties(paymentCreation.getPaymentHeader().getPaymentLines().get(i),d365PaymentLine);
                d365PaymentLines.add(d365PaymentLine);
            }

            //copy header
            BeanUtils.copyProperties(paymentCreation.getPaymentHeader(),d365PaymentCreationDto);

            d365PaymentCreationDto.setPaymentLines(d365PaymentLines);

            // complete header and lines copied
            d365PaymentCreation.setPaymentHeader(d365PaymentCreationDto);
            return d365PaymentCreation;
        }

        return null;
    }

    @SneakyThrows
    public void persistD365ResponseAndAcknowledgeJuno(PaymentD365Sync paymentD365Sync, ResponseEntity<String> responseEntity){

        String responseMessage=null;
        boolean isSuccess =false;
        ErpResponseDto erpResponseDto =null;
        try{
            log.info(" response for order id : {} , is : {}",paymentD365Sync.getOrderId()  , objectMapper.writeValueAsString(responseEntity) );
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                erpResponseDto = objectMapper.readValue(responseEntity.getBody(), ErpResponseDto.class);
            } else {
                erpResponseDto = ErpResponseDto.builder()
                        .response(responseEntity.getBody())
                        .d365SyncStatus(D365ResponseType.FAILED)
                        .build();
            }
            responseMessage =erpResponseDto.getResponse();
            isSuccess = erpResponseDto.getD365SyncStatus() == SUCCESS;
        }catch (Exception e){
            log.error("Exception occured while persisting D365 response for payment for orderID : {}", paymentD365Sync.getOrderId(),e);
            responseMessage = CommonUtils.getErrorMessage(e);
            isSuccess = erpResponseDto.getD365SyncStatus() == SUCCESS;
        }finally {
            log.info("persisting result for order id : {}",paymentD365Sync.getOrderId() );
            paymentD365Sync.setResponseMessage(responseMessage);
            paymentD365Sync.setUpdatedAt(LocalDateTime.now());
            if(Objects.nonNull(paymentD365Sync.getRetryCount())){
                paymentD365Sync.setRetryCount(paymentD365Sync.getRetryCount()+1);
            }
            paymentD365Sync.setD365SyncStatus(isSuccess ? "SUCCESS" : "FAILURE");
            if(responseMessage.contains("TransactionId already found")){
                paymentD365Sync.setD365SyncStatus("SUCCESS");
            }
            paymentD365SyncWriteRepository.save(paymentD365Sync);
            kafkaProducerTemplate.send(junoD365ResponseKafkaTopic ,paymentD365Sync.getOrderId().toString(),
                    objectMapper.writeValueAsString(
                            buildJunoPaymentD365KafkaResponePayload(paymentD365Sync.getOrderId(), paymentD365Sync.getIsExchange() ,paymentD365Sync.getD365SyncStatus(),responseMessage)));
        }


    }


    public void syncFailedAndUnsyncedPaymentRecords(){
        LocalDateTime endDateTime =null;
        LocalDateTime startDateTime =null;

        try{
          endDateTime = LocalDateTime.now();
          startDateTime = endDateTime.minusHours(hoursBeforeCurrentTime);

            if(!isPaymentCronEnabled){
                log.info("syncFailedAndUnsyncedPaymentRecords : Terminating failed payment  sync job as  scheduled cron is disabled ");
                return;
         }

         Pageable pageable;
         int currentPage=0;
         int totalRecordsProcessed = 0;
         int pageLimit = cronPageSize;

         while(true){
                pageable= PageRequest.of(currentPage,pageLimit, Sort.by("id"));
                List<PaymentD365Sync> nonSyncedPaymentRecords = paymentD365SyncWriteRepository.findNonSyncedPaymentRecords(startDateTime,endDateTime,pageable);
                if(nonSyncedPaymentRecords.isEmpty()){
                    break;
                }
                pushBatchToKafkaForD365Sync(nonSyncedPaymentRecords);
                totalRecordsProcessed+=nonSyncedPaymentRecords.size();
                currentPage++;
            }
            log.info("syncFailedAndUnsyncedPaymentRecords : total processed  records retried : {}", totalRecordsProcessed);
        }catch (Exception e){
            log.error("Error occured while syncing payment records : with startDate : {}, endDate : {} ",startDateTime,endDateTime,e);
        }
    }

    @Override
    public void  persistPayloadInSQL(JunoPaymentCreationRequestDto junoPaymentCreationRequestDto){
        PaymentHeader paymentHeader = paymentHeaderRepository.findByDocumentNumberAndIsExchange(junoPaymentCreationRequestDto.getOrderId().toString(),junoPaymentCreationRequestDto.getIsExchange());
        if(Objects.isNull(paymentHeader)){
            try{
                KafkaEventDto kafkaEventDto = KafkaEventDto.builder().
                        documentPayload(objectMapper.writeValueAsString(junoPaymentCreationRequestDto)).
                        eventType("PAYMENT").
                        build();

                kafkaProducerTemplate.send(documentToSQLPersistenceTopic,junoPaymentCreationRequestDto.getOrderId().toString(),objectMapper.writeValueAsString(
                        kafkaEventDto));
            }catch (Exception e){
                log.error(" error occured while sending payload for SQL persistence : ",e);
            }

        }


    }


    @SneakyThrows
    public void persistPaymentHeader(String paymentHeaderPayload){

        JunoPaymentCreationRequestDto junoPaymentCreationRequestDto = objectMapper.readValue(paymentHeaderPayload,JunoPaymentCreationRequestDto.class);
        buildAndPersistPaymentHeaderFromDto(junoPaymentCreationRequestDto);

    }

    public void buildAndPersistPaymentHeaderFromDto(JunoPaymentCreationRequestDto junoPaymentCreationRequestDto){

        PaymentHeader paymentHeader = PaymentHeader.builder().
                documentNumber(junoPaymentCreationRequestDto.getPaymentCreation().getPaymentHeader().getDocumentNumber()).
                isExchange(junoPaymentCreationRequestDto.getIsExchange()).
                journalName(junoPaymentCreationRequestDto.getPaymentCreation().getPaymentHeader().getJournalName()).
                legalEntity(junoPaymentCreationRequestDto.getLegalEntity()).
                build();

        List<PaymentLine> paymentLines =new ArrayList<>();
        paymentHeader.setPaymentLines(paymentLines);

        for(com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.PaymentLine paymentLine : junoPaymentCreationRequestDto.getPaymentCreation().getPaymentHeader().getPaymentLines()){
            PaymentLine currentPaymentLine = PaymentLine.builder().
                    paymentGateway(paymentLine.getPaymentGateway()).
                    paymentHeader(paymentHeader).
                    paymentMethod(paymentLine.getPaymentMethod()).
                    paymentReference(paymentLine.getPaymentReference()).
                    paymentStatus(paymentLine.getPaymentStatus()).
                    paymentType(paymentLine.getPaymentType()).
                    prePayment(paymentLine.getPrePayment()).
                    account(paymentLine.getAccount()).
                    creditAmount(paymentLine.getCreditAmount()).
                    transactionDate(DateUtils.getDateFromStringType(paymentLine.getTransactionDate())).
                    transactionId(paymentLine.getTransactionId()).
                    debitAmount(paymentLine.getDebitAmount()).
                    currencyCode(paymentLine.getCurrencyCode()).
                    description(paymentLine.getDescription()).
                    gst(paymentLine.getGst()).
                    stores(paymentLine.getStores()).
                    site(paymentLine.getSite()).
                    documentNo(paymentLine.getDocumentNumber()).
                    entryNo(paymentLine.getEntryNo()).
                    salesOrder(paymentLine.getSalesOrder()).
                    build();
            paymentHeader.addPaymentLine(currentPaymentLine);
        }
        paymentHeader.setCreatedBy("finance-consumer");
        paymentHeader.setUpdatedBy("finance-consumer");

        paymentHeaderRepository.save(paymentHeader);
    }
}
