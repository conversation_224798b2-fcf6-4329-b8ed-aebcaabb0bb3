package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.dto.d365requests.GrnDTOs.GrnHeader;
import com.lenskart.financeConsumer.erpEvents.constant.Constants;
import com.lenskart.financeConsumer.erpEvents.dao.write.GrnPublishStatusRepository;
import com.lenskart.financeConsumer.erpEvents.dto.ErpRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.GRNPublishRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.GrnPublishEventDto;
import com.lenskart.financeConsumer.erpEvents.dto.grn.Grn;
import com.lenskart.financeConsumer.erpEvents.model.GrnPublishStatus;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.erpEvents.util.Source;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.POSD365GrnSyncService;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
public class GrnPublisherService {

    @Autowired
    private NexsClient nexsClient;

    @Autowired
    private POSD365GrnSyncService posd365GrnSyncService;

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private GrnPublishStatusRepository grnPublishStatusRepository;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Value("${grn.event.consumer.topic}")
    private String grnEventTopic;

    public static Pattern successRegex= Pattern.compile(".*already created successfully for.*",Pattern.CASE_INSENSITIVE);


    public ResponseEntity<?> publishGrns(GRNPublishRequestDto grnPublishRequestDto) {
        log.info("Request to publish GRNs using entityIds {} from source {}", grnPublishRequestDto.getGrnList(), grnPublishRequestDto.getSource());

        if (CollectionUtils.isEmpty(grnPublishRequestDto.getGrnList()) || Objects.isNull(grnPublishRequestDto.getSource()) ||
                StringUtils.isEmpty(grnPublishRequestDto.getSource().name())) {
            return ResponseEntity.badRequest().body("Invalid input: entityIds and source are required.");
        }
        if (Source.SCM.equals(grnPublishRequestDto.getSource()) && StringUtils.isEmpty(grnPublishRequestDto.getFacilityCode())) {
            return ResponseEntity.badRequest().body("Invalid input: facilityCode are required.");
        }
        
        List<GrnPublishEventDto> grnPublishDtoList=convertGRNPublishRequestDtoToGrnPublishEventDto(grnPublishRequestDto);
        
        pushToKafka(grnPublishDtoList);

        return ResponseEntity.ok("GRNs published successfully.");
    }
    public  List<GrnPublishEventDto> convertGRNPublishRequestDtoToGrnPublishEventDto(GRNPublishRequestDto grnPublishRequestDto){
        
        List<GrnPublishEventDto> grnPublishEventDtoList= new ArrayList<>();
        
        for(String grnCode: grnPublishRequestDto.getGrnList()){
            GrnPublishEventDto  grnPublishEventDto=buildGRNPublishDto(grnCode,grnPublishRequestDto.getSource(),grnPublishRequestDto.getFacilityCode());
            grnPublishEventDtoList.add(grnPublishEventDto);
        }
        return grnPublishEventDtoList;
    }
    private GrnPublishEventDto buildGRNPublishDto(String grnCode, Source source, String facilityCode) {
        return GrnPublishEventDto.builder()
                .grn(grnCode)
                .facilityCode(facilityCode)
                .source(source)
                .build();
    }


    public void pushToKafka(List<GrnPublishEventDto> grnPublishDtos) {

        for (GrnPublishEventDto grnPublishDto : grnPublishDtos) {
            kafkaProducerTemplate.send(grnEventTopic.trim(), grnPublishDto.getSource()+"_"+grnPublishDto.getGrn(), ObjectHelper.writeValueWithDateTime(grnPublishDto));
        }
    }


    public GrnPublishStatus sendGrnToNexs(GrnPublishEventDto grnPublishDto) {
        log.info("[GrnPublisherService][sendGrnToNexs] Request to prepare and process NEXS GRN: {}", grnPublishDto.getGrn());
        try {

            Grn grnPayload = nexsClient.getGrnPayload(grnPublishDto.getGrn());
            if (Objects.isNull(grnPayload) || Objects.isNull(grnPayload.getReceiptHeader())) {
                throw new Exception("Unable to get the payload for GRN: " + grnPublishDto.getGrn());
            }

            ErpRequestDto erpRequestDto = commonUtils.buildAndGetErpRequestDto(
                    null, grnPublishDto.getGrn(), grnPayload, FinanceServiceEventTypes.PUBLISH_GRN);

            ErpResponseDto erpResponseDto = commonUtils.callFinanceAdaptor(Constants.GRN_PUBLISH_URL, erpRequestDto,0l);

            return GrnPublishStatus.builder()
                    .grnCode(grnPublishDto.getGrn())
                    .source(grnPublishDto.getSource())
                    .facilityCode(grnPublishDto.getFacilityCode())
                    .d365_sync_status(D365ResponseType.FAILED.name())
                    .message(erpResponseDto.getResponse())
                    .build();

        } catch (Exception exception) {
            log.error("[GrnPublisherService][sendGrnToNexs] Exception processing Nexs GRN {}: {}", grnPublishDto.getGrn(), exception.getMessage(), exception); // More detailed log

            return GrnPublishStatus.builder()
                    .grnCode(grnPublishDto.getGrn())
                    .source(grnPublishDto.getSource())
                    .facilityCode(grnPublishDto.getFacilityCode())
                    .d365_sync_status(D365ResponseType.FAILED.name())
                    .message(genericClientService.getErrorMessage(exception))
                    .build();
        }
    }

    public GrnPublishStatus sendGrnToScm(GrnPublishEventDto grnPublishDto) {
        log.info("[GrnPublisherService][sendGrnToScm] Request to prepare and process SCM GRN: {}", grnPublishDto.getGrn());
        try {

            GrnHeader grnPayload = posd365GrnSyncService.getPayloadByGrnCode(grnPublishDto.getGrn(),grnPublishDto.getFacilityCode());

            ErpRequestDto erpRequestDto = commonUtils.buildAndGetErpRequestDto(
                    null, grnPublishDto.getGrn(), grnPayload, FinanceServiceEventTypes.PUBLISH_GRN);

            ErpResponseDto erpResponseDto = commonUtils.callFinanceAdaptor(Constants.GRN_PUBLISH_URL, erpRequestDto, 0l);

            return GrnPublishStatus.builder()
                    .grnCode(grnPublishDto.getGrn())
                    .facilityCode(grnPublishDto.getFacilityCode())
                    .d365_sync_status(D365ResponseType.FAILED.name())
                    .source(grnPublishDto.getSource())
                    .message(erpResponseDto.getResponse())
                    .build();

        } catch (Exception exception) {
            log.error("[GrnPublisherService][sendGrnToScm] Exception processing SCM GRN {}: {}", grnPublishDto.getGrn(), exception.getMessage(), exception);
            return GrnPublishStatus.builder()
                    .grnCode(grnPublishDto.getGrn())
                    .source(grnPublishDto.getSource())
                    .facilityCode(grnPublishDto.getFacilityCode())
                    .d365_sync_status(D365ResponseType.FAILED.name())
                    .message(genericClientService.getErrorMessage(exception))
                    .build();
        }
    }

    public void upsert(GrnPublishStatus grnPublishStatus) {
        log.error("[GrnPublisherService][upsert] Request to persisting the entity {} ",grnPublishStatus);
        try {

            if(isSuccess(grnPublishStatus)) {
                grnPublishStatus.setD365_sync_status(D365ResponseType.SUCCESS.name());
            }

            Optional<GrnPublishStatus> optionalGrnPublishStatus=findExistingGrnPublishStatus(grnPublishStatus);
            if(optionalGrnPublishStatus.isPresent()){
                GrnPublishStatus existedGrnPublishStatus=optionalGrnPublishStatus.get();
                existedGrnPublishStatus.setMessage(grnPublishStatus.getMessage());
                existedGrnPublishStatus.setD365_sync_status(grnPublishStatus.getD365_sync_status());
                grnPublishStatusRepository.save(existedGrnPublishStatus);
            }else{
                grnPublishStatusRepository.save(grnPublishStatus);
            }

        } catch (Exception exception) {
            log.error("[GrnPublisherService][upsert]Exception occurred while persisting the entity {} {} {}",grnPublishStatus,exception,exception.getMessage());
        }
    }

    public Optional<GrnPublishStatus> findExistingGrnPublishStatus(GrnPublishStatus grnPublishStatus){
        switch (grnPublishStatus.getSource()) {
            case NEXS:
                return grnPublishStatusRepository.findByGrnCodeAndSource(grnPublishStatus.getGrnCode(),grnPublishStatus.getSource());
            case SCM:
                return grnPublishStatusRepository.findByGrnCodeAndFacilityCodeAndSource(grnPublishStatus.getGrnCode(),grnPublishStatus.getFacilityCode(),grnPublishStatus.getSource());
            default:
                throw new RuntimeException("Invalid Source.");
        }

    }
    public boolean isSuccess(GrnPublishStatus grnPublishStatus){
        if (Objects.isNull(grnPublishStatus) || Objects.isNull(grnPublishStatus.getMessage())) {
            return false;
        }
        return successRegex.matcher(grnPublishStatus.getMessage()).find();
    }

    public boolean hasSuccessfulGrnProcessed(GrnPublishEventDto grnPublishEventDto){

        GrnPublishStatus grnPublishStatus=GrnPublishStatus.builder()
                .grnCode(grnPublishEventDto.getGrn())
                .facilityCode(grnPublishEventDto.getFacilityCode())
                .source(grnPublishEventDto.getSource()).build();

        Optional<GrnPublishStatus> optionalGrnPublishStatus= findExistingGrnPublishStatus(grnPublishStatus);
        if(optionalGrnPublishStatus.isPresent() &&  D365ResponseType.SUCCESS.name().equalsIgnoreCase(optionalGrnPublishStatus.get().getD365_sync_status())){
            return true;
        }

        return false;
    }
}
