package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.clients.JunoClient;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.erpEvents.constant.Constants;
import com.lenskart.financeConsumer.erpEvents.dao.write.CustomerHeaderWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dao.write.CustomerWriteRepository;
import com.lenskart.financeConsumer.erpEvents.dto.ErpRequestDto;
import com.lenskart.financeConsumer.erpEvents.dto.customer.*;
import com.lenskart.financeConsumer.erpEvents.dto.customer.kafka.JunoCustomerD365Message;
import com.lenskart.financeConsumer.erpEvents.dto.customer.kafka.JunoCustomerD365ResponseData;
import com.lenskart.financeConsumer.erpEvents.dto.customer.kafka.JunoCustomerD365ResponseKafkaPayload;
import com.lenskart.financeConsumer.erpEvents.dto.customer.request.CustomerCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.customer.response.CustomerPayloadResponse;
import com.lenskart.financeConsumer.erpEvents.dto.customer.response.CustomerResponseDto;
import com.lenskart.financeConsumer.erpEvents.dto.customer.response.JunoCustomerPayloadResponse;
import com.lenskart.financeConsumer.erpEvents.model.*;
import com.lenskart.financeConsumer.erpEvents.service.CustomerD365Service;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.v2.dto.ErpResponseDto;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.lenskart.financeConsumer.erpEvents.constant.Constants.CUSTOMER_D365_RESPONSE_KAFKA_PROCESSOR;

@Slf4j
@Service
public class CustomerD365ServiceV1 implements CustomerD365Service {

    @Autowired
    private CustomerWriteRepository customerWriteRepository;
    @Lazy
    @Autowired
    private JunoClient junoClient;
    @Autowired
    private GenericClientService genericClientService;
    @Autowired
    private KafkaTemplate kafkaProducerTemplate;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private DateUtils dateUtils;
    @Autowired
    private CustomerHeaderWriteRepository customerHeaderWriteRepository;

    @Value("${customer.consumer.topic:customer-d365-sync}")
    private String customerConsumerTopic;

    @Value("${d365.doc-to-sql.kafka.topic}")
    private String documentToSQLPersistenceTopic;

    @Value("${failure.customer.max.process.count:4000}")
    private int maxProcessCount;

    @Value("${failure.customer.last.startFromLast:10}")
    private int lastHour;

    @Value("${failure.customer.last.minutes:10}")
    private int lastMinutes;

    @Value("${failure.customer.max.retry:5}")
    private int maxRetry;

    @Value("${failure.customer.page.limit:10}")
    private int pageLimit;

    @Value("${juno.customer.d365.response.topic:customer_d365_response}")
    private String customerD365ResponseTopic;

    @Value("${customer.d365Failed.desiredReportCSVColumnNames}")
    private String[] desiredReportColumnNames;

    @Value("${customer.d365.failure.report.email.from}")
    private String mailSendFrom;
    @Value("${customer.d365.failure.report.email.sendto}")
    private String[] mailSendTo;
    @Value("${customer.d365.failure.report.email.cc}")
    private String[] mailCC;

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");


    @Override
    public CustomerResponseDto createCustomer(CustomerCreationRequest customerRequest) {
        log.info("[CustomerD365ServiceV1][saveAndProcess] Persisting customer entity: {}", customerRequest);
        try {

            Customer customer = Customer.builder()
                    .customerId(customerRequest.getCustomerId())
                    .orderId(customerRequest.getOrderId())
                    .legalEntity(customerRequest.getLegalEntity())
                    .customerGroup(customerRequest.getCustomerGroup())
                    .source(customerRequest.getSource())
                    .instanceType("v1")
                    .createdBy("finance-consumer")
                    .updatedBy("finance-consumer")
                    .build();

            Optional<Customer> optionalCustomer = customerWriteRepository.findByCustomerIdAndOrderIdAndLegalEntity(customerRequest.getCustomerId(), customerRequest.getOrderId(), customerRequest.getLegalEntity());
            if (optionalCustomer.isPresent())
                customer = optionalCustomer.get();
            else
                customer = customerWriteRepository.save(customer);


            CustomerDto customerDto = new CustomerDto();
            BeanUtils.copyProperties(customer, customerDto);

            customerDto.setPayload(customerRequest.getPayload());

            pushToKafka(customerDto);

            persistPayloadInSQL(customerRequest);


            return CustomerResponseDto.builder()
                    .isSuccess(true)
                    .response("Customer event successfully persisted!")
                    .build();
        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][saveAndProcess] Error persisting customer entity: {}. Error: {}", customerRequest, exception.getMessage(), exception);
            return CustomerResponseDto.builder().isSuccess(false)
                    .response("Failed to persist customer event.").build();
        }
    }

    @Override
    public CustomerPayloadResponse getPayload(Long orderId, Long customerId, String legalEntity, String version) {
        log.info("[CustomerD365ServiceV1][getPayload] Retrieving payload for customer by orderId: {} customerId {}", orderId, customerId);
        try {
            JunoCustomerPayloadResponse junoClientCustomerPayload = junoClient.getCustomerPayload(customerId, orderId, legalEntity, version);

            CustomerCreationRequest customerCreationRequest= junoClientCustomerPayload.getResult().stream().filter(obj->obj.getPayload().getLegalEntity().equalsIgnoreCase(legalEntity)).
                    findAny().orElseThrow(() -> new RuntimeException("No customer found for legal entity: " + legalEntity));

            return CustomerPayloadResponse.builder()
                    .status(true)
                    .payload(customerCreationRequest.getPayload())
                    .build();
        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][getPayload] Error fetching payload for customer: {} orderId {} Error: {}", customerId, orderId, exception.getMessage(), exception);

            return CustomerPayloadResponse.builder()
                    .status(false)
                    .message(genericClientService.getErrorMessage(exception))
                    .build();

        }
    }

    @Override
    public void pushToKafka(CustomerDto customerDto) {
        log.info("[CustomerD365ServiceV1][pushToKafka] Publishing customerDto  Kafka for sync: {}", customerDto);
        try {
            if (Objects.nonNull(customerDto) && !"SUCCESS".equalsIgnoreCase(customerDto.getD365SyncStatus())) {
                //add entity_id
                customerDto.setEntityId(getEntityId(customerDto));
                kafkaProducerTemplate.send(customerConsumerTopic, customerDto.getEntityId(), ObjectHelper.writeValueWithDateTime(customerDto));
            }

        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][pushToKafka] Error publishing customerDto  to Kafka: {}. Error: {}", customerDto, exception.getMessage(), exception);
            throw exception;
        }
    }

    @Override
    public ErpResponseDto processEvent(CustomerDto customerDto) {
        log.info("[CustomerD365ServiceV1][processEvent] Publishing customer entity to dynamics for sync: {}", customerDto);
        ErpResponseDto erpResponseDto;
        try {

            CustomerD365Dto customerD365Dto = CustomerD365Dto.builder().build();
            BeanUtils.copyProperties(customerDto.getPayload(), customerD365Dto);

            CustomerContract customerContract = CustomerContract.builder()
                    .contract(customerD365Dto)
                    .build();

            ErpRequestDto erpRequestDto = commonUtils.buildAndGetErpRequestDto(customerDto.getId(),
                    customerDto.getEntityId(),
                    customerContract,
                    FinanceServiceEventTypes.CUSTOMER);

            erpResponseDto = commonUtils.callFinanceAdaptor(Constants.CUSTOMER_CREATE_URL_V1,
                    erpRequestDto,
                    customerDto.getId());


        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][pushToKafka] Error publishing customer entity to dynamics: {}. Error: {}", customerDto, exception.getMessage(), exception);
            erpResponseDto = ErpResponseDto.builder()
                    .response(genericClientService.getErrorMessage(exception))
                    .d365SyncStatus(D365ResponseType.FAILED)
                    .id(customerDto.getId())
                    .entityId(customerDto.getEntityId())
                    .build();
        }

        return erpResponseDto;
    }


    public void publishToJuno(CustomerDto customerDto) {
        log.info("[CustomerD365ServiceV1][publishD365SyncStatusToKafka] Publishing customerDto D365 sync status to Kafka: {}", customerDto);
        try {
            JunoCustomerD365ResponseKafkaPayload junoCustomerD365ResponseKafkaPayload = buildJunoCustomerD365KafkaResponePayload(customerDto);
            kafkaProducerTemplate.send(customerD365ResponseTopic, customerDto.getEntityId(), ObjectHelper.writeValueWithDateTime(junoCustomerD365ResponseKafkaPayload));

        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][publishD365SyncStatusToKafka] Error publishing D365 sync status to Kafka: {}. Error: {}", customerDto, exception.getMessage(), exception);
        }
    }

    @Override
    public void retryFailureRecords() {
        try {
            Calendar fromDate = dateUtils.getPastCalendarDate(Calendar.HOUR, lastHour);
            Calendar toDate = dateUtils.getPastCalendarDate(Calendar.MINUTE, lastMinutes);

            int page = 0;
            int count = 0;
            while (count < maxProcessCount) {

                Pageable pageable = PageRequest.of(page, pageLimit);
                List<Customer> failedCustomerRecords = customerWriteRepository.findFailedCustomerRecord(D365ResponseType.FAILED.name(), fromDate.getTime(), toDate.getTime(), maxRetry, pageable);

                log.info("[CustomerD365ServiceV1] [retryFailureRecords] fromDate:{} | toDate:{} | page :{} | failedCustomerRecords size: {} | page:{} | maxCount:{}", fromDate, toDate, pageable.getPageNumber(), failedCustomerRecords.size(), page,
                        maxProcessCount);

                if (CollectionUtils.isEmpty(failedCustomerRecords)) {
                    break;
                }
                page = page + 1;
                count = count + failedCustomerRecords.size();

                processFailedRecords(failedCustomerRecords);
            }

            log.info("[CustomerD365ServiceV1][retryFailureRecords] : total failed records processed : {}", count);

        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][retryFailureRecords] Exception occurred while retrying the failed record error {}", exception.getMessage(), exception);
        }
    }

    public void processFailedRecords(List<Customer> customerList) {
        log.info("[CustomerD365ServiceV1][processFailedRecords] Retrying customer events - customerCount: {}",
                customerList != null ? customerList.size() : 0);
        try {
            for (Customer customer : customerList) {
                log.info("[CustomerD365ServiceV1][processFailedRecords] Processing retry for customer - customerId: {} | orderId: {} | retryCount: {}",
                        customer.getCustomerId(), customer.getOrderId(), customer.getRetryCount() + 1);

                customer.setD365SyncStatus(D365ResponseType.IN_PROGRESS.name());
                customer.setRetryCount(customer.getRetryCount() + 1);
                customer.setUpdatedBy("finance-consumer-retry");
                customerWriteRepository.save(customer);

                CustomerDto customerDto = new CustomerDto();
                BeanUtils.copyProperties(customer, customerDto);

                pushToKafka(customerDto);
            }
            log.info("[CustomerD365ServiceV1][processFailedRecords] Successfully retried customer events - processedCount: {}",
                    customerList.size());

        } catch (Exception exception) {
            log.error("[CustomerD365ServiceV1][processFailedRecords] Error retrying customer event sync - customerCount: {} | error: {}",
                    customerList != null ? customerList.size() : 0, exception.getMessage(), exception);
        }
    }

    public String getEntityId(CustomerDto customerDto) {
        return customerDto.getLegalEntity() + "_" + customerDto.getCustomerId() + "_" + customerDto.getOrderId();
    }

    @Override
    public void updateAndPublishToJuno(CustomerDto customerDto) {
        log.info("[CustomerD365ServiceV1][updateAndPublishToJuno] Updating customer and publishing to Juno - customerId: {} | orderId: {} | status: {}",
                customerDto.getCustomerId(), customerDto.getOrderId(), customerDto.getD365SyncStatus());
        Optional<Customer> optionalCustomer = customerWriteRepository.findById(customerDto.getId());
        if (optionalCustomer.isPresent()) {
            Customer customer = optionalCustomer.get();
            customer.setD365SyncStatus(customerDto.getD365SyncStatus());
            customer.setErrorMessage(customerDto.getErrorMessage());
            customerWriteRepository.save(customer);

            publishToJuno(customerDto);
            log.info("[CustomerD365ServiceV1][updateAndPublishToJuno] Successfully updated customer and published to Juno - customerId: {} | orderId: {}",
                    customerDto.getCustomerId(), customerDto.getOrderId());
        } else {
            log.warn("[CustomerD365ServiceV1][updateAndPublishToJuno] Customer not found for update - customerId: {} | orderId: {}",
                    customerDto.getCustomerId(), customerDto.getOrderId());
        }
    }

    @Override
    public CustomerResponseDto retryCustomerSyncToD365(List<Long> ids) {
        log.info("[CustomerD365ServiceV1][retryCustomerSyncToD365] Retrying customer sync - customerIdCount: {}",
                ids != null ? ids.size() : 0);
        try {
            if (CollectionUtils.isEmpty(ids))
                return CustomerResponseDto.builder().
                        response("Invalid input").isSuccess(false).build();

            List<Customer> optionalCustomer = customerWriteRepository.findAllById(ids);

            if(!CollectionUtils.isEmpty(optionalCustomer)) {
                log.info("[CustomerD365ServiceV1][retryCustomerSyncToD365] Found customers for retry - foundCount: {}",
                        optionalCustomer.size());
                processFailedRecords(optionalCustomer);
            } else {
                log.warn("[CustomerD365ServiceV1][retryCustomerSyncToD365] No customers found for provided IDs - requestedIdCount: {}",
                        ids.size());
            }

            return CustomerResponseDto.
                    builder()
                    .isSuccess(true)
                    .response("Customers published successfully").build();
        } catch (Exception e) {
            log.error("[CustomerD365ServiceV1][retryCustomerSyncToD365] Exception occurred while retrying customer sync - customerIdCount: {} | error: {}",
                    ids != null ? ids.size() : 0, e.getMessage(), e);
            return CustomerResponseDto.builder().response(e.getMessage())
                    .isSuccess(false).build();
        }
    }

    private JunoCustomerD365ResponseKafkaPayload buildJunoCustomerD365KafkaResponePayload(CustomerDto customerDto) {

        JunoCustomerD365ResponseKafkaPayload junoCustomerD365ResponseKafkaPayload = new JunoCustomerD365ResponseKafkaPayload();
        junoCustomerD365ResponseKafkaPayload.setCount(0);
        junoCustomerD365ResponseKafkaPayload.setKey(customerDto.getEntityId());
        junoCustomerD365ResponseKafkaPayload.setTopic(customerD365ResponseTopic);
        junoCustomerD365ResponseKafkaPayload.setType(CUSTOMER_D365_RESPONSE_KAFKA_PROCESSOR);


        JunoCustomerD365ResponseData junoCustomerD365ResponseData = new JunoCustomerD365ResponseData();
        junoCustomerD365ResponseData.setD365ResponseMessage(customerDto.getD365Response());
        junoCustomerD365ResponseData.setD365SyncStatus(customerDto.getD365SyncStatus());
        junoCustomerD365ResponseData.setCustomerId(customerDto.getCustomerId());
        junoCustomerD365ResponseData.setOrderId(customerDto.getOrderId());
        junoCustomerD365ResponseData.setLegalEntity(customerDto.getLegalEntity());
        junoCustomerD365ResponseData.setInstanceType(customerDto.getInstanceType());

        JunoCustomerD365Message junoPaymentD365Message = new JunoCustomerD365Message();

        junoPaymentD365Message.setData(junoCustomerD365ResponseData);
        junoPaymentD365Message.setArgs(null);
        junoPaymentD365Message.setHeader(null);

        junoCustomerD365ResponseKafkaPayload.setMessage(junoPaymentD365Message);
        return junoCustomerD365ResponseKafkaPayload;
    }

    @Override
    public void persistPayloadInSQL(CustomerCreationRequest customerCreationRequest) {
        String key=customerCreationRequest.getLegalEntity() + "_" + customerCreationRequest.getCustomerId() + "_" + customerCreationRequest.getOrderId();
        Optional<CustomerHeader> optionalCustomerHeader = customerHeaderWriteRepository.findByCustAccountAndOrderIdAndLegalEntity(String.valueOf(customerCreationRequest.getCustomerId()),customerCreationRequest.getOrderId(),customerCreationRequest.getLegalEntity());
        if (!optionalCustomerHeader.isPresent()) {
            try {
                KafkaEventDto kafkaEventDto = KafkaEventDto.builder().
                        documentPayload(ObjectHelper.writeValueWithDateTime(customerCreationRequest)).
                        eventType("CUSTOMER").
                        build();

                kafkaProducerTemplate.send(documentToSQLPersistenceTopic,key,ObjectHelper.writeValueWithDateTime(
                        kafkaEventDto));
            } catch (Exception e) {
                log.error("[CustomerD365ServiceV1][persistPayloadInSQL]Exception occurred while sending payload for SQL persistence",e);
            }

        }

    }
    @SneakyThrows
    public void persistCustomerHeader(String paymentHeaderPayload){
        CustomerCreationRequest customerCreationRequest = ObjectHelper.readValueWithDateTime(paymentHeaderPayload,CustomerCreationRequest.class);
        buildAndPersistCustomerHeaderFromDto(customerCreationRequest);

    }
    public void buildAndPersistCustomerHeaderFromDto(CustomerCreationRequest customerCreationRequest){
        Long orderId= customerCreationRequest.getOrderId();
        CustomerCreationDto customerCreationDto=customerCreationRequest.getPayload();

        CustomerHeader customerHeader=CustomerHeader.builder()
                .legalEntity(customerCreationDto.getLegalEntity())
                .custAccount(customerCreationDto.getCustAccount())
                .orderId(orderId)
                .customerName(customerCreationDto.getCustomerName())
                .firstName(customerCreationDto.getFirstName())
                .middleName(customerCreationDto.getMiddleName())
                .lastName(customerCreationDto.getLastName())
                .search(customerCreationDto.getSearch())
                .parentId(customerCreationDto.getParentId())
                .gender(customerCreationDto.getGender())
                .customerGroup(customerCreationDto.getCustomerGroup())
                .currency(customerCreationDto.getCurrency())
                .type(customerCreationDto.getType())
                .termsOfPayment(customerCreationDto.getTermsOfPayment())
                .site(customerCreationDto.getSite())
                .units(customerCreationDto.getUnits())
                .costCentre(customerCreationDto.getCostCentre())
                .store(customerCreationDto.getStore())
                .saleChannel(customerCreationDto.getSaleChannel())
                .partnerType(customerCreationDto.getPartnerType())
                .itemClassification(customerCreationDto.getItemClassification())
                .brand(customerCreationDto.getBrand())
                .employee(customerCreationDto.getEmployee())
                .build();

        List<CustomerContactLine> contactLines =new ArrayList<>();

        for(CustomerContactDto  customerContactDto: customerCreationDto.getCustomerContact()){
            CustomerContactLine customerContactLine = CustomerContactLine.builder()
                    .locationName(customerContactDto.getLocationName())
                    .locator(customerContactDto.getLocator())
                    .type(customerContactDto.getType())
                    .isPrimaryContact(customerContactDto.getIsPrimaryContact())
                    .customerHeader(customerHeader)
                    .build();

            contactLines.add(customerContactLine);
        }

        List<CustomerAddressLine> customerAddressLines =new ArrayList<>();

        for(CustomerAddressDto  customerAddressDto: customerCreationDto.getAddressList()){
            CustomerAddressLine customerAddressLine = CustomerAddressLine.builder()
                    .address(customerAddressDto.getAddress())
                    .purpose(customerAddressDto.getPurpose())
                    .street(customerAddressDto.getStreet())
                    .city(customerAddressDto.getCity())
                    .state(customerAddressDto.getState())
                    .zipCode(customerAddressDto.getZipCode())
                    .country(customerAddressDto.getCountry())
                    .tan(customerAddressDto.getTan())
                    .gstin(customerAddressDto.getGstin())
                    .isPrimaryAddress(customerAddressDto.getIsPrimaryAddress())
                    .customerHeader(customerHeader)
                    .build();

            customerAddressLines.add(customerAddressLine);
        }

        List<CustomerBankDetailsLine> customerBankDetailsLines =new ArrayList<>();

        for(CustomerBankDetailsDto  customerBankDetailsDto: customerCreationDto.getCustomerBankDetails()){
            CustomerBankDetailsLine customerBankDetailsLine = CustomerBankDetailsLine.builder()
                    .bankAccountId(customerBankDetailsDto.getBankAccountId())
                    .bankAccountNo(customerBankDetailsDto.getBankAccountNo())
                    .bankName(customerBankDetailsDto.getBankName())
                    .customerHeader(customerHeader)
                    .build();
            customerBankDetailsLines.add(customerBankDetailsLine);
        }

        if(!CollectionUtils.isEmpty(contactLines)){
            customerHeader.setCustomerContactLines(contactLines);
        }

        if(!CollectionUtils.isEmpty(customerAddressLines)){
            customerHeader.setAddressList(customerAddressLines);
        }

        if(!CollectionUtils.isEmpty(customerBankDetailsLines)){
            customerHeader.setCustomerBankDetails(customerBankDetailsLines);
        }


        customerHeader.setCreatedBy("finance-consumer");
        customerHeader.setUpdatedBy("finance-consumer");

        customerHeaderWriteRepository.save(customerHeader);
    }
    @Override
    public void sendD365FailureReport(){
        try {
            LocalDate startDate = LocalDate.now().minusDays(1).atStartOfDay().toLocalDate();
            LocalDate endDate=LocalDate.now().atStartOfDay().toLocalDate();

            StringBuilder sb = new StringBuilder();
            sb = addCustomerD365FailedHeaders(sb);
            int page = 0;
            List<Customer> d365FailedCustomerList;
            Pageable pageable;
            do {
                pageable = PageRequest.of(page, pageLimit);

                //fetch b2bCustomer
                d365FailedCustomerList = customerWriteRepository.findFailedB2BCustomerList(D365ResponseType.FAILED.name(), startDate,endDate, "B2Bdom", pageable);

                if(!CollectionUtils.isEmpty(d365FailedCustomerList)) {
                    sb = addD365FailedCustomerLines(sb, d365FailedCustomerList);
                }

                page++;
            } while (!CollectionUtils.isEmpty(d365FailedCustomerList));

            //send report
            commonUtils.sendReport(sb.toString().getBytes(StandardCharsets.UTF_8),FinanceServiceEventTypes.CUSTOMER,mailSendFrom,mailSendTo,mailCC);

        }catch (Exception exception){
            log.error("[CustomerD365ServiceV1][sendD365FailureReport]Exception Occurred While preparing the customer d365 failure ",exception);
        }
    }

    public StringBuilder addCustomerD365FailedHeaders(StringBuilder sb) {
        for (int i = 0; i < desiredReportColumnNames.length; i++) {
            sb.append(desiredReportColumnNames[i]);
            sb.append(i == desiredReportColumnNames.length - 1 ? "\n" : ",");
        }
        return sb;
    }
    public StringBuilder addD365FailedCustomerLines(StringBuilder sb, List<Customer> d365FailedCustomerList) {
        for (Customer d365FailedCustomer : d365FailedCustomerList) {
            sb.append(FinanceServiceEventTypes.CUSTOMER.name()).append(",");
            sb.append(d365FailedCustomer.getCustomerId()).append(",");
            sb.append(d365FailedCustomer.getOrderId()).append(",");
            sb.append(d365FailedCustomer.getLegalEntity()).append(",");
            sb.append(d365FailedCustomer.getCreatedAt().format(dateTimeFormatter)).append(",");
            sb.append(cleanValueForCsv(d365FailedCustomer.getErrorMessage())).append(",");
            sb.append(d365FailedCustomer.getD365SyncStatus()).append(",");
            sb.append(d365FailedCustomer.getInstanceType()).append(",");
            sb.append("\n");
        }
        return sb;
    }
    private String cleanValueForCsv(String value) {
        if(StringUtils.isBlank(value))
            return null;
        return value.replaceAll("[^a-zA-Z0-9]", " ").trim();
    }

}
