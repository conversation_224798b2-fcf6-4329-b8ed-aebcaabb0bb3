package com.lenskart.financeConsumer.erpEvents.service.impl;

import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.dto.grn.Grn;
import com.lenskart.financeConsumer.erpEvents.dto.po.PurchaseOrder;
import com.lenskart.financeConsumer.erpEvents.factory.InwardEventFactory;
import com.lenskart.financeConsumer.erpEvents.service.InwardVersionService;
import com.lenskart.financeConsumer.erpEvents.util.Version;
import com.lenskart.financeConsumer.util.ObjectHelper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class InwardV1 implements InwardVersionService {
    @Autowired
    private InwardEventFactory inwardEventFactory;
    @Autowired
    private NexsClient nexsClient;
    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;
    @Value("${old.flow.active.flag}")
    private boolean isOldFlowActive;
    @Value("${old.po.consumer.topic}")
    private String oldPoConsumerTopic;
    @Value("${old.grn.consumer.topic}")
    private String oldGrnConsumerTopic;


    @Override
    public Version getVersion() {
        return Version.V1;
    }

    @Override
    public PurchaseOrder getPoPayload(String eventId) {
        log.info("[InwardV1] [getPoPayload] eventId :{}",eventId);
        return nexsClient.getPoPayload(eventId);
    }

    @Override
    public Grn getGrnPayload(String eventId) {
        return nexsClient.getGrnPayload(eventId);
    }

    @Override
    public void publishPoToKafka(PoEventCreationRequest poEventCreationRequest) {
        String message = ObjectHelper.convertToString(poEventCreationRequest);
        if (isOldFlowActive) {
            kafkaProducerTemplate.send(oldPoConsumerTopic, poEventCreationRequest.getEventId(), message);
        }
    }

    @Override
    public void publishGrnToKafka(GrnEventCreationRequest grnEventCreationRequest) {
        String message = ObjectHelper.convertToString(grnEventCreationRequest);
        if (isOldFlowActive) {
            kafkaProducerTemplate.send(oldGrnConsumerTopic, grnEventCreationRequest.getEventId(), message);
        }
    }
}
