package com.lenskart.financeConsumer.erpEvents.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.erpEvents.dto.GrnEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.service.PoService;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GrnConsumerV2 {
    @Autowired
    private PoService poService;
    @Autowired
    private ObjectMapper objectMapper;

    @KafkaListener(topics = "${old.grn.consumer.topic}", groupId = "${old.grn.consumer.topic.group}")
    @Trace(dispatcher = true, metricName = "grnOldFlowConsumer")
    public void listen(String message, Acknowledgment ack) throws InvalidRequestException {
        try {
            GrnEventCreationRequest grnEventCreationRequest = objectMapper.readValue(message,
                                                                                     GrnEventCreationRequest.class);
            poService.processGrn(grnEventCreationRequest);
        } catch (Exception e) {
            log.error("ERROR OCCURRED", e);
        }
        ack.acknowledge();
    }

    @KafkaListener(topics = "${new.grn.consumer.topic}", groupId = "${new.grn.consumer.topic.group}")
    @Trace(dispatcher = true, metricName = "grnNewFlowConsumer")
    public void listenForNewInstance(String message, Acknowledgment ack) throws InvalidRequestException {
        try {
            GrnEventCreationRequest grnEventCreationRequest = objectMapper.readValue(message,
                                                                                     GrnEventCreationRequest.class);
            poService.processGrn(grnEventCreationRequest);
        } catch (Exception e) {
            log.error("ERROR OCCURRED", e);
        }
        ack.acknowledge();
    }
}
