package com.lenskart.financeConsumer.erpEvents.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.erpEvents.dto.PoEventCreationRequest;
import com.lenskart.financeConsumer.erpEvents.service.PoService;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PoConsumer {

    @Autowired
    private PoService poService;
    @Autowired
    private ObjectMapper objectMapper;

    @KafkaListener(topics = "${old.po.consumer.topic}", groupId = "${old.po.consumer.topic.group}")
    @Trace(dispatcher = true, metricName = "poOldFlowConsumer")
    public void listen(String message, Acknowledgment ack) throws InvalidRequestException {
        try {
            PoEventCreationRequest poEventCreationRequest = objectMapper.readValue(message, PoEventCreationRequest.class);
            poService.processPurchaseOrder(poEventCreationRequest);
        }
        catch (Exception e){
            log.error("ERROR OCCURRED",e);
        }
        ack.acknowledge();
    }

    @KafkaListener(topics = "${new.po.consumer.topic}", groupId = "${new.po.consumer.topic.group}")
    @Trace(dispatcher = true, metricName = "poNewFlowConsumer")
    public void listenForNewInstance(String message, Acknowledgment ack) {
        try {
            PoEventCreationRequest poEventCreationRequest = objectMapper.readValue(message, PoEventCreationRequest.class);
            poService.processPurchaseOrder(poEventCreationRequest);
        }
        catch (Exception e){
            log.error("ERROR OCCURRED",e);
        }
        ack.acknowledge();
    }
}
