package com.lenskart.financeConsumer.erpEvents.consumer;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.erpEvents.service.InventoryAdjustmentService;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.newrelic.api.agent.Trace;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class InventoryAdjustConsumer {

    @Autowired
    private InventoryAdjustmentService inventoryAdjustmentService;
    @Autowired
    private ObjectMapper objectMapper;

    @KafkaListener(topics = "${inventory.adjustment.consumer.topic.v1}", groupId = "${inventory.adjustment.consumer.topic.v1.group}")
    @Trace(dispatcher = true, metricName = "inventoryAdjustConsumerV1")
    public void listen(String message, Acknowledgment ack) throws InvalidRequestException {
        try {
            inventoryAdjustmentService.processAdjustInventoryEvent(message);
        } catch (Exception e) {
            log.error("ERROR OCCURRED", e);
        }
        ack.acknowledge();
    }
    @KafkaListener(topics = "${inventory.adjustment.consumer.topic.v2}", groupId = "${inventory.adjustment.consumer.topic.v2.group}")
    @Trace(dispatcher = true, metricName = "inventoryAdjustConsumerV2")
    public void listenV2Instance(String message, Acknowledgment ack) throws InvalidRequestException {
        try {
            inventoryAdjustmentService.processAdjustInventoryEvent(message);
        } catch (Exception e) {
            log.error("ERROR OCCURRED", e);
        }
        ack.acknowledge();
    }
}
