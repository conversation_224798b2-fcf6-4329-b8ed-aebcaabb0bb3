package com.lenskart.financeConsumer.erpEvents.dto.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Generated
@ToString
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerContactDto {
    @JsonProperty("LocationName")
    private String locationName;
    @JsonProperty("Locator")
    private String Locator;
    @JsonProperty("Type")
    private String type;
    @JsonProperty("IsPrimaryContact")
    private String isPrimaryContact = "NO";
}
