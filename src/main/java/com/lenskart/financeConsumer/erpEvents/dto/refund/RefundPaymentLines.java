package com.lenskart.financeConsumer.erpEvents.dto.refund;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Generated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Generated
public class RefundPaymentLines {
    @JsonProperty("PaymentGateway")
    String paymentGateway;

    @JsonProperty("PaymentType")
    String paymentType;

    @JsonProperty("Account")
    private String account;

    @JsonProperty("TransactionDate")
    private String transactionDate;

    @JsonProperty("PrePayment")
    private String prePayment;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("CreditAmount")
    private Double creditAmount;

    @JsonProperty("DebitAmount")
    private Double debitAmount;

    @JsonProperty("CurrencyCode")
    private String currencyCode;

    @JsonProperty("PaymentMethod")
    private String paymentMethod;

    @JsonProperty("PaymentReference")
    private String paymentReference;

    @JsonProperty("SalesOrder")
    private String salesOrder;

    @JsonProperty("PaymentStatus")
    private String paymentStatus;

    @JsonProperty("TIDNum")
    private String TIDNum;

    @JsonProperty("MIDNum")
    private String MIDNum;

    @JsonProperty("Mode")
    private String mode;

    @JsonProperty("Gst")
    private Double gst;

    @JsonProperty("Charges")
    private Double charges;

    @JsonProperty("ChargesSACCode")
    private Double chargesSACCode;

    @JsonProperty("BankAmount")
    private Double bankAmount;

    @JsonProperty("InternationalDomestic")
    private String internationalDomestic;

    @JsonProperty("EntryNo")
    private Integer entryNo;

    @JsonProperty("CardType")
    private String cardType;

    @JsonProperty("ArnNum")
    private String arnNum;

    @JsonProperty("OrderNum")
    private String orderNum;

    @JsonProperty("GstnTransactionId")
    private String gstnTransactionId;

    @JsonProperty("SequenceNumber")
    private String sequenceNumber;

    @JsonProperty("RefNum")
    private String refNum;

    @JsonProperty("WebOrderNumber")
    private String webOrderNumber;

    @JsonProperty("ReturnType")
    private String returnType;

    @JsonProperty("Reason")
    private String reason;

    @JsonProperty("CreatedOn")
    private String createdOn;

    @JsonProperty("CreatedTime")
    private String createdTime;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("ErrorText")
    private String errorText;

    @JsonProperty("BankDate")
    private String bankDate;

    @JsonProperty("BankDateNew")
    private String bankDateNew;

    @JsonProperty("AgentId")
    private String agentId;

    @JsonProperty("DocumentNumber")
    private String documentNumber;

    @JsonProperty("DocumentDate")
    private String documentDate;

    @JsonProperty("Site")
    private String site;

    @JsonProperty("Units")
    private String units;

    @JsonProperty("CostCenter")
    private String costCenter;

    @JsonProperty("Stores")
    private String stores;

    @JsonProperty("SalesChannel")
    private String salesChannel;

    @JsonProperty("SubChannel")
    private String subChannel;

    @JsonProperty("PartnerType")
    private String partnerType;

    @JsonProperty("ItemClassification")
    private String itemClassification;

    @JsonProperty("Brand")
    private String brand;

    @JsonProperty("Employee")
    private String employee;

    @JsonProperty("TransactionId")
    private String transactionId;
}
