package com.lenskart.financeConsumer.erpEvents.dto.movementJournal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@Setter
@Getter
@AllArgsConstructor
@Builder
@ToString
public class InventoryAdjustmentLineItem {

    @NotBlank
    private String event;
    @NotBlank
    private String transactionType;
    @NotNull
    private Integer transactionId;
    @NotBlank
    private String transactionDate;
    @NotNull
    private String barcode;
    @NotNull
    private Integer productId;
    @NotNull
    private float unitPriceWithTax;
    @NotBlank
    private String facility;
    @NotBlank
    private String legalEntity;
}
