package com.lenskart.financeConsumer.erpEvents.dto.customer.kafka;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Setter
@Getter
public class JunoCustomerD365ResponseKafkaPayload {
    private String type;
    private JunoCustomerD365Message message;
    private Integer count;
    private String key;
    private String topic;
    private Integer partition;
    private Integer offset;
}
