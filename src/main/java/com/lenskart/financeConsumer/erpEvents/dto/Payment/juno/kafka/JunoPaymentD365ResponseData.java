package com.lenskart.financeConsumer.erpEvents.dto.Payment.juno.kafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Setter
@Getter
public class JunoPaymentD365ResponseData {
    private Integer orderId;
    private Boolean isExchange;
    private String d365ResponseMessage;
    private String d365SyncStatus;
}
