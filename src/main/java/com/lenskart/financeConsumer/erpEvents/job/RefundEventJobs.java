package com.lenskart.financeConsumer.erpEvents.job;

import com.lenskart.financeConsumer.constant.SchedulerConfigConstants;
import com.lenskart.financeConsumer.erpEvents.service.impl.RefundD365Service;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@Generated
public class RefundEventJobs {

    @Autowired
    SchedulerConfigService schedulerConfigService;
    @Autowired
    RefundD365Service refundD365Service;

    @Scheduled(cron = "${schedule.refund.failure.records.cron}")
    @SchedulerLock(name = "refundFailureEntries")
    public void refundRetryFailureEntriesJob() {
        log.info("[RefundEventJobs][refundRetryFailureEntriesJob] refund retry failure entries starting time");
        try {
            SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(SchedulerConfigConstants.REFUND_FAILURE_ENTRIES);
            if (schedulerConfig.getEnabled()) {
                refundD365Service.syncFailedRefundEvents(schedulerConfig);
            }
        } catch (Exception e) {
            log.error("[RefundEventJobs][refundRetryFailureEntriesJob] refund failure entries error", e);
        }
        log.info("[RefundEventJobs][refundRetryFailureEntriesJob] refund failure entries ending time");
    }

    @Scheduled(cron = "${schedule.refund.failure.report.cron}")
    @SchedulerLock(name = "refundFailureReport")
    public void refundRetryFailureReportJob() {
        log.info("[RefundEventJobs][refundRetryFailureReportJob] FL to D365 failure refund report job starting time");
        try {
            SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(SchedulerConfigConstants.REFUND_FAILURE_REPORT);
            if (schedulerConfig.getEnabled()) {
                refundD365Service.sendD365FailureReport();
            }
        } catch (Exception e) {
            log.error("[RefundEventJobs][refundRetryFailureReportJob] FL to D365 failure refund report job error", e);
        }
        log.info("[RefundEventJobs][refundRetryFailureReportJob] FL to D365 failure refund report job ending time");
    }
}
