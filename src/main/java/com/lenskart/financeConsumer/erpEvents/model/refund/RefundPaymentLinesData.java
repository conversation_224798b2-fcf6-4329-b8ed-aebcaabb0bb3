package com.lenskart.financeConsumer.erpEvents.model.refund;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Generated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Version;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Generated
@Entity(name = "refund_payment_lines")
public class RefundPaymentLinesData {

    @Column(name = "PaymentGateway")
    String paymentGateway;
    @Column(name = "PaymentType")
    String paymentType;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ToString.Exclude
    @ManyToOne
    @JoinColumn(name = "refund_header_id")
    private RefundHeaderData refundHeader;

    @Column(name = "Account")
    private String account;

    @Column(name = "TransactionDate")
    private String transactionDate;

    @Column(name = "PrePayment")
    private String prePayment;

    @Column(name = "Description")
    private String description;

    @Column(name = "CreditAmount")
    private Double creditAmount;

    @Column(name = "DebitAmount")
    private Double debitAmount;

    @Column(name = "CurrencyCode")
    private String currencyCode;

    @Column(name = "PaymentMethod")
    private String paymentMethod;

    @Column(name = "PaymentReference")
    private String paymentReference;

    @Column(name = "SalesOrder")
    private String salesOrder;

    @Column(name = "PaymentStatus")
    private String paymentStatus;

    @Column(name = "TIDNum")
    private String TIDNum;

    @Column(name = "MIDNum")
    private String MIDNum;

    @Column(name = "Mode")
    private String mode;

    @Column(name = "Gst")
    private Double gst;

    @Column(name = "Charges")
    private Double charges;

    @Column(name = "ChargesSACCode")
    private Double chargesSACCode;

    @Column(name = "BankAmount")
    private Double bankAmount;

    @Column(name = "InternationalDomestic")
    private String internationalDomestic;

    @Column(name = "EntryNo")
    private Integer entryNo;

    @Column(name = "CardType")
    private String cardType;

    @Column(name = "ArnNum")
    private String arnNum;

    @Column(name = "OrderNum")
    private String orderNum;

    @Column(name = "GstnTransactionId")
    private String gstnTransactionId;

    @Column(name = "SequenceNumber")
    private String sequenceNumber;

    @Column(name = "RefNum")
    private String refNum;

    @Column(name = "WebOrderNumber")
    private String webOrderNumber;

    @Column(name = "ReturnType")
    private String returnType;

    @Column(name = "Reason")
    private String reason;

    @Column(name = "CreatedOn")
    private String createdOn;

    @Column(name = "CreatedTime")
    private String createdTime;

    @Column(name = "Status")
    private String status;

    @Column(name = "ErrorText")
    private String errorText;

    @Column(name = "BankDate")
    private String bankDate;

    @Column(name = "BankDateNew")
    private String bankDateNew;

    @Column(name = "AgentId")
    private String agentId;

    @Column(name = "DocumentNumber")
    private String documentNumber;

    @Column(name = "DocumentDate")
    private String documentDate;

    @Column(name = "Site")
    private String site;

    @Column(name = "Units")
    private String units;

    @Column(name = "CostCenter")
    private String costCenter;

    @Column(name = "Stores")
    private String stores;

    @Column(name = "SalesChannel")
    private String salesChannel;

    @Column(name = "SubChannel")
    private String subChannel;

    @Column(name = "PartnerType")
    private String partnerType;

    @Column(name = "ItemClassification")
    private String itemClassification;

    @Column(name = "Brand")
    private String brand;

    @Column(name = "Employee")
    private String employee;

    @Column(name = "TransactionId")
    private String transactionId;

    @Version
    @Column(name = "version")
    private long version;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
