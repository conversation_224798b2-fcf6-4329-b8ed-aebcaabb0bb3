package com.lenskart.financeConsumer.erpEvents.model;

import com.lenskart.financeConsumer.model.financeDb.TransferOrderLinesEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Setter
@Getter
@Entity
@Table(name = "payment_header")
public class PaymentHeader {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private String id;

    @Column(name = "document_number")
    private String documentNumber;

    @Column(name = "journal_name")
    private String journalName;

    @Column(name = "legal_entity")
    private String legalEntity;

    @Column(name ="is_exchange")
    private Boolean isExchange;

    @OneToMany(mappedBy = "paymentHeader", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PaymentLine> paymentLines;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "created_by")
    private String createdBy;

    // important bi-directional mapping for persisting child entity in db using parent entity only
    public void addPaymentLine(PaymentLine paymentLine){
        paymentLines.add(paymentLine);
        paymentLine.setPaymentHeader(this);
    }



}
