package com.lenskart.financeConsumer.erpEvents.dao.write;

import com.lenskart.financeConsumer.erpEvents.model.po.PurchaseOrderHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PurchaseOrderHeaderWriteRepository extends JpaRepository<PurchaseOrderHeader, Long> {
    PurchaseOrderHeader findByPurchaseOrderNumber(String purchaseOrderNumber);
}
