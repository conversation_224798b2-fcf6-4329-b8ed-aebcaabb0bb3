package com.lenskart.financeConsumer.erpEvents.dao.write;

import com.lenskart.financeConsumer.erpEvents.model.CustomerHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomerHeaderWriteRepository extends JpaRepository<CustomerHeader,Long> {
    Optional<CustomerHeader> findByCustAccountAndOrderIdAndLegalEntity(String custAccount,Long orderId,String legalEntity);
}
