package com.lenskart.financeConsumer.util;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.core.model.D365ReturnTracking;
import com.lenskart.core.model.D365TransferJournalTracking;
import com.lenskart.core.model.HubMaster;
import com.lenskart.core.model.ItemResolutionFlat;
import com.lenskart.core.model.ItemWisePriceDetails;
import com.lenskart.core.model.MpOrderDetails;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.SystemPreference;
import com.lenskart.core.model.UwOrder;
import com.lenskart.core.model.UniReportStockEntry;
import com.lenskart.financeConsumer.clients.OrderOpsClient;
import com.lenskart.financeConsumer.dao.*;
import com.lenskart.financeConsumer.dto.D365ReturnTrackingResponse;
import com.lenskart.financeConsumer.dto.ManualSaleOrderReturnDto;
import com.lenskart.financeConsumer.dto.PosFranchiseSbrtResponse;
import com.lenskart.financeConsumer.dto.d365requests.Return.PosSessionRequest;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnCreateRequest;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SoLine;
import com.lenskart.financeConsumer.dto.d365requests.PosApiResponse;
import com.lenskart.financeConsumer.dto.d365requests.TransferJournalDTO.D365TransferJournalDTO;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.exceptions.TransferJournalException;
import com.lenskart.financeConsumer.model.InvoiceDetails;
import com.lenskart.financeConsumer.model.OrderItemGSTDetail;
import com.lenskart.financeConsumer.model.ReturnHistory;
import com.lenskart.financeConsumer.model.ReturnOrderAddressUpdate;
import com.lenskart.financeConsumer.model.enums.NavChannel;
import com.lenskart.financeConsumer.model.enums.SbrtOrderMapper;
import com.lenskart.financeConsumer.pos.readOnlyRepository.FranchiseMasterReadRepository;
import com.lenskart.financeConsumer.service.*;
import com.lenskart.financeConsumer.service.impl.ReturnOrderServiceImpl;
import com.lenskart.ordermetadata.dto.ReturnDetailAddressUpdateDTO;
import com.lenskart.ordermetadata.dto.ReturnHistoryDTO;
import com.lenskart.ordermetadata.dto.headlessReturn.ReturnDetailsDTO;
import com.lenskart.ordermetadata.dto.headlessReturn.ReturnOrderDTO;
import com.lenskart.ordermetadata.dto.headlessReturn.ReturnOrderItemDTO;
import com.lenskart.ordermetadata.dto.headlessReturn.request.GetReturnDetailsRequest;
import com.lenskart.ordermetadata.dto.response.ReturnReasonTableDTO;
import com.lenskart.orderops.dao.SbrtOrderItemRepository;
import com.lenskart.orderops.model.ReturnOrder;
import com.lenskart.orderops.model.ReturnOrderItem;
import com.lenskart.orderops.model.SbrtOrderItem;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.lenskart.financeConsumer.model.ReturnHistory.ENTITY_TYPE.RETURN_ORDER;
import static com.lenskart.financeConsumer.util.Constants.ReturnOrder.Awb_Assigned;

@Slf4j
@Component
public class ReturnUtil {

    public static final String INTRANSIT = "_Intransit";
    @Value("${pos.base.url}")
    private String posBaseUrl;

    @Value("${pos.app.key}")
    private String lenskartApiKey;

    @Value("${pos.app.id}")
    private String lenskartAppId;

    @Value("${pos.session.base.url}")
    private String posSessionBaseUrl;

    @Value("${pos.session.user}")
    private String posSessionUser;

    @Value("${pos.session.password}")
    private String posSessionPass;

    @Value("${nexs.base.url:https://grn.nexs.preprod.lenskart.com/nexs/api/grn/v1}")
    private String nexsBaseUrl;

    @Value("${default.cost.price.flag:false}")
    private boolean defaultCostPriceFlag;

    @Value("${default.cost.price:100.00}")
    private String defaultCostPrice;

    @Autowired
    UwOrdersRepository uwOrdersRepository;

    @Autowired
    OrdersHeaderRepository ordersHeaderRepository;

    @Autowired
    D365TransferJournalTrackingRepository d365TransferJournalTrackingRepository;
    @Autowired
    InvoiceDetailsService invoiceDetailsService;

    @Autowired
    private SaleOrderUtil saleOrderUtil;

    @Autowired
    private ItemWisePriceInfoDetails itemWisePriceInfoService;

    @Autowired
    private OrderItemGstDetailsService orderItemGstDetailsService;

    @Autowired
    UniReportStockEntryRepository uniReportStockEntryRepository;

    @Autowired
    private S3InvoiceDetailsService s3InvoiceDetailsService;

    @Autowired
    OrderRepository orderRepository;

    @Autowired
    MpOrderDetailsRepository mpOrderDetailsRepository;

    @Autowired
    D365ReturnTrackingRepository d365ReturnTrackingRepository;

    @Autowired
    SystemPreferenceService systemPreferenceService;


    @Autowired
    ReturnHistoryRepository returnHistoryRepository;

    @Autowired
    ReturnOrderItemRepository returnOrderItemRepository;

    @Autowired
    ReturnOrderRepository returnOrderRepository;

    @Autowired
    PosUtils posUtils;

    @Autowired
    SystemPreferenceRepository systemPreferenceRepository;

    @Autowired
    private HubMasterRepository hubMasterRepository;

    @Autowired
    SbrtOrderItemRepository sbrtOrderItemRepository;

    @Autowired
    ReturnOrderServiceImpl returnOrderService;

    @Autowired
    private HubMasterService hubMasterService;

    DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);

    String posURL = "/gatepass/getGatepassItemsDetails/";

    @Autowired
    ItemMasterService itemMasterService;


    SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    DecimalFormat decimalFormat = new DecimalFormat("#0.00");

    @Autowired
    ShippingStatusRepository shippingStatusRepository;

    private List<String> intCountries;

    @Value("#{'${finance.classification.blank.hsncode}'.split(',')}")
    private Set<String> blankHsnCodeClassifications;

    private final static String FOFO_IDENTIFIER = "fofo";

    private  List<String> MANESAR_FACILITY_CODE;

    private List<String> nexsFacilities;

    private List<String> physicalFacilities;

    SimpleDateFormat receivedDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

    public static final Pattern serialNumberErrorPattern = Pattern.compile(".*already exists physically in the inventory.*",
            Pattern.CASE_INSENSITIVE);

    public static final Pattern hasNotBeenCreatedPattern = Pattern.compile(".*has not been created for item.*",
            Pattern.CASE_INSENSITIVE);


    @Value("#{'${confirmed.receipt.date.valid.status:awb_assigned}'.split(',')}")
    private List<String> VALID_STATUS_FOR_SETTING_CONFIRMED_RECEIPT_DATE;

    @Autowired
    private UwOrdersService uwOrdersService;

    @Autowired
    private ItemResolutionFlatRepository itemResolutionFlatRepository;

    @Value("${default.nexs.facility:DK02}")
    private String defaultNexsFacility;

    @Autowired
    private ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));

    @Autowired
    private FranchiseMasterReadRepository franchiseMasterReadRepository;
    @Autowired
    private Gson gson;

    @Autowired
    @Qualifier("restTemplateWithTimeout")
    private RestTemplate restTemplate;

    @Value("${return.base.url:http://return-api.scm.preprod-eks.internal/}")
    private String returnBaseUrl;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;

    @PostConstruct
    void init() {
        intCountries = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN, Constants.SYSTEM_PREFERENCE_KEYS.D365_Country_List);
        nexsFacilities = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.NEXS_FACILITIES,Constants.SYSTEM_PREFERENCE_KEYS.NEXS_FACILITIES);
        MANESAR_FACILITY_CODE = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.MANESAR_FACILITIES,Constants.SYSTEM_PREFERENCE_KEYS.MANESAR_FACILITIES);
        physicalFacilities = hubMasterRepository.findAllFacilityCode();
    }


    public String getCourierCodeFromPos(UwOrder uwOrder) {
        log.info("ReturnUtil : Fetching courier code from POS api");
        try {
            String url = posBaseUrl + Constants.POS.URL + uwOrder.getUwItemId();
            log.info("ReturnUtil : POS url : {}",url);
            Map responseBody = null;
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            headers.add("User-Agent", "Application");
            headers.add("X-Lenskart-API-Key", lenskartApiKey);
            headers.add("X-Lenskart-App-Id", lenskartAppId);
            headers.add("X-Lenskart-Session-Token", getPosSessionToken());
            HttpEntity<?> entity = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response != null) {
                responseBody = response.getBody();
                Map content = (Map) responseBody.get("content");
                log.info("content : "+content);
                if (responseBody != null) {

                }
                log.info("[ReturnUtil][getCourierCodeFromPos] Pos response : " + responseBody);
            }
        } catch (Exception e) {
            log.error("[ReturnUtil][getCourierCodeFromPos] exception found to get data from pos : ",e);
        }
        return "";
    }

    public PosFranchiseSbrtResponse getStoreSbrtStatus(String facilityCode) {
        log.info("ReturnUtil : Fetching courier code from POS api");
        PosFranchiseSbrtResponse posFranchiseSbrtStatus = null;
        try {
            String url = UriComponentsBuilder.fromHttpUrl(posSessionBaseUrl + Constants.POS.SBRT_STORE_URL)
                    .queryParam("facilityCode", facilityCode)
                    .toUriString();
            log.info("ReturnUtil : POS url : {}",url);
            Map responseBody = null;
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            headers.add("User-Agent", "Application");
            headers.add("X-Lenskart-API-Key", lenskartApiKey);
            headers.add("X-Lenskart-App-Id", lenskartAppId);
            headers.add("X-Lenskart-Session-Token", getPosSessionToken());
            HttpEntity<?> entity = new HttpEntity<>(headers);
            ResponseEntity<PosFranchiseSbrtResponse> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, PosFranchiseSbrtResponse.class);

            if (response != null && response.getBody() != null) {
                posFranchiseSbrtStatus = response.getBody();
                log.info("[ReturnUtil][getStoreSbrtStatus] POS response : {}", posFranchiseSbrtStatus);
            }
        } catch (Exception e) {
            log.error("[ReturnUtil][getCourierCodeFromPos] exception found to get data from pos : ",e);
        }

        return posFranchiseSbrtStatus;
    }

    private String getPosSessionToken() {
        String sessionToken = "";
        try {
            String url = posSessionBaseUrl + Constants.POS.SESSION_URL;
            log.info("ReturnUtil : POS_Session url : {}",url);
            RestTemplate restTemplate = new RestTemplate();
            Map responseBody = null;
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            headers.add("User-Agent", "Application");
            headers.add("X-Lenskart-API-Key", lenskartApiKey);
            headers.add("X-Lenskart-App-Id", lenskartAppId);
            PosSessionRequest request = new PosSessionRequest();
            request.setEncodingRequired(true);
            request.setUserName(posSessionUser);
            request.setPassword(posSessionPass);
            HttpEntity<PosSessionRequest> entity = new HttpEntity<PosSessionRequest>(request, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class);
            if (response != null) {
                responseBody = response.getBody();
                log.info("session response : "+response);
                if (responseBody != null) {
                    sessionToken = (String) responseBody.get("sessionToken");
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to get sessionToken from pos : ",e);
        }
        return sessionToken;
    }

    public void setMethodOfPayment(OrdersHeader ordersHeader, String subChannel, SalesOrderHeader salesOrderHeader, UwOrder uwOrder, Order order) {
        log.info("ReturnUtil : Setting MethodOfPayment for uwItemId : {}",uwOrder.getUwItemId());
        try {
            MpOrderDetails mpOrderDetails = mpOrderDetailsRepository.findOneByIncrementIdNew(order.getIncrementId());
            if (mpOrderDetails != null) {
                salesOrderHeader.setMethodOfPayment(order.getSaleSource());
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
                //LKSG
                if (uwOrder.getNavChannel().contains("COCO") && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                    salesOrderHeader.setMethodOfPayment("offlinecash");
                } else {
                    salesOrderHeader.setMethodOfPayment("LKSG");
                }
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("AE")) {
                //LKAE
                if (uwOrder.getNavChannel().contains("COCO") && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                    salesOrderHeader.setMethodOfPayment("offlinecash");
                } else {
                    salesOrderHeader.setMethodOfPayment("LKAE");
                }
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("US")) {
                salesOrderHeader.setMethodOfPayment(ordersHeader.getPaymentMode());
            } else if ((uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B) && !uwOrder.getNavChannel().equalsIgnoreCase(Constants.Common.FOFOB2B)) || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                //DKIN
                if ((uwOrder.getNavChannel().equalsIgnoreCase("COCOOTC") || uwOrder.getNavChannel().equalsIgnoreCase("JJOTC") || uwOrder.getNavChannel().equalsIgnoreCase("COCOB2B") || uwOrder.getNavChannel().equalsIgnoreCase("JJB2B")) && (!salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                    salesOrderHeader.setMethodOfPayment(salesOrderHeader.getModeOfPayment());
                } else if (uwOrder.getNavChannel().equalsIgnoreCase("OJOSDTC") || uwOrder.getNavChannel().equalsIgnoreCase("JJDTC") || uwOrder.getNavChannel().equalsIgnoreCase("WEBDTC") || uwOrder.getNavChannel().equalsIgnoreCase("JJOnlineDTC")) {
                    salesOrderHeader.setMethodOfPayment("DKIN");
                }
            } else {
                //LKIN
                if ((uwOrder.getNavChannel().equalsIgnoreCase("COCODTC") || uwOrder.getNavChannel().equalsIgnoreCase("FOFODTC")) && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                    salesOrderHeader.setMethodOfPayment("offlinecash");
                } else if (uwOrder.getNavChannel().equalsIgnoreCase("OJOSDTC") || uwOrder.getNavChannel().equalsIgnoreCase("WEBDTC") || uwOrder.getNavChannel().equalsIgnoreCase("JJOnlineDTC")) {
                    salesOrderHeader.setMethodOfPayment("DKIN");
                } else if (uwOrder.getNavChannel().equalsIgnoreCase("JJDTC") && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                    salesOrderHeader.setMethodOfPayment("offlinecash");
                } else if (uwOrder.getNavChannel().equalsIgnoreCase("JJDTC") && (!salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                    salesOrderHeader.setMethodOfPayment("DKIN");
                }
            }
            if (StringUtils.isBlank(salesOrderHeader.getMethodOfPayment())) {
                Order PaymentOrder = orderRepository.getByItemId(uwOrder.getItemId());
                if (PaymentOrder.getMethod().equalsIgnoreCase("cashondelivery")) {
                    salesOrderHeader.setMethodOfPayment(salesOrderHeader.getCourierCode());
                } else {
                    salesOrderHeader.setMethodOfPayment("Prepaid");
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set MethodOfPayment for uwItemid : {}",uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : MethodOfPayment for uwItemId : {} {}",uwOrder.getUwItemId(), salesOrderHeader.getMethodOfPayment());
    }

    public void setModeOfPayment(Order order, SalesOrderHeader salesOrderHeader, UwOrder uwOrder) {
        log.info("ReturnUtil : Setting ModeOfPayment for uwItemId : {}",uwOrder.getUwItemId());
        try {
            if (order != null) {
                if (order.getMethod().equalsIgnoreCase("cashondelivery")) {
                    salesOrderHeader.setModeOfPayment(Constants.SalesOrder.COD);
                    salesOrderHeader.setTermsOfPayment(Constants.SalesOrder.COD);
                } else {
                    salesOrderHeader.setModeOfPayment(StringUtils.defaultString(order.getMethod()).trim());
                    salesOrderHeader.setTermsOfPayment("");
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set ModeOfPayment for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setCourierCode(UwOrder uwOrder, OrdersHeader ordersHeader, ReturnOrder returnOrder, SalesOrderHeader salesOrderHeader) {
        log.info("ReturnUtil : Setting CourierCode for uwItemId : {}",uwOrder.getUwItemId());
        String courierCode = "";
        PosApiResponse posApiResponse = null;
        Integer uwItemId = uwOrder.getUwItemId();
        UwOrder uwOrderB2B = null;
        String errorMessage = "";
        try {
            if (uwOrder.getIsLocalFittingRequired()) {
                courierCode = "";
            } else {
                if (returnOrder != null) {
                    if (returnOrder.getReturnMethod() != null && returnOrder.getReturnMethod().equalsIgnoreCase("RPU")) {
                        courierCode = StringUtils.defaultString(returnOrder.getReverseCourier());
                    } else if (returnOrder.getReturnMethod() != null &&
                            (returnOrder.getReturnMethod().equalsIgnoreCase("StoreReceiving"))) {

                        if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                            log.info("ReturnUtil : Fetching details from pos api for B2B uwItemId : {}", uwItemId);
                            uwOrderB2B = uwOrdersRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());
                            if (uwOrderB2B != null) {
                                log.info("ReturnUtil : Fetching details from pos api for uwItemId : {}", uwItemId);
                                posApiResponse = posUtils.getDetailsFromPos(uwOrderB2B.getUwItemId());
                            }

                        } else {
                            log.info("ReturnUtil : Fetching details from pos api for uwItemId : {}", uwItemId);
                            posApiResponse = posUtils.getDetailsFromPos(uwOrder.getUwItemId());
                        }
                        log.info("ReturnUtil : posApiResponse for uwItemId : {} : {}", uwItemId, posApiResponse);
                        if (posApiResponse != null && StringUtils.isEmpty(posApiResponse.getErrorMessage())) {
                            if (!StringUtils.isEmpty(posApiResponse.getCourier())) {
                                courierCode = posApiResponse.getCourier();
                            } else if (!StringUtils.isEmpty(posApiResponse.getPosGatepass())) {
                                courierCode = "Gatepass";
                            }
                        }
                    }
                }
            }
            log.info("ReturnUtil : courierCode for uwItemId : {} : {}", uwItemId, courierCode);
            salesOrderHeader.setCourierCode(courierCode);
        } catch (Exception e) {
            if (posApiResponse != null && !StringUtils.isEmpty(posApiResponse.getErrorMessage())) {
                errorMessage = posApiResponse.getErrorMessage();
            }
            log.error("ReturnUtil : Exception found to get CourierCode for uwItemId : {} : ",uwItemId, e);
        }
    }

    public void setRefInvoices(UwOrder uwOrder, SalesOrderHeader salesOrderHeader) {
        log.info("ReturnUtil : Setting RefInvoice for uwItemId : {}",uwOrder.getUwItemId());
        try {
            InvoiceDetails invoiceDetails = null;
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                UwOrder uwOrder1 = uwOrdersRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());
                invoiceDetails = invoiceDetailsService.getInvoiceDetails(uwOrder1.getShippingPackageId());
                log.info("Invoice details found for uwOrder : {}",uwOrder1.getUwItemId());
                if (invoiceDetails != null) {
                    salesOrderHeader.setRefInvoiceNo(invoiceDetails.getInvoiceCode());
                    salesOrderHeader.setOrgInvoiceNo(invoiceDetails.getInvoiceCode());
                }

            } else {
                invoiceDetails = invoiceDetailsService.getInvoiceDetails(uwOrder.getShippingPackageId());
                salesOrderHeader.setRefInvoiceNo(invoiceDetails.getInvoiceCode());
                salesOrderHeader.setOrgInvoiceNo(invoiceDetails.getInvoiceCode());
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set RefInvoice for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setCustomerDetails(SalesOrderHeader salesOrderHeader, OrdersHeader ordersHeader, Order order, UwOrder uwOrder) {
        log.info("ReturnUtil : Setting CustomerDetails for uwItemId : {}",uwOrder.getUwItemId());
        try {
            String customerId = String.valueOf(order.getCustomerId());
            log.info("setCustomerDetails : isBulkOrder : " + ordersHeader.getIsBulkOrder());
            if (ordersHeader.getIsBulkOrder() && !salesOrderHeader.getSubChannel().equalsIgnoreCase(Constants.ReturnOrder.COCOBulk)) {
                String saleSource = order.getSaleSource();
                log.info("setCustomerDetails : saleSource : " + order.getSaleSource());
                if (saleSource.contains(Constants.Channel.AQUALENS)) {
                    salesOrderHeader.setCustomerAccount(customerId);
                    salesOrderHeader.setInvoiceAccount(customerId);
                } else {
                    String storeCode = saleOrderUtil.getStoreCode(ordersHeader);
                    log.info("setCustomerDetails : storeCode : " + order.getSaleSource());
                    salesOrderHeader.setCustomerAccount(storeCode);
                    salesOrderHeader.setInvoiceAccount(storeCode);
                }
            } else if (salesOrderHeader.getSubChannel().equalsIgnoreCase(Constants.ReturnOrder.COCOBulk)
                    && ordersHeader.getLkCountry().equalsIgnoreCase(Constants.Country.IN)
                    || ordersHeader.getLkCountry().equalsIgnoreCase(Constants.Country.SG)
                    || ordersHeader.getLkCountry().equalsIgnoreCase(Constants.Country.AE)
                    || ordersHeader.getLkCountry().equalsIgnoreCase(Constants.Country.US)) {
                if (ordersHeader.getLkCountry().equalsIgnoreCase(Constants.Country.IN)) {
                    salesOrderHeader.setCustomerAccount(Constants.ReturnOrder.DK + ordersHeader.getLkCountry());
                    salesOrderHeader.setInvoiceAccount(Constants.ReturnOrder.DK + ordersHeader.getLkCountry());
                } else {
                    salesOrderHeader.setCustomerAccount(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                    salesOrderHeader.setInvoiceAccount(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                }
            } else {
                salesOrderHeader.setCustomerAccount(customerId);
                salesOrderHeader.setInvoiceAccount(customerId);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set CustomerDetails for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setSoLinePrices(UwOrder uwOrder, SoLine soLine) throws Exception {
        log.info("ReturnUtil : setSoLinePrices for uwItemId : {}",uwOrder.getUwItemId());
        Double salesPrice = getSalePrice(uwOrder.getUwItemId());
        soLine.setSalesPrice(decimalFormat.format(salesPrice));

        if (Constants.Common.OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            log.info("ReturnUtil : setSoLinePrices fetching return cost price for OTC order uwItemId : {}",uwOrder.getUwItemId());
            UwOrder bulkUwOrder = getBulkUwOrderByBarcode(uwOrder.getBarcode());
            if (bulkUwOrder != null) {
                Double bulkSalesPrice = getSalePrice(bulkUwOrder.getUwItemId());
                soLine.setReturnCostPrice(bulkSalesPrice);
            }
            else {
                soLine.setReturnCostPrice(Double.valueOf(getUnitPrice(uwOrder)));
            }
        }
        else if(Constants.Common.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()))  {
            log.info("ReturnUtil : setSoLinePrices fetching return cost price for B2B order uwItemId : {}",uwOrder.getUwItemId());
            UwOrder b2bUwOrder = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
            soLine.setReturnCostPrice(Double.valueOf(getUnitPrice(b2bUwOrder)));
        }
        else {
            //fetch return cost price from nexs api for rest of the order types
            soLine.setReturnCostPrice(Double.valueOf(getUnitPrice(uwOrder)));
        }
    }

    private Double getSalePrice(Integer uwItemId) throws Exception {
        log.info("ReturnUtil : fetching salesPrice for uwItemId : {} ", uwItemId);
        Double salesPrice = 0.0;
        OrderItemGSTDetail orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwItemId);
        if (orderItemGSTDetail != null) {
            salesPrice = orderItemGSTDetail.getCostPerItem();
        }
        log.info("ReturnUtil : salesPrice for uwItemId : {} : {}", uwItemId, salesPrice);
        return salesPrice;
    }

    public void setSoLineBasicDetails(UwOrder uwOrder, Order order, SoLine soLine) {
        log.info("ReturnUtil : setSoLineBasicDetails for uwItemId : {}",uwOrder.getUwItemId());
        try {
            soLine.setLineNumber(Long.valueOf(uwOrder.getUwItemId()));
            soLine.setItemNumber(StringUtils.defaultString(String.valueOf(uwOrder.getProductId())));
            soLine.setQtyOrdered(1);
            soLine.setOriginalSaleOrderNo(order.getIncrementId());
            soLine.setOriginalSaleOrderLineNo(uwOrder.getUwItemId());
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineBasicDetails for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setSoLineInventLocationId(ReturnOrder returnOrder, SoLine soLine, OrdersHeader ordersHeader, UwOrder uwOrder) {
        log.info("ReturnUtil : setSoLineInventLocationId for uwItemId : {}",uwOrder.getUwItemId());
        try {
            if (returnOrder != null) {
                if (returnOrder.getStatus().equalsIgnoreCase(Constants.ReturnOrder.Awb_Assigned)) {
                    soLine.setInventLocationId("Courier");
                } else if (!StringUtils.isEmpty(returnOrder.getSource()) && returnOrder.getSource().equalsIgnoreCase("pos")) {
                    soLine.setInventLocationId(ordersHeader.getFacilityCode());
                } else if (!StringUtils.isEmpty(returnOrder.getSource()) && returnOrder.getSource().equalsIgnoreCase("WAREHOUSE")) {
                    soLine.setInventLocationId("Direct");
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : setSoLineInventLocationId for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setSoLineDiscount(UwOrder uwOrder, SoLine soLine) {
        log.info("ReturnUtil : setSoLineDiscount for uwItemId : {}",uwOrder.getUwItemId());
        try {
            ItemWisePriceDetails itemWisePriceDetails = null;
            itemWisePriceDetails = itemWisePriceInfoService.findItemWisePriceInfoByItemId(uwOrder.getItemId());
            log.info("ReturnUtil : itemWisePriceDetails : {}",itemWisePriceDetails);
            if (itemWisePriceDetails != null) {
                soLine.setLineDiscountAmount(String.valueOf(itemWisePriceDetails.getLenskartDiscount()));
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineDiscount for uwItemId : {}",uwOrder.getUwItemId());
        }
    }

    public void setSoLineHsnAndSacCode(Product product, SoLine soLine, int classificationId, UwOrder uwOrder) {
        log.info("ReturnUtil : setSoLineHsnAndSacCode for uwItemId : {}",uwOrder.getUwItemId());
        String hsncode = "";
        OrderItemGSTDetail orderItemGSTDetail = null;
        try {
            orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwOrder.getUwItemId());
            if (orderItemGSTDetail != null) {
                hsncode = orderItemGSTDetail.getHsn();
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineHsnAndSacCode for uwItemId : {}",uwOrder.getUwItemId());
        }
        if(StringUtils.isBlank(hsncode) && blankHsnCodeClassifications.contains(String.valueOf(classificationId))){
            hsncode = ProductUtil.getHSNCode(product.getHsnCode());
        }
        log.info("ReturnUtil : hsnCode for uwItemId : {} : {}",uwOrder.getUwItemId(),hsncode);
        soLine.setHsnCode(hsncode);
        soLine.setSacCode("");
    }

    public void setSoLineTax(UwOrder uwOrder, SoLine soLine) {
        log.info("ReturnUtil : setSoLineTax for uwItemId : {}",uwOrder.getUwItemId());
        OrderItemGSTDetail orderItemGSTDetail = null;
        Double taxRate = 0.00D;
        UwOrder uwOrder2 = null;

        try {
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                uwOrder2 = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
            } else {
                uwOrder2 = uwOrder;
            }
            if (uwOrder2 != null) {
                orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwOrder2.getUwItemId());
            }
            if (orderItemGSTDetail != null) {
                taxRate = orderItemGSTDetail.getCgstPc()
                        + orderItemGSTDetail.getIgstPc()
                        + orderItemGSTDetail.getSgstPc()
                        + orderItemGSTDetail.getUgstPc();
                log.info("ReturnUtil : TaxRate for uwItemId : {} : {}",uwOrder.getUwItemId(), taxRate);
                soLine.setSalesOrderItemCode(String.valueOf(uwOrder.getUwItemId()));
                soLine.setTaxRateType(String.format("%.2f", taxRate));
                soLine.setItemSalesTaxGrp(String.format("%.2f", taxRate));
                soLine.setSalesTaxGrp(String.format("%.2f", taxRate));
            } else {
                log.error("ReturnUtil : setSoLineTax orderItemGSTDetailList found empty : {}",uwOrder.getUwItemId());
            }

        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get taxRate from order-ops-client for uwItemId : {}", uwOrder.getUwItemId());
        }
    }

    public String getReturnStoreCode(OrdersHeader ordersHeader, UwOrder uwOrder) {
        log.info("ReturnUtil : getReturnStoreCode for uwItemId : {}", uwOrder.getUwItemId());
        String storeCode = "";
        try {
            if (ordersHeader != null) {
                storeCode = getStoreCode(ordersHeader);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : getReturnStoreCode for uwItemId : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : storeCode for uwItemId : {} {}",uwOrder.getUwItemId(), storeCode);
        return storeCode;
    }

    public void setSoLinePurchPrice(UwOrder uwOrder, SoLine soLine) {
        log.info("ReturnUtil : setSoLinePurchPrice for uwItemId : {}", uwOrder.getUwItemId());
        Double purchPrice = 0.0;
        OrderItemGSTDetail orderItemGSTDetail = null;
        UwOrder uwOrder2 = null;
        try {
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                uwOrder2 = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
                if (uwOrder2 != null) {
                    orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwOrder2.getUwItemId());
                    if (orderItemGSTDetail != null) {
                        purchPrice = orderItemGSTDetail.getCostPerItem();
                    } else {
                        log.error("ReturnUtil : purchPrice not found for uwItemId : {}",uwOrder.getUwItemId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get purchPrice for uwItemId : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : purchPrice for uwItemId : {} : {}", uwOrder.getUwItemId(),purchPrice);
        soLine.setPurchPrice(String.valueOf(purchPrice));
    }

    public void setSoLineConfirmedReceiptDate(ReturnOrder returnOrder, UwOrder uwOrder, SoLine soLine) {
        log.info("ReturnUtil : fetching ConfirmedReceiptDate for uwItemId : {}", uwOrder.getUwItemId());
        try {
            ReturnHistory returnHistory = null;
            if (returnOrder != null) {
                returnHistory = returnHistoryRepository.findTopByEntityIdAndEntityTypeAndCurrentStatusInOrderByCreatedAtAsc(returnOrder.getReturnId(),RETURN_ORDER,VALID_STATUS_FOR_SETTING_CONFIRMED_RECEIPT_DATE);
                if (returnHistory != null) {
                    log.info("Setting ConfirmedReceiptDate and ConfirmedShipDate from returnHistory.");
                    soLine.setConfirmedReceiptDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreateAt())));
                    soLine.setConfirmedShipDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreateAt())));
                } else {
                    List<ReturnHistoryDTO> returnHistoryDTOList = getReturnHistory(returnOrder.getReturnId());
                    returnHistory = getLatestReturnHistory(returnHistoryDTOList, returnOrder.getReturnId(),VALID_STATUS_FOR_SETTING_CONFIRMED_RECEIPT_DATE);
                    if (returnHistory != null) {
                        log.info("Setting ConfirmedReceiptDate and ConfirmedShipDate from returnHistory.");
                        soLine.setConfirmedReceiptDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreateAt())));
                        soLine.setConfirmedShipDate(StringUtils.defaultString(dateFormat.format(returnHistory.getCreateAt())));
                    }else{
                        log.info("Setting ConfirmedReceiptDate and ConfirmedShipDate from returnOrder's returnCreateDateTime.");
                        soLine.setConfirmedReceiptDate(StringUtils.defaultString(dateFormat.format(returnOrder.getReturnCreateDatetime())));
                        soLine.setConfirmedShipDate(StringUtils.defaultString(dateFormat.format(returnOrder.getReturnCreateDatetime())));
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to fetch ConfirmedReceiptDate for uwItemId : {}", uwOrder.getUwItemId());
        }
    }

    private ReturnHistory getLatestReturnHistory(List<ReturnHistoryDTO> returnHistoryDTOList, Integer returnId, List<String> validStatuses) {
        ReturnHistory returnHistory = null;
        if(!CollectionUtils.isEmpty(returnHistoryDTOList)){
            returnHistoryDTOList = returnHistoryDTOList.stream().filter(returnHistoryDTO -> returnHistoryDTO.getCurrentStatus() != null && validStatuses.contains(returnHistoryDTO.getCurrentStatus().toLowerCase())).collect(Collectors.toList());
            log.info("[getLatestReturnHistory] returnHistoryDTOList : {}", returnHistoryDTOList);
            if(!CollectionUtils.isEmpty(returnHistoryDTOList)){
                ReturnHistoryDTO returnHistoryDTO = returnHistoryDTOList.get(returnHistoryDTOList.size()-1);
                returnHistory = getReturnHistory(returnHistoryDTO, returnId);
            }
        }
        return returnHistory;
    }

    public String getInventLocationIdForReturn(UwOrder uwOrder, OrdersHeader ordersHeader, ReturnOrder returnOrder, ReturnCreateRequest returnRequestDto) {
        String inventLocationId = "";
        Integer uwItemId = uwOrder.getUwItemId();
        PosApiResponse posApiResponse = null;
        String courier = "Courier";
        String direct = "Direct";

        if(isNexsUwOrder(uwOrder)){
            courier = "BH-Courier";
            direct = "BH-Direct";
        }

        try {
            if (returnOrder != null) {
                if (!StringUtils.isEmpty(returnOrder.getReturnMethod())) {
                    if (returnOrder.getReturnMethod().equalsIgnoreCase("RPU") || returnOrder.getReturnMethod().equalsIgnoreCase("VSM:schedule_pickup")) {
                        inventLocationId = courier;
                    } else if (returnOrder.getReturnMethod().equals("StoreReceiving")
                            || returnOrder.getReturnMethod().equalsIgnoreCase("returntoNearbyStore")
                            || returnOrder.getReturnMethod().equalsIgnoreCase("vsm:store_return")) {
                        if (!StringUtils.isEmpty(returnOrder.getFacilityCode())) {
                            inventLocationId = returnOrder.getFacilityCode();
                        } else {
                            log.info("ReturnUtil : fetching returnFacility from POS for uwItemId : {}", uwItemId);
                            posApiResponse = posUtils.getDetailsFromPos(uwItemId);
                            log.info("ReturnUtil : POS api response for uwItemId : {} : {}", uwItemId, posApiResponse);
                            if (posApiResponse != null && !StringUtils.isEmpty(posApiResponse.getReturnFacility())) {
                                inventLocationId = posApiResponse.getReturnFacility();
                            }
                        }
/*
                        if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && !uwOrder.getNavChannel().contains("FOFO")) {
                            inventLocationId = getFacilityCodeForB2BOrders(uwOrder, returnOrder);
                        } else {
                            inventLocationId = StringUtils.defaultString(uwOrder.getFacilityCode());
                        }*/
                    } else if (returnOrder.getReturnMethod().equals("DirectReceiving")
                            || returnOrder.getReturnMethod().equals("ShiptoLenskrt")
                            || uwOrder.getNavChannel().contains("FOFO")) {
                        if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                            inventLocationId = direct;
                        } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                            inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                        }
                    }
                } else if (StringUtils.isEmpty(returnOrder.getReturnMethod())) {
                    if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && uwOrder.getNavChannel().equalsIgnoreCase("WebB2B")) {
                        if (uwOrder.getParentUw() == 0) {
                            inventLocationId = getFacilityCodeForB2BOrders(uwOrder, returnOrder);
                        }

                    } else {
                        if (!StringUtils.isEmpty(returnOrder.getSource()) && (returnOrder.getSource().equals("WAREHOUSE"))
                                || uwOrder.getNavChannel().contains("FOFO")) {
                            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                                inventLocationId = direct;
                            } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                                inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                            }
                        } else if (returnOrder.getReturnType().equalsIgnoreCase("rto")) {
                            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                                inventLocationId = direct;
                            } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                                inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                            }
                        } else if (returnOrder.getReturnType().equalsIgnoreCase("awaited_rto")
                                || (returnOrder.getSource().equalsIgnoreCase("vsm"))) {
                            inventLocationId = courier;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set InventLocationId for uwOrder : {} : ", uwItemId, e);
        }
        log.info("ReturnUtil : InventLocationId for uwOrder : {} : {}", uwItemId, inventLocationId);
        return inventLocationId;
    }

    private boolean isNexsUwOrder(UwOrder uwOrder) {
        try {
            if (Objects.nonNull(uwOrder) && StringUtils.isNotBlank(uwOrder.getFacilityCode()) && !CollectionUtils.isEmpty(nexsFacilities) && nexsFacilities.contains(uwOrder.getFacilityCode())) {
                return true;
            }
            if (StringUtils.isNotBlank(uwOrder.getProductDeliveryType()) && uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B)) {
                UwOrder b2bUwOrder = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getUwItemId());
                if(Objects.nonNull(b2bUwOrder) && StringUtils.isNotBlank(b2bUwOrder.getFacilityCode())) {
                    return nexsFacilities.contains(b2bUwOrder.getFacilityCode());
                }
            }
        }
        catch (Exception e){
            log.error("[isNexsUwOrder] exception "+e);
        }
        return false;
    }

    public String getFacilityCodeForB2BOrders(UwOrder uwOrder, ReturnOrder returnOrder) {
        log.info("ReturnUtil : Finding facilityCode for B2B order for uwOrder : {}", uwOrder.getUwItemId());
        UwOrder custUwOrder = null;
        UwOrder lensUwOrder = null;
        String facilityCode = "";
        try {
            if (uwOrder.getParentUw() == 0) {
                custUwOrder = uwOrdersRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());
            } else {
                custUwOrder = uwOrdersRepository.findByUwItemId(uwOrder.getUwItemId());
            }

            if (custUwOrder != null) {
                facilityCode = custUwOrder.getFacilityCode();
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get B2B return details for uwOrder : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : facilityCode for B2B order for uwOrder : {} : {}", uwOrder.getUwItemId(), facilityCode);
        return facilityCode;
    }

    public String getSalesChannel(UwOrder uwOrder, OrdersHeader ordersHeader, ReturnOrder returnOrder) {
        log.info("ReturnUtil : getSalesChannel for uwOrder : {}", uwOrder.getUwItemId());
        String salesChannel= "";
        Integer uwItemId = uwOrder.getUwItemId();
        PosApiResponse response = null;
        UwOrder custUwOrder = null;

        try {
            if (uwOrder.getIsLocalFittingRequired()) {
                log.info("ReturnUtil : localFit order found for uwOrder : {}", uwOrder.getUwItemId());
                if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
                    salesChannel = "SGDK01";
                } else {
                    log.info("getSalesChannel : fetching details from pos for uwOrder : {}", uwOrder.getUwItemId());
                    salesChannel = "DK02";
                }
            } else {
                log.info("getSalesChannel : fetching details from ordersHeader for uwOrder : {}", uwOrder.getUwItemId());
                salesChannel = getStoreCode(ordersHeader);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to get salesChannel for uwItemid : {}", uwItemId);
        }
        log.info("ReturnUtil : salesChannel for uwOrder : {} : {}",uwItemId, salesChannel);
        return salesChannel;
    }

    public void setPSlipLegalEntity(UwOrder uwOrder, OrdersHeader ordersHeader, SalesOrderHeader returnPayload, PackingSlip packingSlip) {
        log.info("ReturnUtil : setPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        try {
            if ((uwOrder.getProductDeliveryType().equals(Constants.Common.B2B) && !uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                if (intCountries.contains(ordersHeader.getLkCountry())) {
                    packingSlip.setLegalEntity(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                } else {
                    packingSlip.setLegalEntity(Constants.Common.LegalEntityLenskart);
                }
            } else {
                packingSlip.setLegalEntity(returnPayload.getLegalEntity());
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        }
    }

    public void setPSlipDate(ReturnCreateRequest returnRequestDto, UwOrder uwOrder, PackingSlip packingSlip) {
        log.info("ReturnUtil : setPSlipDate for uwOrder : {}", uwOrder.getUwItemId());
        ReturnHistory returnHistory = null;
        Integer uwItemId = uwOrder.getUwItemId();
        ReturnOrder returnOrder = returnRequestDto.getReturnOrder();
        try {
            if (returnOrder != null) {
                log.info("ReturnUtil : fetching return_history for uwItemId : {} ", uwItemId);
                returnHistory = returnHistoryRepository.findTopByEntityIdOrderByCreatedAtDesc(returnOrder.getReturnId());
                log.info("ReturnUtil : returnHistory for uwItemId : {} : {}",uwItemId, returnHistory);
                if (returnHistory != null && returnHistory.getCreateAt() != null) {
                    packingSlip.setPackingSlipDate(dateFormat.format(returnHistory.getCreateAt()));
                } else {
                    List<ReturnHistoryDTO> returnHistoryDTOList = getReturnHistory(returnOrder.getReturnId());
                    returnHistory = getLatestReturnHistory(returnHistoryDTOList, returnOrder.getReturnId());
                    if (returnHistory != null && returnHistory.getCreateAt() != null) {
                        packingSlip.setPackingSlipDate(dateFormat.format(returnHistory.getCreateAt()));
                    }else{
                        log.info("ReturnUtil : returnHistory found null fetching data from return_order for uwOrder : {}", uwItemId);
                        if (returnOrder != null && returnOrder.getReturnCreateDatetime() != null) {
                            packingSlip.setPackingSlipDate(dateFormat.format(returnOrder.getReturnCreateDatetime()));
                        } else {
                            packingSlip.setPackingSlipDate("NA");
                        }
                    }
                }
            } else {
                log.error("ReturnUtil : returnOrder found NULL for uwItemId : {} : {}",uwItemId, returnOrder);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipDate for uwOrder : {}", uwOrder.getUwItemId());
        }
    }
    private ReturnHistory getLatestReturnHistory(List<ReturnHistoryDTO> returnHistoryDTOList, Integer returnId) {
        log.info("[getLatestReturnHistory] returnId : {}, returnHistoryDTOList : {}", returnId, returnHistoryDTOList);
        ReturnHistory returnHistory = null;
        if(!CollectionUtils.isEmpty(returnHistoryDTOList)){
            ReturnHistoryDTO returnHistoryDTO = returnHistoryDTOList.get(returnHistoryDTOList.size()-1);
            returnHistory = getReturnHistory(returnHistoryDTO, returnId);
        }
        return returnHistory;
    }

    private ReturnHistory getReturnHistory(ReturnHistoryDTO returnHistoryDTO, Integer returnId) {
        log.info("[getReturnHistory] returnId : {}, returnHistoryDTO1 : {}", returnId, returnHistoryDTO);
        ReturnHistory returnHistory = null;
        if(returnHistoryDTO != null){
            returnHistory = new ReturnHistory();
            returnHistory.setAddedBy(returnHistoryDTO.getAddedBy());
            returnHistory.setComment(returnHistoryDTO.getComment());
            returnHistory.setCourier(returnHistoryDTO.getCourier());
            returnHistory.setCreatedAt(returnHistoryDTO.getCreatedAt());
            returnHistory.setCurrentStatus(returnHistoryDTO.getCurrentStatus());
            returnHistory.setEntityType(returnHistoryDTO.getEntityType());
            returnHistory.setReverseAwb(returnHistoryDTO.getReverseAwb());
            returnHistory.setReversePickupReferenceId(returnHistoryDTO.getReversePickupReferenceId());
            returnHistory.setEntityId(returnId);
        }
        log.info("[getReturnHistory] returnId : {}, returnHistoryDTO : {}", returnId, returnHistoryDTO);
        return returnHistory;
    }

    public void setPSlipTrackingNo(ReturnCreateRequest returnRequestDto, UwOrder uwOrder, PackingSlip packingSlip) {
        log.info("ReturnUtil : setPSlipTrackingNo for uwOrder : {}", uwOrder.getUwItemId());
        try {
            String reverseAwbNo = "";
            if (returnRequestDto.getReturnOrder() != null && returnRequestDto.getReturnOrder().getReturnMethod() != null) {
                if (returnRequestDto.getReturnOrder().getReturnMethod().equalsIgnoreCase("RPU")) {
                    reverseAwbNo = returnRequestDto.getReturnOrder().getReverseAwb();
                    log.info("ReverseAWB found in returnOrder {} ", reverseAwbNo);
                    packingSlip.setTrackingNo(reverseAwbNo);
                } else {
                    packingSlip.setTrackingNo(Constants.ReturnOrder.NA);
                }
            } else {
                log.info("ReturnOrder not found in ReturnRequestDto");
                packingSlip.setTrackingNo(Constants.ReturnOrder.NA);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipTrackingNo for uwOrder : {}", uwOrder.getUwItemId());
        }
    }

    public void setPSlipSalesInvoiceNo(UwOrder uwOrder, PackingSlip packingSlip) {
        log.info("ReturnUtil : setPSlipSalesInvoiceNo for uwOrder : {}", uwOrder.getUwItemId());
        try {
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                UwOrder uwOrder1 = uwOrdersRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());
                packingSlip.setSalesInvoiceNo(StringUtils.defaultString(s3InvoiceDetailsService.getInvoiceCode(uwOrder1.getShippingPackageId())));
            } else {
                packingSlip.setSalesInvoiceNo(StringUtils.defaultString(s3InvoiceDetailsService.getInvoiceCode(uwOrder.getShippingPackageId())));
            }
        } catch (Exception e) {
            log.info("ReturnUtil : Exception found to setPSlipSalesInvoiceNo for uwOrder : {}", uwOrder.getUwItemId());
        }
    }

    public String getStoreCode(OrdersHeader ordersHeader){
        String storeCode = ordersHeader.getFacilityCode();
        if(null!=storeCode && !("0".equalsIgnoreCase(storeCode))) {
            return storeCode;
        }
        return "";
    }

    public Integer getReturnIdFromSalesOrderNumber(String salesOrderNumber) {
        Integer returnId = null;
        try {
            if (StringUtils.isNotBlank(salesOrderNumber)) {
                returnId = Integer.valueOf(salesOrderNumber.split("_")[0]);
            }
        }
        catch (Exception e){
            log.error("[getReturnIdFromSalesOrderNumber] exception: {} {}",e.getMessage(),e);
        }
        return returnId;
    }

    public void updateReturnD365SyncFlag(Integer returnId, Boolean status, String responseMessage, SalesOrderHeader returnPayload, ReturnCreateRequest returnRequestDto) {
        log.info("updateReturnD365SyncFlag : for returnId status :  {} : {}", returnId, status);
        Integer uwItemId = returnRequestDto.getUwItemId();
        try {
            List<D365ReturnTracking> d365ReturnTrackingList = null;
            log.info("updateReturnD365SyncFlag : fetching d365ReturnTracking for uwItemId :  {}", uwItemId);
            d365ReturnTrackingList = d365ReturnTrackingRepository.findByReturnId(returnId);
            if(CollectionUtils.isEmpty(d365ReturnTrackingList) && returnId != null){
                log.info("updateReturnD365SyncFlag : fetching d365ReturnTracking for uwItemId :  {} from headless returns service", uwItemId);
                d365ReturnTrackingList = getReturnTrackingFromHeadless(returnId);
            }
            log.info("updateReturnD365SyncFlag : d365ReturnTrackingList for uwItemId : {} : {} ", uwItemId, d365ReturnTrackingList);
            for (D365ReturnTracking d365ReturnTracking : d365ReturnTrackingList
            ) {
                if (!(d365ReturnTracking.getD365Flag() == 1)) {
                    if (responseMessage.contains("Unprocessable return") || responseMessage.contains("Payload creation failed")) {
                        d365ReturnTracking.setD365Flag(3);
                    } else if(Constants.ReturnOrder.LK_ITEM_RETURN.equalsIgnoreCase(responseMessage)){
                        d365ReturnTracking.setD365Flag(4);
                    }else {
                        d365ReturnTracking.setD365Flag(((status) || responseMessage.contains("already exist")) ? 1 : 2);
                    }
                    d365ReturnTracking.setReturnSyncMessage(StringUtils.left(responseMessage,300));
                    log.info("updateReturnD365SyncFlag : updating db entry in d365_return_tracking");
                    d365ReturnTracking.setUpdatedAt(new Date());
                    if(!CollectionUtils.isEmpty(d365ReturnTrackingRepository.findByReturnId(returnId))){
                        d365ReturnTracking.setReturnRetryUpdatedAt(new Date());
                        d365ReturnTrackingRepository.save(d365ReturnTracking);
                    }else{
                        try{
                            d365ReturnTracking.setReturnRetryUpdatedAt(new Date());
                            HttpHeaders headers = new HttpHeaders();
                            headers.setContentType(MediaType.APPLICATION_JSON);
                            HttpEntity<?> httpEntity = new HttpEntity<Object>(d365ReturnTracking,headers);
                            String url = returnBaseUrl + "push-data/d365_return_tracking_event";
                            log.info("url is {} and request is {}", url, httpEntity);
                            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
                            log.info("[updateReturnD365SyncFlag] responseEntity : {}",responseEntity);
                        }catch (Exception exception){
                            log.error("[updateReturnD365SyncFlag] error : {}", exception.getMessage());
                        }
                    }
                }
                log.info("updateReturnD365SyncFlag : Return D365 Sync Flag updated successfully");
            }
            log.info("updateReturnD365SyncFlag : d365ReturnTrackingList updated successfully for uwItemId : {}", uwItemId);
        } catch (Exception e) {
            log.error("updateReturnD365SyncFlag : exception for updating db entry d365 return uwItemId : {} : ", uwItemId, e);
        }
    }

    private List<D365ReturnTracking> getReturnTrackingFromHeadless(Integer returnId){
        List<D365ReturnTracking> d365ReturnTrackingList = null;
        try{
            String apiUrl = returnBaseUrl + "return/details/v1.0/d365/return-tracking/" + returnId;

            log.info("[getReturnTrackingFromHeadless] apiUrl : {}", apiUrl);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);

            ResponseEntity<D365ReturnTrackingResponse> response = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.GET,
                    entity,
                    D365ReturnTrackingResponse.class
            );

            log.info("[getReturnTrackingFromHeadless] response : {}", response);

            if(response.getBody() != null){
                D365ReturnTrackingResponse d365ReturnTrackingResponse = response.getBody();
                d365ReturnTrackingList = d365ReturnTrackingResponse.getD365ReturnTracking();
            }
            log.info("[getReturnTrackingFromHeadless] d365ReturnTrackingList : {}",d365ReturnTrackingList);

        }catch (Exception exception){
            log.error("[getReturnTrackingFromHeadless] exception : {}", exception.getMessage());
        }
        return d365ReturnTrackingList;
    }

    public void updateD365PSlipSyncFlag(Integer returnId, Boolean pSlipResponseStatus, String pslipMessage, ReturnCreateRequest returnRequestDto) {
        log.info("updateD365PSlipSyncFlag : for returnId pSlipResponseStatus :  {} : {}", returnId, pSlipResponseStatus);
        Integer uwItemId = returnRequestDto.getUwItemId();
        boolean isReturnFromHeadless = false;
        try {
            List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingRepository.findByReturnId(returnId);
            if(CollectionUtils.isEmpty(d365ReturnTrackingList) && returnId != null){
                log.info("updateReturnD365SyncFlag : fetching d365ReturnTracking for uwItemId :  {} from headless returns service", uwItemId);
                d365ReturnTrackingList = getReturnTrackingFromHeadless(returnId);
                isReturnFromHeadless = true;
            }
            log.info("updateD365PSlipSyncFlag : d365ReturnTrackingList for uwItemId : {} : {} ", uwItemId, d365ReturnTrackingList);
            for (D365ReturnTracking d365ReturnTracking : d365ReturnTrackingList
            ) {
                if (!(d365ReturnTracking.getPslipCreated() == 1)) {
                    d365ReturnTracking.setPslipCreated((pSlipResponseStatus) ? 1 : 2);
                    d365ReturnTracking.setPslipSyncMessage(StringUtils.left(pslipMessage,300));
                    d365ReturnTracking.setPSlipRetryCount(d365ReturnTracking.getPSlipRetryCount()+1);
                    d365ReturnTracking.setPslipRetryUpdatedAt(new Date());
                    log.info("updateD365PSlipSyncFlag : updating db entry for d365 pSlip for uwItemId : {}", uwItemId);
                    d365ReturnTracking.setUpdatedAt(new Date());
                    if(isReturnFromHeadless){
                        String url = returnBaseUrl + "push-data/d365_return_tracking_event";
                        HttpHeaders headers = new HttpHeaders();
                        headers.setContentType(MediaType.APPLICATION_JSON);
                        HttpEntity<?> httpEntity = new HttpEntity<Object>(d365ReturnTracking,headers);
                        log.info("updateD365PSlipSyncFlag url is {} and request is {}", url, httpEntity);
                        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
                        log.info("[updateD365PSlipSyncFlag] responseEntity : {}",responseEntity);
                    }else{
                        d365ReturnTrackingRepository.save(d365ReturnTracking);
                    }

                    if(StringUtils.isNotEmpty(d365ReturnTracking.getPslipSyncMessage()) &&
                            ( serialNumberErrorPattern.matcher(d365ReturnTracking.getPslipSyncMessage()).find() || hasNotBeenCreatedPattern.matcher(d365ReturnTracking.getPslipSyncMessage()).find() ) ) {
                        log.info("triggering inventory correction for returnId : {}",d365ReturnTracking.getReturnId());
                        returnOrderService.generatePayloadAndPushToKafka(d365ReturnTracking);
                    }
                }
            }
            log.info("updateD365PSlipSyncFlag : d365ReturnTrackingList updated successfully for uwItemId : {}", uwItemId);
        } catch (Exception e) {
            log.error("updateD365PSlipSyncFlag : exception found to update pSlip status for uwItemId: {} : ", uwItemId, e);
        }
    }

    public void updateReturnD365SyncFlagRetry(Integer returnId, Boolean status, String responseMessage, SalesOrderHeader returnPayload, ReturnCreateRequest returnRequestDto) {
        log.info("updateReturnD365SyncFlagRetry : for returnId status :  {} : {}", returnId, status);
        Integer uwItemId = returnRequestDto.getUwItemId();
        boolean isReturnFromHeadless = false;
        try {
            log.info("updateReturnD365SyncFlagRetry : fetching d365ReturnTracking for uwItemId :  {}", uwItemId);
            List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingRepository.findByReturnId(returnId);
            if(CollectionUtils.isEmpty(d365ReturnTrackingList) && returnId != null){
                log.info("updateReturnD365SyncFlagRetry : fetching d365ReturnTracking for uwItemId :  {} from headless returns service", uwItemId);
                d365ReturnTrackingList = getReturnTrackingFromHeadless(returnId);
                isReturnFromHeadless = true;
            }
            log.info("updateReturnD365SyncFlagRetry : d365ReturnTrackingList for uwItemId : {} : {} ", uwItemId, d365ReturnTrackingList);
                for (D365ReturnTracking d365ReturnTracking : d365ReturnTrackingList
                ) {
                    if (!(d365ReturnTracking.getD365Flag() == 1)) {
                        if (responseMessage.contains("Unprocessable return") || responseMessage.contains("Payload creation failed")) {
                            d365ReturnTracking.setD365Flag(3);
                        } else {
                            d365ReturnTracking.setD365Flag(((status) || responseMessage.contains("already exist")) ? 1 : 2);
                        }
                        d365ReturnTracking.setReturnSyncMessage(StringUtils.left(responseMessage,300));
                        d365ReturnTracking.setRetryCount(d365ReturnTracking.getRetryCount() + 1);
                        d365ReturnTracking.setReturnRetryUpdatedAt(new Date());
                        log.info("updateReturnD365SyncFlagRetry : updating db entry in d365_return_tracking");
                        d365ReturnTracking.setUpdatedAt(new Date());
                        if(isReturnFromHeadless){
                            try{
                                HttpHeaders headers = new HttpHeaders();
                                headers.setContentType(MediaType.APPLICATION_JSON);
                                HttpEntity<?> httpEntity = new HttpEntity<Object>(d365ReturnTracking,headers);
                                String url = returnBaseUrl + "push-data/d365_return_tracking_event";
                                log.info("url is {} and request is {}", url, httpEntity);
                                ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
                                log.info("updateReturnD365SyncFlagRetry responseEntity : {}",responseEntity);
                            }catch (Exception exception){
                                log.error("[updateReturnD365SyncFlagRetry] error : {}", exception.getMessage());
                            }
                        }else{
                            d365ReturnTrackingRepository.save(d365ReturnTracking);
                        }
                    }
                }
            log.info("updateReturnD365SyncFlagRetry : return flag in retry updated successfully for uwItemId : {}", uwItemId);
        } catch (Exception e) {
            log.error("updateReturnD365SyncFlagRetry : exception for updating db entry in return retry uwItemId : {} : ", uwItemId, e);
        }
    }
    public void updateD365PSlipSyncFlagInRetry(Integer returnId, Boolean pSlipResponseStatus, String responseMessage, ReturnCreateRequest returnRequestDto) {
        Integer uwItemId = returnRequestDto.getUwItemId();
        log.info("updatePslipRetry : updating PSlip retry count and status for uwItemId : {}", uwItemId);
        try {
            log.info("updatePslipRetry : pSlipResponseStatus and responseMessage for uwItemId : {} : {} : {}", uwItemId, pSlipResponseStatus, responseMessage);
            List<D365ReturnTracking> d365ReturnTrackingRetryList = d365ReturnTrackingRepository.findByReturnId(returnId);
            log.info("updatePslipRetry : d365ReturnTrackingRetry for d365 pSlip retry for uwItemId : {} : {}", uwItemId, d365ReturnTrackingRetryList);
            for (D365ReturnTracking d365ReturnTrackingRetry : d365ReturnTrackingRetryList
            ) {
                if (!(d365ReturnTrackingRetry.getPslipCreated() == 1)) {
                    d365ReturnTrackingRetry.setPslipCreated((pSlipResponseStatus) ? 1 : 2);
                    d365ReturnTrackingRetry.setPSlipRetryCount(d365ReturnTrackingRetry.getPSlipRetryCount() + 1);
                    d365ReturnTrackingRetry.setPslipRetryUpdatedAt(new Date());
                    d365ReturnTrackingRetry.setPslipSyncMessage(StringUtils.left(responseMessage,300));
                    log.info("updatePslipRetry : updating db entry for d365 pSlip retry for uwItemId : {}", uwItemId);
                    d365ReturnTrackingRetry.setUpdatedAt(new Date());
                    d365ReturnTrackingRepository.save(d365ReturnTrackingRetry);
                    if(StringUtils.isNotEmpty(d365ReturnTrackingRetry.getPslipSyncMessage()) &&
                            ( serialNumberErrorPattern.matcher(d365ReturnTrackingRetry.getPslipSyncMessage()).find() || hasNotBeenCreatedPattern.matcher(d365ReturnTrackingRetry.getPslipSyncMessage()).find() ) ) {
                        log.info("triggering inventory correction for returnId : {}",d365ReturnTrackingRetry.getReturnId());
                        returnOrderService.generatePayloadAndPushToKafka(d365ReturnTrackingRetry);
                    }
                }
            }
            log.info("updatePslipRetry : d365ReturnTrackingList updated successfully for uwItemId : {}", uwItemId);
        } catch (Exception e) {
            log.error("updatePslipRetry : exception found to update PSlip retry : {} : ", uwItemId, e);
        }
    }

    public void updateD365MovJournalSyncFlag(Integer returnId, boolean status, Integer uwItemId, ReturnCreateRequest returnRequestDto) {
        log.info("updateMovJFlag : updating movJournalFlag for uwItemId: {} {}", uwItemId, returnId);
        try {
            if (returnRequestDto != null) {
                List<D365ReturnTracking> d365ReturnTrackingRetryList = d365ReturnTrackingRepository.findByReturnId(returnId);
                log.info("updateMovJFlag : d365ReturnTrackingRetry for uwItemId : {} : {}", uwItemId, d365ReturnTrackingRetryList);
                for (D365ReturnTracking d365ReturnTrackingRetry : d365ReturnTrackingRetryList
                ) {
                    if (!(d365ReturnTrackingRetry.getMovJournalFlag() == 1)) {
                        d365ReturnTrackingRetry.setMovJournalFlag((status) ? 1 : 2);
                        log.info("updateMovJFlag : updating movJournalFlag for uwItemId: {} : {}",uwItemId, status);
                        d365ReturnTrackingRepository.save(d365ReturnTrackingRetry);
                    }
                }
                log.info("updateMovJFlag : d365ReturnTrackingList updated successfully for uwItemId : {}", uwItemId);
            }
        } catch (Exception e) {
            log.error("updateMovJFlag : exception found to update movJournalFlag for uwItemId : {} {} {}", uwItemId, e.getMessage(), e);
        }
    }

    public boolean updateD365SyncFlag(ResponseEntity responseEntity, D365TransferJournalDTO d365TransferJournalDTO) {
        log.info("[ReturnUtil][updateD365SyncFlag] d365TransferJournalDTO : {}",d365TransferJournalDTO);
        try {
            Boolean status = false;
            List<D365TransferJournalTracking> d365TransferJournalTrackingList = d365TransferJournalTrackingRepository.findByUwItemId(d365TransferJournalDTO.getUwItemId());

            if (d365TransferJournalTrackingList.isEmpty()) {
                if (null != d365TransferJournalDTO) {
                    D365TransferJournalTracking d365TransferJournalTrackingItem = generateD365Data(d365TransferJournalDTO);
                    d365TransferJournalTrackingList = new ArrayList<>();
                    d365TransferJournalTrackingList.add(d365TransferJournalTrackingItem);
                }
            }

            if (null != responseEntity) {
                Map responseBody = (HashMap) responseEntity.getBody();
                if (responseBody != null) {
                    String message = String.valueOf(responseBody.get(Constants.Common.MESSAGE));
                    status = Boolean.valueOf(String.valueOf(responseBody.get(Constants.Common.SUCCESS)));
                    log.info("[ReturnUtil][updateD365SyncFlag] status : {}", status);
                    for (D365TransferJournalTracking d365TransferJournalTracking : d365TransferJournalTrackingList) {

                        if (status) {
                            d365TransferJournalTracking.setD365Flag(1);
                        } else {
                            d365TransferJournalTracking.setD365Flag(2);
                        }
                        d365TransferJournalTracking.setMessage(StringUtils.left(message, 300));
                        log.info("[ReturnUtil][updateD365SyncFlag] updating retry_count in TJournal creation for uwItemId : {}",d365TransferJournalDTO.getUwItemId());
                        if (d365TransferJournalTracking.getRetryCount() == null) {
                            d365TransferJournalTracking.setRetryCount(0);
                        } else {
                            d365TransferJournalTracking.setRetryCount(d365TransferJournalTracking.getRetryCount() + 1);
                        }


                        d365TransferJournalTracking.setUpdatedAt(new Date());
                        d365TransferJournalTrackingRepository.save(d365TransferJournalTracking);
                    }

                    log.info("[ReturnUtil][updateD365SyncFlag] retry_count in TJournal creation updated successfully for uwItemId : {}",d365TransferJournalDTO.getUwItemId());
                }
            }
        }
        catch (Exception e){
            log.error("[ReturnUtil][updateD365SyncFlag] exception found to create TJournal for uwItemId : ",e);
        }

        return true;
    }

    private D365TransferJournalTracking generateD365Data(D365TransferJournalDTO d365TransferJournalDTO) throws RecordNotFoundException {
        D365TransferJournalTracking d365TransferJournalTracking = new D365TransferJournalTracking();
        Integer uwItemId = null;
        Integer incrementId = d365TransferJournalDTO.getIncrementId();
        String barcode = null;
        if (null != d365TransferJournalDTO.getUwItemId()) {
            uwItemId = d365TransferJournalDTO.getUwItemId();
            log.info("[ReturnUtil][generateD365Data] uwItemId in TJournal : {}",uwItemId);
            d365TransferJournalTracking.setUwItemId(uwItemId);
            UwOrder uwOrder = uwOrdersService.getUwOrderByUwItemId(uwItemId);
            if(null!=uwOrder) {
                incrementId = uwOrder.getIncrementId();
                barcode = uwOrder.getBarcode();
            }
            log.info("[ReturnUtil][generateD365Data] incrementId in TJournal : {}",incrementId);
        }
        if (null != d365TransferJournalDTO.getPutawayCode()) {
            d365TransferJournalTracking.setPutawayCode(d365TransferJournalDTO.getPutawayCode());
        }
        d365TransferJournalTracking.setIncrementId(incrementId);
        d365TransferJournalTracking.setBarcode(barcode);
        d365TransferJournalTracking.setInventoryType(d365TransferJournalDTO.getInventoryType());
        d365TransferJournalTracking.setD365Flag(0);
        d365TransferJournalTracking.setReceivedDate(d365TransferJournalDTO.getReceivedDate());
        d365TransferJournalTracking.setTransferFlag(d365TransferJournalDTO.getIsTransferOrder());
        log.info("[ReturnUtil][generateD365Data] d365TrackingData : {}", d365TransferJournalTracking);
        return d365TransferJournalTracking;
    }



    public void setLegalEntity(UwOrder uwOrder, SalesOrderHeader salesOrderHeader, OrdersHeader ordersHeader, boolean isSbrt) {
        log.info("ReturnUtil : Setting LegalEntity for uwOrder : {}",uwOrder.getUwItemId());
        try {
            if (isSbrt) {
                salesOrderHeader.setLegalEntity(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
            }
            else {
                if ((uwOrder.getProductDeliveryType().equals(Constants.Common.B2B) && !uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        salesOrderHeader.setLegalEntity(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                    } else {
                        salesOrderHeader.setLegalEntity(Constants.Common.LegalEntityDealskart);
                    }
                } else {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        salesOrderHeader.setLegalEntity(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                    } else {
                        salesOrderHeader.setLegalEntity(Constants.Common.LegalEntityLenskart);
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set LegalEntity for uwOrder : {}",uwOrder.getUwItemId());
        }
    }
    public String getPSlipLegalEntity(UwOrder uwOrder, OrdersHeader ordersHeader, SalesOrderHeader returnPayload, PackingSlip packingSlip, boolean isSbrt) {
        log.info("ReturnUtil : getPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        String pSlipLegalEntity = "";
        try {
            if (isSbrt) {
                pSlipLegalEntity = Constants.ReturnOrder.LK + ordersHeader.getLkCountry();
            }
            else {
                if ((uwOrder.getProductDeliveryType().equals(Constants.Common.B2B) && !uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        pSlipLegalEntity = Constants.ReturnOrder.LK + ordersHeader.getLkCountry();
                    } else if(uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)){
                        pSlipLegalEntity = Constants.Common.LegalEntityDealskart;
                    } else {
                        pSlipLegalEntity = Constants.Common.LegalEntityLenskart;
                    }
                } else {
                    pSlipLegalEntity = returnPayload.getLegalEntity();
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to getPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        }
        log.info("ReturnUtil : getPSlipLegalEntity for uwOrder : {} : {}", uwOrder.getUwItemId(), pSlipLegalEntity);
        return pSlipLegalEntity;
    }

    public String getPSlipLegalEntity(UwOrder uwOrder, OrdersHeader ordersHeader, boolean isSbrt) {
        log.info("ReturnUtil : getPSlipLegalEntity for uwOrder : {}", uwOrder.getUwItemId());
        try {
            if (isSbrt) {
                return Constants.ReturnOrder.LK + ordersHeader.getLkCountry();
            } else {
                if ((uwOrder.getProductDeliveryType().equals(Constants.Common.B2B) && !uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        return Constants.ReturnOrder.LK + ordersHeader.getLkCountry();
                    } else if (uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                        return Constants.Common.LegalEntityDealskart;
                    } else {
                        return Constants.Common.LegalEntityLenskart;
                    }
                } else {
                    if (intCountries.contains(ordersHeader.getLkCountry())) {
                        return (Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                    } else {
                        return (Constants.Common.LegalEntityLenskart);
                    }

                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found in fetching packingSlipLegal entity for uwOrder : {}", uwOrder.getUwItemId(), e);
        }
        return null;
    }

    public void setPSlipLegalEntityRetry(UwOrder uwOrder, OrdersHeader ordersHeader, ReturnCreateRequest pSlipRequest, PackingSlip packingSlip) {
        log.info("ReturnUtil : setting PSlip legalEntity in retry");
        try {
            if ((uwOrder.getProductDeliveryType().equals(Constants.Common.B2B) && !uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                if (intCountries.contains(ordersHeader.getLkCountry())) {
                    packingSlip.setLegalEntity(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                } else if(uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)){
                    packingSlip.setLegalEntity(Constants.Common.LegalEntityDealskart);
                }else {
                    packingSlip.setLegalEntity(Constants.Common.LegalEntityLenskart);
                }
            } else {
                if (intCountries.contains(ordersHeader.getLkCountry())) {
                    packingSlip.setLegalEntity(Constants.ReturnOrder.LK + ordersHeader.getLkCountry());
                } else {
                    packingSlip.setLegalEntity(Constants.Common.LegalEntityLenskart);
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set LegalEntity in retry for uwOrder : {}",uwOrder.getUwItemId());
        }

    }

    public void setPSlipDateRetry(ReturnCreateRequest pSlipRequest, UwOrder uwOrder, PackingSlip packingSlip) {
        log.info("ReturnUtil : setting PSlipDate in Retry");
        ReturnHistory returnHistory = null;
        Integer uwItemId = uwOrder.getUwItemId();
        ReturnOrder returnOrder = pSlipRequest.getReturnOrder();
        try {
            if (pSlipRequest.getReturnOrder() != null) {
                log.info("fetching return_history for returnId : {} ", returnOrder.getReturnId());
                returnHistory = returnHistoryRepository.findTopByEntityIdOrderByCreatedAtDesc(returnOrder.getReturnId());
                log.info("returnHistory from db : {} ", returnHistory);
                if (returnHistory != null) {
                    packingSlip.setPackingSlipDate(dateFormat.format(returnHistory.getCreateAt()));
                } else {
                    List<ReturnHistoryDTO> returnHistoryDTOList = getReturnHistory(returnOrder.getReturnId());
                    returnHistory = getLatestReturnHistory(returnHistoryDTOList, returnOrder.getReturnId());
                    if (returnHistory != null) {
                        packingSlip.setPackingSlipDate(dateFormat.format(returnHistory.getCreateAt()));
                    }else{
                        log.info("ReturnUtil : returnHistory found null fetching data from return_order for uwOrder : {}", uwItemId);
                        if (returnOrder != null && returnOrder.getReturnCreateDatetime() != null) {
                            packingSlip.setPackingSlipDate(dateFormat.format(returnOrder.getReturnCreateDatetime()));
                        } else {
                            packingSlip.setPackingSlipDate("NA");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipDateRetry for uwOrder : {}", uwItemId);
        }
    }

    public void setPSlipTrackingNoRetry(ReturnCreateRequest pSlipRequest, UwOrder uwOrder, PackingSlip packingSlip) {
        log.info("ReturnUtil : setPSlipTrackingNo in retry for uwOrder : {}", uwOrder.getUwItemId());
        try {
            String reverseAwbNo = "";
            if (pSlipRequest.getReturnOrder() != null && pSlipRequest.getReturnOrder().getReturnMethod() != null) {
                if (pSlipRequest.getReturnOrder().getReturnMethod().equalsIgnoreCase("RPU")) {
                    reverseAwbNo = pSlipRequest.getReturnOrder().getReverseAwb();
                    log.info("ReverseAWB found in returnOrder {} ", reverseAwbNo);
                    packingSlip.setTrackingNo(reverseAwbNo);
                } else {
                    packingSlip.setTrackingNo(Constants.ReturnOrder.NA);
                }
            } else {
                log.info("ReturnOrder not found in ReturnRequestDto in retry");
                packingSlip.setTrackingNo(Constants.ReturnOrder.NA);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setPSlipTrackingNo for uwOrder in retry : {}", uwOrder.getUwItemId());
        }
    }

    public boolean createSalesChannelAtPos(String salesChannel, Integer uwItemId) {
        log.info("ReturnUtil : Creating SalesChannel at POS end for uwItemId : {}",uwItemId);
        boolean salesChannelCreated = false;
        log.info("ReturnUtil : Invalid SalesChannel for uwItemId : {} {}", uwItemId,salesChannel);
        String posApiFlag = "";
        try {
            SystemPreference sysPref = systemPreferenceRepository.findByKey(Constants.SYSTEM_PREFERENCE_KEYS.D365_POS_API_CALL_FLAG);
            if (sysPref != null) {
                posApiFlag = sysPref.getValue();
            }
            if (!StringUtils.isEmpty(posApiFlag) && posApiFlag.equals("1")) {
                String response = posUtils.callPosNSOApi(salesChannel, uwItemId);
                log.info("ReturnUtil : SalesChannel creation response from POS for uwItemId : {} {}",uwItemId,response);
                if (!StringUtils.isEmpty(response) && response.equalsIgnoreCase("true")) {
                    salesChannelCreated = true;
                    log.info("ReturnUtil : SalesChannel created successfully at POS end for uwItemId : {} {}",uwItemId,salesChannel);
                } else {
                    log.error("ReturnUtil : SalesChannel created failed at POS end for uwItemId : {} {}",uwItemId,salesChannel);
                }

            }

        } catch (Exception e) {
            log.error("ReturnUtil : Unable to create SALESCHANNEL at POS end : ",e);
        }
        return salesChannelCreated;
    }

    public List<String> getReturnOrderErrorWhitelist() {
        List<String> whitelistedErrors = new ArrayList<>();
        try {
            String errorWhitelist = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.RETURN_ORDER_ERROR_WHITELIST, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (!StringUtils.isEmpty(errorWhitelist)) {
                whitelistedErrors = Arrays.asList(errorWhitelist.split(";"));
            }
        }
        catch(Exception e){
            log.error("[getReturnOrderErrorWhitelist] exception {} {}",e.getMessage(),e);
        }
        return whitelistedErrors;
    }

    public Boolean getPSlipSyncBarcodeFlag() {
        try {
            String pslipSyncFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.PSLIP_SYNC_BARCODE_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != pslipSyncFlag && pslipSyncFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getPSlipSyncBarcodeFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public Boolean getTriggerProductSyncFlag() {
        try {
            String pidSyncFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.PRODUCT_SYNC_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != pidSyncFlag && pidSyncFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getPSlipSyncBarcodeFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public Boolean getPSlipRetryFlag() {
        try {
            String pidRetryFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.P_SLIP_RETRY_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != pidRetryFlag && pidRetryFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getPSlipRetryFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public Boolean getTOrderRetryFlag() {
        try {
            String tOrderRetryFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.T_ORDER_RETRY_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != tOrderRetryFlag && tOrderRetryFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getTOrderRetryFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public Boolean getExchangeRetryFlag() {
        try {
            String tOrderRetryFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.EXCHANGE_RETRY_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != tOrderRetryFlag && tOrderRetryFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getTOrderRetryFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public Boolean getExchangeJunoRetryFlag() {
        try {
            String tOrderRetryFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.EXCHANGE_JUNO_RETRY_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != tOrderRetryFlag && tOrderRetryFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getTOrderRetryFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public Date getSaleOrderDispatchDate(UwOrder uwOrder) {
        Integer uwItemId = uwOrder.getUwItemId();
        log.info("ReturnUtils : getSaleOrderDispatchDate for uwItemId : {}",uwItemId);
        ShippingStatus shippingStatus = null;
        Date completeTime = null;
        try {
            log.info("ReturnUtils : fetching shipping_status for uwItemId : {}",uwItemId);
            shippingStatus = shippingStatusRepository.findShippingStatus(uwItemId);
            if (shippingStatus != null) {
                completeTime = shippingStatus.getComplete_time();
                //completeTime = sdf1.format(completeTime);
            }
        } catch (Exception e) {
            log.error("ReturnUtils : exception found to fetch shipping_status for uwItemId : {} : ",uwItemId, e);
        }
        log.info("ReturnUtils : shipping completeTime for uwItemId : {} : {}",uwItemId, completeTime);
        return completeTime;
    }

    public Date getFinanceClosingDate(Integer uwItemId) {
        log.info("ReturnUtils : getting financial closing date for uwItemId : {}",uwItemId);
        String dateStr = Constants.ReturnOrder.Finance_Closing_Date;
        Date date = null;
        try {
            date = sdf1.parse(dateStr);
        } catch (Exception e) {
            log.error("ReturnUtils : Error found to parse the date for uwItemId : {} : {}",uwItemId,dateStr);
        }
        return date;
    }

    public boolean compareDate(Date saleOrderCompleteTime, Date financeClosingDate, Integer uwItemId) {
        log.info("ReturnUtils : saleOrderCompleteTime financeClosingDate for uwItemId : {} : {} : {}",uwItemId, saleOrderCompleteTime, financeClosingDate);
        if (saleOrderCompleteTime != null && financeClosingDate != null) {
            return saleOrderCompleteTime.before(financeClosingDate);
        } else {
            log.error("ReturnUtils : invalid date found cannot compare");
        }
        return false;
    }

    public ResponseEntity createItemForD365(SalesOrderHeader returnPayload, Integer uwItemId, Integer pid, ReturnResponseDto returnResponseDto) {
        log.info("ReturnUtil : creating item at d365 end for uwItemId for pid : {} : {}",uwItemId, pid);
        ResponseEntity responseEntity = null;
        String responseMessage = "";
        try {
            responseEntity = itemMasterService.syncToD365(pid);
            if (responseEntity != null) {
                Map responseBody = (Map) responseEntity.getBody();
                log.info("ReturnUtil : responseBody from d365 for uwItemId for pid : {} : {} : {}",uwItemId, pid, responseBody);
                responseMessage = (String) responseBody.get("Message");
                log.info("ReturnUtil : responseMessage from d365 for uwItemId for pid : {} : {} : {}",uwItemId, pid, responseMessage);
                Boolean responseStatus = Boolean.valueOf(String.valueOf(responseBody.get("Success")));
                log.info("ReturnUtil : responseStatus at d365 for uwItemId for pid : {} : {} : {}",uwItemId, pid, responseStatus);
                returnResponseDto.setItemMasterResponse(responseBody);
                returnResponseDto.setItemMasterCreationMessage(responseMessage);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : exception found in item creation for d365 for uwItemId with pid : {} : {} : ",uwItemId,pid, e);
        }
        return responseEntity;
    }

    public Integer getFromSysPref() {
        String key = "test_item_master";
        Integer pid = null;
        try {
            SystemPreference sysPref = systemPreferenceRepository.findByKey(key);
            if (sysPref != null) {
                pid = Integer.parseInt(sysPref.getValue());
            }
        } catch (Exception e) {
            log.error("exception found to get data from sys_pref : {}",key);
        }
        log.info("pid from sys_pref : {}",pid);
        return pid;
    }

    public String getBarCode(UwOrder uwOrder) {
        Integer uwItemId = uwOrder.getUwItemId();
        String barCode = "";
        log.info("ReturnUtil : fetching BarCode for uwOrder : {}",uwItemId);
        try {
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && b2BVirtualOrder(uwOrder)) {
                UwOrder uwOrderB2B = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
                if (uwOrderB2B != null && !StringUtils.isEmpty(uwOrderB2B.getBarcode())) {
                    barCode = StringUtils.defaultString(uwOrderB2B.getBarcode());
                }
            } else {
                if (!StringUtils.isEmpty(uwOrder.getBarcode())) {
                    barCode = StringUtils.defaultString(uwOrder.getBarcode());
                }
            }
        } catch (Exception e) {
            log.info("exception found to fetch BarCode for uwOrder : {} : ",uwItemId,e);
        }
        log.info("ReturnUtil : BarCode for uwOrder : {} : {}",uwItemId, barCode);
        return barCode;
    }

    public boolean b2BVirtualOrder(UwOrder uwOrder) {
        if (StringUtils.isNotBlank(uwOrder.getBarcode())) {
            if (!physicalFacilities.contains(uwOrder.getFacilityCode())) {
                return "B2B".equalsIgnoreCase(uwOrder.getProductDeliveryType()) && StringUtils.isNotBlank(uwOrder.getNavChannel()) && !uwOrder.getNavChannel().toLowerCase().contains(FOFO_IDENTIFIER);
            } else {
                return false;
            }
        }
        return true;
    }

    public boolean validateReturnRequest(ReturnCreateRequest returnCreateRequest) {
        Integer uwItemId = returnCreateRequest.getUwItemId();
        log.info("ReturnUtils : validateReturnRequest for uwItemId : {}",uwItemId);
        ReturnOrderItem returnOrderItem = null;
        Product product = null;
        List<String> loyaltyServices = null;
        try {
            ReturnDetailsDTO returnDetails = getReturns("UW_ITEM_ID", String.valueOf(uwItemId));
            returnOrderItem = getReturnOrderItem(returnDetails);
            product = productService.getProduct(returnOrderItem.getProductId());
            loyaltyServices = systemPreferenceService.getValuesAsList("lk_3orfree_service", "lk_cash_loyalty_services");
            log.info("ReturnUtils : loyaltyServices list for uwItemId : {} : {}",uwItemId,loyaltyServices);
        } catch (Exception e) {
            log.error("ReturnUtils : exception found to get loyaltyServices for uwItemId : {} : ",uwItemId, e);
        }
        if (loyaltyServices != null) {
            return loyaltyServices.contains(String.valueOf(product.getClassification()));
        }
        return false;
    }

    public String getShippingPackageId(UwOrder uwOrder) {
        Integer uwItemId = uwOrder.getUwItemId();
        UwOrder uwOrderB2b = null;
        String shippingPackageId = "";
        log.info("ReturnUtil : fetching shippingPackageId for uwOrder : {}",uwItemId);
        try {
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                uwOrderB2b = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
                if (uwOrderB2b != null) {
                    shippingPackageId = uwOrderB2b.getShippingPackageId();
                }
            } else {
                shippingPackageId = uwOrder.getShippingPackageId();
            }
        }catch (Exception e) {
            log.error("ReturnUtils : exception found to get shippingPackageId for uwItemId : {} : ",uwItemId, e);
        }
        log.info("ReturnUtil : shippingPackageId for uwOrder : {} : {}",uwItemId, shippingPackageId);
        return shippingPackageId;
    }

    public void updateD365Tracking(ReturnCreateRequest returnCreateRequest, String responseMessage) {
        log.info("ReturnUtil : updateD365Tracking for uwItemId : {}",returnCreateRequest.getUwItemId());
        Integer uwItemId = returnCreateRequest.getUwItemId();
        Integer returnId = null;
        Boolean status = false;
        try {
            if(returnCreateRequest.getReturnId() != null) {
                returnId = returnCreateRequest.getReturnId();
            } else {
                return;
            }
            log.info("ReturnUtil : updateReturnD365SyncFlag for uwItemId : {} : {}",uwItemId, returnId);
            updateReturnD365SyncFlag(returnId, status, responseMessage, null, returnCreateRequest);
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to updateD365Tracking : {} : ",uwItemId,e);
        }
    }


    @Autowired
    ProductService productService;

    @Value("${d365.lenskart.virtual.facility}")
    String lkVirtualFacility;

    @Autowired
    private OrderOpsClient orderOpsClient;

    public boolean isB2BPhysicalItem(UwOrder uwOrder) {
        try {
            if(Objects.nonNull(uwOrder) && StringUtils.isNotBlank(uwOrder.getFacilityCode())){
                return physicalFacilities.stream().anyMatch(uwOrder.getFacilityCode()::equalsIgnoreCase);
            }
        }
        catch (Exception e){
            log.error("[isB2BPhysicalItem] exception "+e);
        }
        return false;
    }

    public boolean checkIfReturnEInvoiceRequired(ReturnOrder returnOrder, UwOrder uwOrder, OrdersHeader ordersHeader) {
        boolean createEInvoice = false;
        boolean physicalItem = false;
        try {
            if (ObjectUtils.allNotNull(returnOrder, uwOrder, ordersHeader)) {
                if(Constants.Common.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType())){
                    physicalItem = isB2BPhysicalItem(uwOrder);
                }
                String navChannels = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.EINVOICE_DOMESTIC_NAVCHANNEL, Constants.SYSTEM_PREFERENCE_GROUPS.E_INVOICE);
                if(StringUtils.isNotBlank(navChannels)) {
                    Set<String> invoiceNavChannels = Stream.of(navChannels.split("\\s*,\\s*")).collect(Collectors.toSet());
                    log.info("[generateReturnEInvoiceRequestAndPushToKafka] navchannels for return einvoincing {}", invoiceNavChannels);
                    if(!CollectionUtils.isEmpty(invoiceNavChannels)) {
                        createEInvoice = !"awaited_rto".equalsIgnoreCase(returnOrder.getReturnType())
                                && Constants.Country.IN.equalsIgnoreCase(ordersHeader.getLkCountry())
                                && StringUtils.isNotBlank(uwOrder.getNavChannel()) && invoiceNavChannels.contains(uwOrder.getNavChannel())
                                && !physicalItem;
                        log.info("[validateReturnEInvoiceRequest] createEInvoice {}", createEInvoice);
                    }
                }

            }
        }
        catch (Exception e){
            log.error("[validateReturnEInvoiceRequest] exception ",e);
        }
        return createEInvoice;
    }

    public String getLkReferenceWhForReturn(UwOrder uwOrder, OrdersHeader ordersHeader, ReturnOrder returnOrder) {
        String inventLocationId = "";
        Integer uwItemId = uwOrder.getUwItemId();
        PosApiResponse posApiResponse = null;
        String courier = "Courier";
        String direct = "Direct";

        if(isNexsUwOrder(uwOrder)){
            courier = "BH-Courier";
            direct = "BH-Direct";
        }

        try {
            if (returnOrder != null) {
                if (!StringUtils.isEmpty(returnOrder.getReturnMethod())) {
                    if (returnOrder.getReturnMethod().equalsIgnoreCase("RPU") || returnOrder.getReturnMethod().equalsIgnoreCase("VSM:schedule_pickup")) {
                        inventLocationId = courier;
                    } else if (returnOrder.getReturnMethod().equals("StoreReceiving")
                            || returnOrder.getReturnMethod().equalsIgnoreCase("returntoNearbyStore")
                            || returnOrder.getReturnMethod().equalsIgnoreCase("vsm:store_return")) {
                        if (!StringUtils.isEmpty(returnOrder.getFacilityCode())) {
                            inventLocationId = returnOrder.getFacilityCode();
                        } else {
                            log.info("ReturnUtil : fetching returnFacility from POS for uwItemId : {}", uwItemId);
                            posApiResponse = posUtils.getDetailsFromPos(uwItemId);
                            log.info("ReturnUtil : POS api response for uwItemId : {} : {}", uwItemId, posApiResponse);
                            if (posApiResponse != null && !StringUtils.isEmpty(posApiResponse.getReturnFacility())) {
                                inventLocationId = posApiResponse.getReturnFacility();
                            }
                        }
/*
                        if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && !uwOrder.getNavChannel().contains("FOFO")) {
                            inventLocationId = getFacilityCodeForB2BOrders(uwOrder, returnOrder);
                        } else {
                            inventLocationId = StringUtils.defaultString(uwOrder.getFacilityCode());
                        }*/
                    } else if (returnOrder.getReturnMethod().equals("DirectReceiving")
                            || returnOrder.getReturnMethod().equals("ShiptoLenskrt")
                            || uwOrder.getNavChannel().contains("FOFO")) {
                        if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                            inventLocationId = direct;
                        } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                            inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                        }
                    }
                } else if (StringUtils.isEmpty(returnOrder.getReturnMethod())) {
                    if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && uwOrder.getNavChannel().equalsIgnoreCase("WebB2B")) {
                        if (uwOrder.getParentUw() == 0) {
                            inventLocationId = getFacilityCodeForB2BOrders(uwOrder, returnOrder);
                        }

                    } else {
                        if (!StringUtils.isEmpty(returnOrder.getSource()) && (returnOrder.getSource().equals("WAREHOUSE"))
                                || uwOrder.getNavChannel().contains("FOFO")) {
                            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                                inventLocationId = direct;
                            } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                                inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                            }
                        } else if (returnOrder.getReturnType().equalsIgnoreCase("rto")) {
                            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                                inventLocationId = direct;
                            } else if (intCountries.contains(ordersHeader.getLkCountry())) {
                                inventLocationId = StringUtils.defaultString(returnOrder.getFacilityCode());
                            }
                        } else if (returnOrder.getReturnType().equalsIgnoreCase("awaited_rto")
                                || (returnOrder.getSource().equalsIgnoreCase("vsm"))) {
                            inventLocationId = courier;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set InventLocationId for uwOrder : {} : ", uwItemId, e);
        }
        log.info("ReturnUtil : InventLocationId for uwOrder : {} : {}", uwItemId, inventLocationId);
        return inventLocationId;
    }

    public String getFulfillmentWH(UwOrder uwOrder){
        String fulFillmentWH = "";


        String productDeliveryType = getProductDeliveryType(uwOrder.getNavChannel());
        if(productDeliveryType.equalsIgnoreCase("B2B")){
            Optional<String> physicalFacility = getPhysicalFacilityForB2BOrder(uwOrder);
            if (physicalFacility.isPresent()) {
                fulFillmentWH = physicalFacility.get()+INTRANSIT;
            }
        }
        if(productDeliveryType.equalsIgnoreCase("bulk")){
            log.info("[getFulfillmentWH] {}", uwOrder.getFacilityCode());
            if (hubMasterService.getHubMasterFacilities().contains(uwOrder.getFacilityCode())) {
                fulFillmentWH = uwOrder.getFacilityCode()+INTRANSIT;
            }
        }
        if(productDeliveryType.equalsIgnoreCase("DTC")){
            log.info("[getFulfillmentWH] {}", uwOrder.getFacilityCode());
            if (hubMasterService.getHubMasterFacilities().contains(uwOrder.getFacilityCode())) {
                fulFillmentWH = uwOrder.getFacilityCode()+INTRANSIT;
            } else {
                fulFillmentWH = uwOrder.getFacilityCode();
            }
        }
        if(productDeliveryType.equalsIgnoreCase("OTC")){
            log.info("[getFulfillmentWH] {}", uwOrder.getFacilityCode());
            if (hubMasterService.getHubMasterFacilities().contains(uwOrder.getFacilityCode())) {
                fulFillmentWH = uwOrder.getFacilityCode()+INTRANSIT;
            } else {
                fulFillmentWH = uwOrder.getFacilityCode();
            }
        }
        log.info("[getFulfillmentWH] uwItemId : {}, fulfillmentWH : {}", uwOrder.getUwItemId(), fulFillmentWH);
        return fulFillmentWH;
    }

    private Optional<String> getPhysicalFacilityForB2BOrder(UwOrder uwOrder) {
        UwOrder b2bCounterPart = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
        return Stream.of(uwOrder.getFacilityCode(), b2bCounterPart.getFacilityCode()).filter(f -> hubMasterService.getHubMasterFacilities().contains(f)).findFirst();
    }

    public String getInventLocationIdForReturn(UwOrder uwOrder,OrdersHeader ordersHeader){
        if (uwOrder != null && uwOrder.getIsLocalFittingRequired()) {
            if (ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                return "DK02";
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
                return "SGDK01";
            }

        } else {
            String type= ordersHeader.getStoreType();
            String storeCode = "";
            String productDeliveryType = getProductDeliveryType(uwOrder.getNavChannel());
            if(Constants.Common.DTC.equalsIgnoreCase(productDeliveryType) || Constants.Common.BULK.equalsIgnoreCase(productDeliveryType)){
                storeCode = uwOrder.getFacilityCode();
                return  String.format("%s%s", storeCode, hubMasterService.getHubMasterFacilities().contains(storeCode) ? INTRANSIT : "");
            }
            else{
                return uwOrder.getFacilityCode();
            }
        }
        return uwOrder.getFacilityCode();
    }

    public UwOrder getBulkUwOrderByBarcode(String barcode) {
        log.info("[getBulkUwOrderByBarcode] getting bulk UwOrder by barcode");
        if (StringUtils.isBlank(barcode)) {
            log.error("[getBulkUwOrderByBarcode] barcode is empty with barcode");
            return null;
        }

        List<UwOrder> uwOrderList = uwOrdersRepository.findByBarcode(barcode);

        if (uwOrderList.isEmpty()) {
            log.error("[getBulkUwOrderByBarcode] uwOrderList is empty with barcode {}", barcode);
            return null;
        }

        List<UwOrder> bulkUwOrderList = uwOrderList.stream().filter(u -> u.getNavChannel() != null && u.getNavChannel().toLowerCase().contains("bulk")).collect(Collectors.toList());

        if (bulkUwOrderList.isEmpty()) {
            log.error("[getBulkUwOrderByBarcode] bulkUwOrderList is empty with barcode {} and nav channel bulk", barcode);
            return null;
        }

        Optional<UwOrder> uwOrderOp = bulkUwOrderList.stream().max((o1, o2) -> o1.getCreatedAt().after(o2.getCreatedAt()) ? 1 : 0);

        if (!uwOrderOp.isPresent()) {
            log.error("uwOrderOp is empty with barcode {} and nav channel bulk", barcode);
            return null;
        }
        UwOrder uwOrder = uwOrderOp.get();
        Gson gson = new Gson();
        log.info("Fetched bulk order: {}", gson.toJson(uwOrder));
        return uwOrder;
    }

    public String getReturnReceivingDate(Date receivedDate) {
        log.info("[ReturnUtils][getReturnReceivingDate] : getting financial closing date : {}",receivedDate);
        String receivedDatedateStr = "";
        try {
            receivedDatedateStr = receivedDateFormat.format(receivedDate);
        } catch (Exception e) {
            log.error("[ReturnUtils][getReturnReceivingDate] : Error found to parse the date for  : {}",receivedDate);
        }
        return receivedDatedateStr;
    }

    public String getFromWarehouse(UwOrder uwOrder) {
        log.info("[ReturnUtils][getFromWarehouse] Getting bulk uwOrder: {}", uwOrder.getBarcode());
        boolean isOTC = Constants.Common.OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType());
        if(isOTC && Objects.nonNull(uwOrder.getBarcode())){
            UwOrder uwOrderNew = getBulkUwOrderByBarcode(uwOrder.getBarcode());
            if(Objects.nonNull(uwOrderNew)) {
                uwOrder = uwOrderNew;
            }
        }
        return getFulfillmentWH(uwOrder);
    }

    public String getSite(UwOrder uwOrder, String facilityCode) {
        String site = null;
        try {
            if (uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B) && !hubMasterService.getHubMasterFacilities().contains(uwOrder.getFacilityCode())) {
                UwOrder b2bUwOrder = null;
                try {
                    b2bUwOrder = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
                } catch (Exception e) {
                    log.error("[getSite] error fetching uwOrder with uwItemId: {} exception: {} {}", uwOrder.getB2bRefrenceItemId(), e.getMessage(), e);
                    return null;
                }
                if (null != b2bUwOrder) {
                    site = b2bUwOrder.getFacilityCode();
                }
            } else {
                site = uwOrder.getFacilityCode();
            }
            if (null == site) {
                site = facilityCode;
            }
        }
        catch (Exception e){
            log.error("[getSite] exception occurred: {} {}",e.getMessage(),e);
        }
        log.info("[getSite] site: {}",site);
        return site;
    }

    private static boolean containsSubstringIgnoreCase(String mainString, String substring) {

        String lowerCaseMainString = mainString.toLowerCase();
        String lowerCaseSubstring = substring.toLowerCase();

        return lowerCaseMainString.contains(lowerCaseSubstring);
    }

    public String getToWarehouse(UwOrder uwOrder, String facilityCode) {
        String site = "";
        ItemResolutionFlat itemResolutionFlat = itemResolutionFlatRepository.findByUwItemId(uwOrder.getUwItemId());
        if(itemResolutionFlat != null){
            String facility = itemResolutionFlat.getFacilityCode();
            String nexsFacility = "NXS";
            if(containsSubstringIgnoreCase(facility,nexsFacility)){
                site = this.defaultNexsFacility;
            } else{
                site = itemResolutionFlat.getFacilityCode();
            }
        } else{
            site = getSite(uwOrder, facilityCode);
        }
        log.info("[PutawayUtil][getSite] uwItemId : {} site : {}",uwOrder.getUwItemId(), site);
        return site;
    }

    public void setCustomerDetails(String navChannel, String country, String customerId, String saleSource, String storeCode, SalesOrderHeader salesOrderHeader) {
        log.info("ReturnUtil : Setting CustomerDetails for uwItemId : ");
        try {
            log.info("setCustomerDetails : navChannel : " + navChannel);
            Boolean isBulk = isBulkOrder(navChannel);
            if (isBulk && !navChannel.equalsIgnoreCase(Constants.ReturnOrder.COCOBulk)) {
                log.info("setCustomerDetails : saleSource : " + saleSource);
                if (saleSource.toUpperCase().contains(Constants.Channel.AQUALENS)) {
                    salesOrderHeader.setCustomerAccount(customerId);
                    salesOrderHeader.setInvoiceAccount(customerId);
                } else {
                    log.info("setCustomerDetails : storeCode : " + storeCode);
                    salesOrderHeader.setCustomerAccount(storeCode);
                    salesOrderHeader.setInvoiceAccount(storeCode);
                }
            } else if (navChannel.equalsIgnoreCase(Constants.ReturnOrder.COCOBulk)
                    && country.equalsIgnoreCase(Constants.Country.IN)
                    || country.equalsIgnoreCase(Constants.Country.SG)
                    || country.equalsIgnoreCase(Constants.Country.AE)
                    || country.equalsIgnoreCase(Constants.Country.US)) {
                if (country.equalsIgnoreCase(Constants.Country.IN)) {
                    salesOrderHeader.setCustomerAccount(Constants.ReturnOrder.DK + country);
                    salesOrderHeader.setInvoiceAccount(Constants.ReturnOrder.DK + country);
                } else {
                    salesOrderHeader.setCustomerAccount(Constants.ReturnOrder.LK + country);
                    salesOrderHeader.setInvoiceAccount(Constants.ReturnOrder.LK + country);
                }
            } else {
                salesOrderHeader.setCustomerAccount(customerId);
                salesOrderHeader.setInvoiceAccount(customerId);
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set CustomerDetails for uwItemId : ");
        }
    }

    public Boolean isBulkOrder(String navChannel) {
        return "NULL".equalsIgnoreCase(navChannel) || navChannel.toLowerCase().contains("bulk");
    }

    public void setAddressForReturn(SalesOrderHeader salesOrderHeader, ManualSaleOrderReturnDto manualSaleOrderReturn) {
        // STC
        //salesOrderHeader.setAddressCity(orderAddressUpdate.getCity());
        salesOrderHeader.setAddressZipCode(manualSaleOrderReturn.getPincode());
        if (manualSaleOrderReturn.getCountry().equals("IN")) {
            salesOrderHeader.setAddressCountryRegionId("IND");
            salesOrderHeader.setAddressState(ProductUtil.StateMapping.getOrDefault(manualSaleOrderReturn.getState().toUpperCase().replaceAll("\\s", ""), ""));
            //salesOrderHeader.setAddressState("DL");
            salesOrderHeader.setAddressCity(manualSaleOrderReturn.getCity());
        } else {
            salesOrderHeader.setAddressCountryRegionId(manualSaleOrderReturn.getCountry());
            salesOrderHeader.setAddressState("INT");
        }
    }

    public Product getProduct(String productId){
        Product product = null;
        try {
            product = productService.getProduct(Integer.valueOf(productId));
        } catch (RecordNotFoundException e) {
            log.error("record not found !! {}",e.getMessage());
        }catch (Exception e){
            log.error("exception occurred !! {}",e.getMessage());
        }
        return product;
    }

    public String getLegalEntity(ManualSaleOrderReturnDto manualSaleOrderReturn) {
        log.info("ReturnUtil : Setting LegalEntity for magento : {}",manualSaleOrderReturn.getMagentoItemId());
        String legalEntity = "";
        String productDeliveryType = getProductDeliveryType(manualSaleOrderReturn);
        String facilityCode = getFacilityCode(manualSaleOrderReturn);
        if (manualSaleOrderReturn.getCountry().equalsIgnoreCase("SG")) {
            legalEntity = "LKSG";
        } else if (manualSaleOrderReturn.getCountry().equalsIgnoreCase("AE")) {
            legalEntity = "LKAE";
        } else if (manualSaleOrderReturn.getCountry().equalsIgnoreCase("US")) {
            legalEntity = "LKUS";
        } else if ((productDeliveryType.equalsIgnoreCase(Constants.Common.B2B) && !manualSaleOrderReturn.getNavChannel().equalsIgnoreCase(Constants.Common.FOFOB2B)) || productDeliveryType.equalsIgnoreCase(Constants.Common.OTC)) {
            legalEntity = Constants.Common.LegalEntityDealskart;
        } else if (manualSaleOrderReturn.getNavChannel().equalsIgnoreCase("COCOBulk") && !manualSaleOrderReturn.getCountry().equalsIgnoreCase("IN")) {
            HubMaster hubMaster = hubMasterService.getHubMaster(facilityCode);
            legalEntity = "LK" + hubMaster.getCountry();
        } else {
            // Local fitting,DTC
            legalEntity = Constants.Common.LegalEntityLenskart;
        }
        return legalEntity;
    }

    public String getProductDeliveryType(ManualSaleOrderReturnDto manualSaleOrderReturnDto){
        String navChannel = manualSaleOrderReturnDto.getNavChannel();
        String productDeliveryType = "";
        if(StringUtils.isNotBlank(navChannel)){
            if(navChannel.contains(Constants.Common.B2B)){
                productDeliveryType = Constants.Common.B2B;
            } else if (navChannel.contains(Constants.Common.DTC)) {
                productDeliveryType = Constants.Common.DTC;
            }else if(navChannel.contains(Constants.Common.OTC)){
                productDeliveryType = Constants.Common.OTC;
            }else{
                productDeliveryType = Constants.Common.BULK;
            }
        }
        return productDeliveryType;
    }

    public void setModeOfPayment(SalesOrderHeader salesOrderHeader, ManualSaleOrderReturnDto manualSaleOrderReturn) {
        log.info("[ReturnUtil][setModeOfPayment] : Setting ModeOfPayment for uwItemId : {}",manualSaleOrderReturn.getMagentoItemId());
        try {
            String paymentMode = manualSaleOrderReturn.getPaymentGateway();
            if (paymentMode != null) {
                if (paymentMode.equalsIgnoreCase("cashondelivery")) {
                    salesOrderHeader.setModeOfPayment(Constants.SalesOrder.COD);
                    salesOrderHeader.setTermsOfPayment(Constants.SalesOrder.COD);
                } else {
                    salesOrderHeader.setModeOfPayment(StringUtils.defaultString(paymentMode).trim());
                    salesOrderHeader.setTermsOfPayment("");
                }
            }
        } catch (Exception e) {
            log.error("[ReturnUtil][setModeOfPayment] : Exception found to set ModeOfPayment for uwItemId : {}",manualSaleOrderReturn.getMagentoItemId());
        }
    }

    public void setSoLineHsnAndSacCode(Product product, SoLine soLine, ManualSaleOrderReturnDto manualSaleOrderReturnDto) {
        log.info("ReturnUtil : setSoLineHsnAndSacCode for uwItemId : {}",manualSaleOrderReturnDto.getMagentoItemId());
        String hsncode = "";
        try {
            String productDetail = manualSaleOrderReturnDto.getProductDetail();
            Map productDetailMap = objectMapper.readValue(productDetail, Map.class);
            if(productDetailMap != null){
                hsncode = productDetailMap.get("hsnCode") != null ? productDetailMap.get("hsnCode").toString() : "";
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to setSoLineHsnAndSacCode for uwItemId : {}",manualSaleOrderReturnDto.getMagentoItemId());
        }
        if(StringUtils.isBlank(hsncode) && blankHsnCodeClassifications.contains(String.valueOf(product.getClassification()))){
            hsncode = ProductUtil.getHSNCode(product.getHsnCode());
        }
        log.info("ReturnUtil : hsnCode for uwItemId : {} : {}",manualSaleOrderReturnDto.getMagentoItemId(),hsncode);
        soLine.setHsnCode(hsncode);
        soLine.setSacCode("");
    }

    public void setSoLineTax(SoLine soLine, ManualSaleOrderReturnDto manualSaleOrderReturnDto) {
        log.info("ReturnUtil : setSoLineTax for uwItemId : {}", manualSaleOrderReturnDto.getMagentoItemId());
        Double taxRate = 0.00D;

        try {
            taxRate = manualSaleOrderReturnDto.getCgst()
                    + manualSaleOrderReturnDto.getIgst()
                    + manualSaleOrderReturnDto.getSgst()
                    + manualSaleOrderReturnDto.getUgst();
            log.info("ReturnUtil : TaxRate for uwItemId : {} : {}", manualSaleOrderReturnDto.getMagentoItemId(), taxRate);
            soLine.setSalesOrderItemCode(String.valueOf(manualSaleOrderReturnDto.getMagentoItemId()));
            soLine.setTaxRateType(String.format("%.2f", taxRate));
            soLine.setItemSalesTaxGrp(String.format("%.2f", taxRate));
            soLine.setSalesTaxGrp(String.format("%.2f", taxRate));
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get taxRate from order-ops-client for uwItemId : {}", manualSaleOrderReturnDto.getMagentoItemId());
        }
    }

    public void setSoLinePurchPrice(ManualSaleOrderReturnDto manualSaleOrderReturnDto, SoLine soLine) {
        log.info("ReturnUtil : setSoLinePurchPrice for uwItemId : {}", manualSaleOrderReturnDto.getMagentoItemId());
        Double purchPrice = 0.0;
        String productDeliveryType = getProductDeliveryType(manualSaleOrderReturnDto);
        try {
            if (productDeliveryType.equalsIgnoreCase("B2B")) {
                purchPrice = manualSaleOrderReturnDto.getUnitPrice();
            }
        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to get purchPrice for uwItemId : {}", manualSaleOrderReturnDto.getMagentoItemId());
        }
        log.info("ReturnUtil : purchPrice for uwItemId : {} : {}", manualSaleOrderReturnDto.getMagentoItemId(),purchPrice);
        soLine.setPurchPrice(String.valueOf(purchPrice));
    }

    public void setSoLineConfirmedReceiptDate(ManualSaleOrderReturnDto manualSaleOrderReturnDto, SoLine soLine) {
        log.info("ReturnUtil : fetching ConfirmedReceiptDate for magento : {}", manualSaleOrderReturnDto.getMagentoItemId());
        try {
            soLine.setConfirmedReceiptDate(StringUtils.defaultString(setFormattedDate(manualSaleOrderReturnDto.getReversePickupDate())));
            soLine.setConfirmedShipDate(StringUtils.defaultString(setFormattedDate(manualSaleOrderReturnDto.getReversePickupDate())));
        } catch (Exception e) {
            log.error("ReturnUtil : exception found to fetch ConfirmedReceiptDate for magento : {}", manualSaleOrderReturnDto.getMagentoItemId());
        }
    }

    public String setFormattedDate(String date) {
        DateFormat inputFormat = new SimpleDateFormat("dd/MM/yyyy hh:mm");
        String formattedDate = "";
        try {
            Date parsedDate = inputFormat.parse(date);
            formattedDate = dateFormat.format(parsedDate);
        } catch (ParseException e) {
            log.error("[setReturnDate] date parse error : {}" , e.getMessage());
        }
        return formattedDate;
    }


    public String getInventLocationIdForReturn(ManualSaleOrderReturnDto manualSaleOrderReturn){
        boolean isLocalFittingRequired = getIsLocalFittingRequired(manualSaleOrderReturn.getFacility());
        String facilityCode = getFacilityCode(manualSaleOrderReturn);
        String inventLocationId = "";
        String productDeliveryType = getProductDeliveryType(manualSaleOrderReturn);
        if (isLocalFittingRequired) {
            if (manualSaleOrderReturn.getCountry().equalsIgnoreCase("IN")) {
                inventLocationId = "DK02";
            } else if (manualSaleOrderReturn.getCountry().equalsIgnoreCase("SG")) {
                inventLocationId = "SGDK01";
            }

        } else {
            if(Constants.Common.DTC.equalsIgnoreCase(productDeliveryType)){
                inventLocationId = String.format("%s%s", facilityCode, hubMasterService.getHubMasterFacilities().contains(facilityCode) ? INTRANSIT : "");
            }else{
                inventLocationId = facilityCode;
            }
        }
        return inventLocationId;
    }

    public Boolean getIsLocalFittingRequired(String facility) {
        return Constants.FACILITY_CODES.LOCAL_FITTING_REQUIRED.equalsIgnoreCase(facility);
    }

    public String getFacilityCode(ManualSaleOrderReturnDto manualSaleOrderReturnDto) {
        String facility = manualSaleOrderReturnDto.getFacility();
        boolean isLocalFittingRequired = getIsLocalFittingRequired(facility);
        if(Constants.FACILITY_CODES.LENSKART_FACILITY.equalsIgnoreCase(facility)){
            return Constants.FACILITY_CODES.DK02;
        }else if(isLocalFittingRequired){
            if (manualSaleOrderReturnDto.getCountry().equalsIgnoreCase("IN")) {
                return Constants.FACILITY_CODES.DK02;
            } else if (manualSaleOrderReturnDto.getCountry().equalsIgnoreCase("SG")) {
                return Constants.FACILITY_CODES.SGDK01;
            }
        }else{
            Map<String, Object> franchiseMasterMap = franchiseMasterReadRepository.getFranchiseMaster(facility);
            if(!CollectionUtils.isEmpty(franchiseMasterMap) && franchiseMasterMap.get("facility_code") != null){
                facility = franchiseMasterMap.get("facility_code").toString();
            }
        }
        return facility;
    }

    public String getFulfillmentWH(ManualSaleOrderReturnDto manualSaleOrderReturnDto){
        String productDeliveryType = getProductDeliveryType(manualSaleOrderReturnDto);
        String facility = getFacilityCode(manualSaleOrderReturnDto);
        String fulFillmentWH = facility;
        if(Constants.Common.B2B.equalsIgnoreCase(productDeliveryType) || Constants.Common.BULK.equalsIgnoreCase(productDeliveryType) || Constants.Common.OTC.equalsIgnoreCase(productDeliveryType)){
            if (isPhysicalFacility(facility)) {
                fulFillmentWH = facility+INTRANSIT;
            }
        }
        if(Constants.Common.DTC.equalsIgnoreCase(productDeliveryType)){
            if (isPhysicalFacility(facility)) {
                fulFillmentWH = facility+INTRANSIT;
            }
        }
        log.info("[getFulfillmentWH] magentoId : {}, fulfillmentWH : {}", manualSaleOrderReturnDto.getMagentoItemId(), fulFillmentWH);
        return fulFillmentWH;
    }

    public boolean isPhysicalFacility(String facility) {
        return hubMasterService.getHubMasterFacilities().contains(facility);
    }

    public boolean isNexsUwOrder(ManualSaleOrderReturnDto manualSaleOrderReturnDto) {
        String facility = getFacilityCode(manualSaleOrderReturnDto);
        try {
            if (StringUtils.isNotBlank(facility) && !CollectionUtils.isEmpty(nexsFacilities) && nexsFacilities.contains(facility)) {
                return true;
            }
        }
        catch (Exception e){
            log.error("[isNexsUwOrder] exception "+e);
        }
        return false;
    }

    public String getLkReferenceWhForReturn(ManualSaleOrderReturnDto manualSaleOrderReturnDto) {
        String inventLocationId = "";
        String courier = "Courier";
        String direct = "Direct";
        String productDeliveryType = getProductDeliveryType(manualSaleOrderReturnDto);
        String facilityCode = getFacilityCode(manualSaleOrderReturnDto);

        if(isNexsUwOrder(manualSaleOrderReturnDto)){
            courier = "BH-Courier";
            direct = "BH-Direct";
        }

        try {
            if (Constants.Common.B2B.equalsIgnoreCase(productDeliveryType) && manualSaleOrderReturnDto.getNavChannel().equalsIgnoreCase("WebB2B")) {
                inventLocationId = facilityCode;
            } else {
                if (manualSaleOrderReturnDto.getNavChannel().contains("FOFO")) {
                    if (manualSaleOrderReturnDto.getCountry().equalsIgnoreCase("IN")) {
                        inventLocationId = direct;
                    } else if (intCountries.contains(manualSaleOrderReturnDto.getCountry())) {
                        inventLocationId = facilityCode;
                    }
                } else if (!StringUtils.isEmpty(manualSaleOrderReturnDto.getRtoDate())) {
                    if (manualSaleOrderReturnDto.getCountry().equalsIgnoreCase("IN")) {
                        inventLocationId = direct;
                    } else if (intCountries.contains(manualSaleOrderReturnDto.getCountry())) {
                        inventLocationId = facilityCode;
                    }
                }
            }

        } catch (Exception e) {
            log.error("ReturnUtil : Exception found to set InventLocationId for uwOrder : {} : ", manualSaleOrderReturnDto.getMagentoItemId(), e);
        }
        log.info("ReturnUtil : InventLocationId for uwOrder : {} : {}", manualSaleOrderReturnDto.getMagentoItemId(), inventLocationId);
        return inventLocationId;
    }

    public boolean getIsLoyaltyService(String productId) {
        Product product = null;
        List<String> loyaltyServices = null;
        try {
            product = productService.getProduct(Integer.valueOf(productId));
            loyaltyServices = systemPreferenceService.getValuesAsList("lk_3orfree_service", "lk_cash_loyalty_services");
        } catch (Exception e) {
            log.error("ReturnUtils : exception found to get loyaltyServices for {} ", e.getMessage());
        }
        if (loyaltyServices != null) {
            return loyaltyServices.contains(String.valueOf(product.getClassification()));
        }
        return false;
    }

    public Boolean getReturnV2RetryFlag() {
        try {
            String pidRetryFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.RETURN_V2_RETRY_FLAG, Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN);
            if (null != pidRetryFlag && pidRetryFlag.equalsIgnoreCase("1")) {
                return true;
            }
        }
        catch(Exception e){
            log.error("[getPSlipRetryFlag] exception {} {}",e.getMessage(),e);
        }
        return false;
    }

    public String getProductDeliveryType(String navChannel){
        String productDeliveryType = "";
        if(StringUtils.isNotBlank(navChannel)){
            if(navChannel.contains(Constants.Common.B2B)){
                productDeliveryType = Constants.Common.B2B;
            } else if (navChannel.contains(Constants.Common.DTC)) {
                productDeliveryType = Constants.Common.DTC;
            }else if(navChannel.contains(Constants.Common.OTC)){
                productDeliveryType = Constants.Common.OTC;
            }else if(navChannel.toLowerCase().contains("bulk")){
                productDeliveryType = Constants.Common.BULK;
            }
        }
        return productDeliveryType;
    }

    /*
    Validate the following:
        1) Nav Channel is not FOFOOTC
        2) LKCOUNTRY is IN (India)
        3) check SBRT flag from 3 sources
        Returns: true for valid return
     */
    public boolean shouldCreateReturn(ReturnCreateRequest returnCreateRequest) {
        try {
            log.info("shouldCreateReturn: validation return data {}", returnCreateRequest.getUwItemId());
            if (returnCreateRequest.getUwItemId() != null && returnCreateRequest.getUwItemId() != 0) {
                UwOrder uwOrder = uwOrdersRepository.findByUwItemId(returnCreateRequest.getUwItemId());
                if (uwOrder != null) {
                    log.info("navChannel : {}, returnId: {}", uwOrder.getNavChannel(),returnCreateRequest.getReturnId());
                    if (uwOrder.getNavChannel().equalsIgnoreCase(Constants.Common.FOFOOTC) ||
                            uwOrder.getNavChannel().equalsIgnoreCase(Constants.Common.HEC_BULK)) {
                        log.info("Could not create return as Nav channel is : {} for uwOrder : {}" , uwOrder.getNavChannel(), gson.toJson(uwOrder));
                        return false;
                    }

                    if (returnCreateRequest.getReturnId() != null && checkIsInsurance(returnCreateRequest.getReturnId()))
                    {
                        log.info("Could not create return as IsInsurance flag is set to true.");
                        return false;
                    }

                    OrdersHeader ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
                    if (!ordersHeader.getLkCountry().equalsIgnoreCase(Constants.Country.IN)) return false;

                    boolean sbrtFlag = getSBRTFlag(uwOrder); //fetch sbrt flag from juno
                    if (sbrtFlag &&
                            (uwOrder.getNavChannel().equalsIgnoreCase(NavChannel.COCOBulk.name())
                                    || uwOrder.getNavChannel().equalsIgnoreCase(NavChannel.JJBulk.name()))
                    ) {
                        log.info("Not creating return for COCOBulk/JJBulk SBRT order {}", returnCreateRequest.getUwItemId());
                        return false;
                }

                returnCreateRequest.setSBRTEnabled(sbrtFlag);
                returnCreateRequest.setUwOrder(uwOrder);
            }
        }
            }
        catch (Exception e) {
            log.error("shouldCreateReturn: validation failed {}",e);
            return false;
        }

        return true;
    }

    public boolean shouldNotUpdateD365Tracking(Integer uwItemId) {
        try {
            log.info("[shouldNotUpdateD365] Entering with uwItemId: {}", uwItemId);
            if (uwItemId != null && uwItemId != 0) {
                UwOrder uwOrder = uwOrdersRepository.findByUwItemId(uwItemId);
                if (uwOrder != null) {
                    log.info("[shouldNotUpdateD365] navChannel : {}", uwOrder.getNavChannel());
                    if (uwOrder.getNavChannel().equalsIgnoreCase(NavChannel.BRWEBDTC.name())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("[shouldNotUpdateD365] validation failed: ", e);
        }
        return false;
    }

    private boolean checkIsInsurance(Integer returnId){
        try {
            ReturnDetailsDTO returnDetails = getReturns("RETURN_ID", String.valueOf(returnId));
            ReturnOrder returnOrder = getReturnOrder(returnDetails);
            log.info("[checkIsInsurance] returnOrder : {}", gson.toJson(returnOrder));
            return returnOrder != null && returnOrder.getIsInsurance();
        } catch (Exception e) {
            log.error("Error checking Insurance Status for returnId : {}, Error : {} ",returnId, e);
        }
        return false;
    }

    public String getUnitPrice(UwOrder uwOrder) {
        String unitPrice = null;
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("barcode_list", Arrays.asList(uwOrder.getBarcode()));
        bodyMap.put("facility_code", uwOrder.getFacilityCode());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Object> requestEntity = new HttpEntity<>(bodyMap, headers);
        String url = this.nexsBaseUrl+"nexs/api/grn/v1/get/barcode/price";
        log.info("[ReturnUtil][getUnitPrice] Calling url " + url + " ,request = "+ requestEntity );
        try{
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().add(new StringHttpMessageConverter());
            ResponseEntity<String>  responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            log.info("[ReturnUtil][getUnitPrice] order: {} , response : {} ", uwOrder.getIncrementId(), responseEntity);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                List<Map<String, Object>> resultMap = objectMapper.readValue(responseEntity.getBody(), new TypeReference<List<Map<String, Object>>>() {});
                if(resultMap != null && !CollectionUtils.isEmpty(resultMap)){
                    unitPrice = resultMap.get(0).get("price") != null ? resultMap.get(0).get("price").toString() : "0";
                    if ("0".equalsIgnoreCase(unitPrice) && isManesarFacility(uwOrder.getFacilityCode())) {
                        unitPrice =  getUnitPriceWithoutTax(uwOrder);
                    }
                    if("0".equalsIgnoreCase(unitPrice)){
                        return getDefaultCostPrice(uwOrder);
                    }
                } else if (isManesarFacility(uwOrder.getFacilityCode())) {
                    unitPrice = getUnitPriceWithoutTax(uwOrder);
                } else {
                    return getDefaultCostPrice(uwOrder);
                }
            }else{
                return getDefaultCostPrice(uwOrder);
            }
            log.info("[ReturnUtil][getUnitPrice] order: {} , unitPrice : {} ", uwOrder.getIncrementId(), unitPrice);
        }catch (Exception e){
            log.error("[ReturnUtil][getUnitPrice] order : {}, error : {}", uwOrder.getIncrementId(), e);
            throw new TransferJournalException("/get/barcode/price api threw exception, cannot proceed for uwItemId : "+uwOrder.getUwItemId());
        }
        return unitPrice;
    }

    private String getUnitPriceWithoutTax(UwOrder uwOrder) {
        log.info("[ReturnUtil][getUnitPrice] checking unitPriceWithoutTax from UniReportStockEntry for manesar_facilities : {} ", MANESAR_FACILITY_CODE);
        UniReportStockEntry uniReportStockEntry = uniReportStockEntryRepository.findByItemCodeAndFacilityCodeIn(uwOrder.getBarcode(),MANESAR_FACILITY_CODE);
        log.info("[ReturnUtil][getUnitPrice] uniReportStockEntry : {}",gson.toJson(uniReportStockEntry));
        return uniReportStockEntry != null && uniReportStockEntry.getUnitPriceWithoutTax() != null ? uniReportStockEntry.getUnitPriceWithoutTax() : "0";
    }

    private String getDefaultCostPrice(UwOrder uwOrder) throws Exception {
        if (defaultCostPriceFlag) {
            log.info("[ReturnUtil][getDefaultCostPrice] using default cost price : {}", defaultCostPrice);
            return defaultCostPrice;
        }

        log.error("[ReturnUtil][getUnitPrice] order : {}, error : {}", uwOrder.getIncrementId(), "/get/barcode/price api didn't give result");
        throw new Exception("/get/barcode/price api didn't give result");
    }

    private boolean isManesarFacility(String facilityCode) {
        log.info("[ReturnUtil][isManesarFacility] checking if facilityCode is among manesar_facilities code : {} ", MANESAR_FACILITY_CODE);
        return MANESAR_FACILITY_CODE.contains(facilityCode);
    }

    public UwOrder cloneUwOrders(UwOrder uwOrder) {
        UwOrder newUwOrder =  new UwOrder();
        newUwOrder.setNavChannel(uwOrder.getNavChannel());
        newUwOrder.setProductDeliveryType(uwOrder.getProductDeliveryType());
        newUwOrder.setUwItemId(uwOrder.getUwItemId());
        newUwOrder.setBarcode(uwOrder.getBarcode());
        newUwOrder.setFacilityCode(uwOrder.getFacilityCode());
        newUwOrder.setB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
        newUwOrder.setIncrementId(uwOrder.getIncrementId());
        newUwOrder.setCreatedAt(uwOrder.getCreatedAt());
        return newUwOrder;
    }

    public void setSbrtUwOrderDetails(UwOrder uwOrder, boolean isSbrt) {
        uwOrder.setProductDeliveryType(
                getSbrtProductDeliveryType(uwOrder.getProductDeliveryType(),uwOrder.getNavChannel(), isSbrt)
        );
        uwOrder.setNavChannel(
                getSbrtNavChannel(uwOrder.getNavChannel(), isSbrt)
        );
    }

    public String getSbrtProductDeliveryType(String deliveryType, String navChannel, boolean sbrtFlag) {
        SbrtOrderMapper mapper = SbrtOrderMapper.getMapping(navChannel);
        if (mapper != null && sbrtFlag) {
            return mapper.getSbrtDeliveryType();
        }

        return deliveryType;
    }

    public String getSbrtNavChannel(String navChannel, boolean sbrtFlag) {
        SbrtOrderMapper mapper = SbrtOrderMapper.getMapping(navChannel);
        if (mapper != null && sbrtFlag) {
            return mapper.getSbrtNavChannel();
        }

        return navChannel;
    }

    public boolean getSBRTFlag(UwOrder uwOrder) {
        log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}", uwOrder.getUwItemId());
        if (uwOrder.getNavChannel().equalsIgnoreCase(NavChannel.COCOBulk.name())
                || uwOrder.getNavChannel().equalsIgnoreCase(NavChannel.COCOB2B.name())) {
            log.info("[ReturnUtil][getSBRTFlag] Found COCOBulk/COCOB2B Order, setting as SBRT : {}", uwOrder.getUwItemId());
            return true;
        }
        else if (uwOrder.getNavChannel().toLowerCase().startsWith("fofo")) {
            log.info("[ReturnUtil][getSBRTFlag] Found FOFO Order, setting as non SBRT : {}", uwOrder.getUwItemId());
            return false;
        }
        try {
            if (isSBRTOrder(uwOrder)) return true;
            log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, Not a SBRT enabled order", uwOrder.getUwItemId());

            if (uwOrder.getNavChannel().startsWith(Constants.Common.WEB_PREFIX)) {
                if(
                        Boolean.parseBoolean(systemPreferenceService.getSystemPreferenceValues(
                                Constants.SYSTEM_PREFERENCE_KEYS.IS_SBRT_WEB_ENABLED,
                                Constants.SYSTEM_PREFERENCE_GROUPS.SBRT)
                        )
                ) return true;
            }
            else {

                if(getStoreSBRTStatus(uwOrder)) return true;
            }

            log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, Not a web or store sbrt enabled order", uwOrder.getUwItemId());
        }
        catch (Exception e) {
            log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, error : {}", uwOrder.getUwItemId(), e);
        }

        log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, isSbrt : {}", uwOrder.getUwItemId(), false);
        return false;
    }

    public boolean getStoreSBRTStatus(UwOrder uwOrder) {
        log.info("[ReturnUtil][getStoreSBRTStatus] uwItemId : {}", uwOrder.getUwItemId());
        String facilityCode = uwOrder.getFacilityCode();
        if (uwOrder.getNavChannel().toLowerCase().contains("bulk") || uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.DTC)) {
            OrdersHeader ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
            if (ordersHeader != null) {
                facilityCode = ordersHeader.getFacilityCode();
            }
        }
        PosFranchiseSbrtResponse posFranchiseSbrtResponse = getStoreSbrtStatus(facilityCode);
        log.info("[ReturnUtil][getSBRTFlag] uwItemId : {}, isSBRT: {}", uwOrder.getUwItemId(), posFranchiseSbrtResponse != null && posFranchiseSbrtResponse.isSbrtStore());
        return posFranchiseSbrtResponse != null && posFranchiseSbrtResponse.isSbrtStore();
    }

    public boolean isSBRTOrder(UwOrder uwOrder) {
        log.info("[ReturnUtil][isSBRTOrder] checking uwItemId in sbrt table : {}", uwOrder.getUwItemId());
        SbrtOrderItem sbrtOrderItem = sbrtOrderItemRepository.findByUwItemId(uwOrder.getUwItemId());
        return sbrtOrderItem != null;
    }

    public ReturnDetailsDTO getReturns(String identifierType, String identifierValue){
        log.info("[getReturns] identifierType : {} , identifierValue : {}", identifierType, identifierValue);
        ReturnDetailsDTO returnDetailsDTO = null;
        try{
            GetReturnDetailsRequest returnDetailsRequest = new GetReturnDetailsRequest();
            returnDetailsRequest.setIdentifierType(identifierType);
            returnDetailsRequest.setIdentifierValues(Collections.singletonList(identifierValue));
            String url = returnBaseUrl + "return/details/v1.0/get";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> httpEntity = new HttpEntity<>(returnDetailsRequest, headers);
            ResponseEntity<ReturnDetailsDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, ReturnDetailsDTO.class);
            log.info("[getReturns] identifierValue : {}, response : {}", identifierValue, responseEntity);
            returnDetailsDTO = responseEntity.getBody();
        }catch (Exception exception){
            log.error("[getReturns] identifierValue : + " + identifierValue+ " exception while fetching returns : "+exception);
        }
        log.info("[getReturns] identifierValue : {}, returnDetailsDTO : {}", identifierValue, returnDetailsDTO);
        return returnDetailsDTO;
    }

    public List<ReturnHistoryDTO> getReturnHistory(Integer returnId){
        log.info("[getReturnHistory] returnId : {}, getting from headless returns", returnId);
        List<ReturnHistoryDTO> returnHistoryDTOList = null;
        try{
            String url = returnBaseUrl + "return/details/v1.0/get/return-history/" + returnId;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
            log.info("[getReturnHistory] url : {}", url);
            ResponseEntity<List<ReturnHistoryDTO>> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<List<ReturnHistoryDTO>>() {}
            );
            log.info("[getReturnHistory] responseEntity : {}", responseEntity);
            returnHistoryDTOList = responseEntity.getBody();
        }catch (Exception exception){
            log.error("[getReturnHistory] returnId : + " + returnId+ " exception while fetching returns : "+exception);
        }
        log.info("[getReturnHistory] returnId : {}, returnDetailsDTO : {}", returnId, returnHistoryDTOList);
        return returnHistoryDTOList;
    }

    public ReturnOrder getReturnOrder(ReturnDetailsDTO returnDetailsDTO){
        log.info("[getReturnOrder] enter returnDetailsDTO : {}", returnDetailsDTO);
        ReturnOrder returnOrder = null;
        if(returnDetailsDTO != null && !CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrders())){
            ReturnOrderDTO returnOrderDTO = returnDetailsDTO.getReturnOrders().get(0);
            log.info("[getReturnOrder] returnOrderDTO : {}",returnOrderDTO);
            if(returnOrderDTO != null){
                returnOrder = new ReturnOrder();
                returnOrder.setOrderNo(returnOrderDTO.getIncrementId());
                returnOrder.setReturnId(returnOrderDTO.getId());
                returnOrder.setGroupId(Long.valueOf(returnOrderDTO.getGroupId()));
                returnOrder.setFacilityCode(returnOrderDTO.getFacilityCode());
                returnOrder.setReturnType(returnOrderDTO.getReturnType());
                returnOrder.setReturnMethod(returnOrderDTO.getReturnMethod());
                returnOrder.setIsInsurance(returnOrderDTO.getIsInsurance());
                returnOrder.setUnicomOrderCode(returnOrderDTO.getUnicomOrderCode());
                returnOrder.setReturnCreateDatetime(returnOrderDTO.getReturnCreateDatetime());
            }
        }
        log.info("[getReturnOrder] returnOrder : {}", returnOrder);
        return returnOrder;
    }

    public ReturnOrderItem getReturnOrderItem(ReturnDetailsDTO returnDetailsDTO){
        log.info("[getReturnOrderItem] enter returnDetailsDTO : {}", returnDetailsDTO);
        ReturnOrderItem returnOrderItem = null;
        if(returnDetailsDTO != null && !CollectionUtils.isEmpty(returnDetailsDTO.getReturnOrders())){
            ReturnOrderItemDTO returnOrderItemDTO = returnDetailsDTO.getReturnOrderItems().get(0);
            log.info("[getReturnOrderItem] returnOrderItemDTO : {}",returnOrderItemDTO);
            if(returnOrderItemDTO != null){
                returnOrderItem = new ReturnOrderItem();
                returnOrderItem.setReturnId(returnOrderItemDTO.getReturnId());
                returnOrderItem.setProductId(Math.toIntExact(returnOrderItemDTO.getProductId()));
                returnOrderItem.setItemId(returnOrderItemDTO.getItemId());
                returnOrderItem.setReturnCreateDatetime(returnOrderItemDTO.getReturnCreateDatetime());
                returnOrderItem.setQcStatus(returnOrderItemDTO.getQcStatus());
                returnOrderItem.setUwItemId(returnOrderItemDTO.getUwItemId());
                returnOrderItem.setQcComment(returnOrderItemDTO.getQcComment());
            }
        }
        log.info("[getReturnOrderItem] returnOrderItem : {}", returnOrderItem);
        return returnOrderItem;
    }

    public ReturnReasonTableDTO getReturnReasons(Integer uwItemId){
        log.info("[getReturns] uwItemId : {}", uwItemId);
        ReturnReasonTableDTO returnReasonTableDTO = null;
        try{
            List<Integer> uwItemIdList = Collections.singletonList(uwItemId);
            String url = returnBaseUrl + "return/details/v1.0/get/return-reason/";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> httpEntity = new HttpEntity<>(uwItemIdList, headers);
            ResponseEntity<List> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, List.class);
            log.info("[getReturns] uwItemId : {}, response : {}", uwItemId, responseEntity);
            returnReasonTableDTO = getReturnReason(responseEntity);
        }catch (Exception exception){
            log.error("[getReturns] uwItemId : + " + uwItemId+ " exception while fetching returns : "+exception);
        }
        log.info("[getReturns] uwItemId : {}, returnDetailsDTO : {}", uwItemId, returnReasonTableDTO);
        return returnReasonTableDTO;
    }

    private ReturnReasonTableDTO getReturnReason(ResponseEntity<List> responseEntity) {
        ReturnReasonTableDTO returnReasonTableDTO = null;
        if(responseEntity.getStatusCode().is2xxSuccessful() && !CollectionUtils.isEmpty(responseEntity.getBody())){
            Map<String, Object> returnReasonMap = (Map<String, Object>)responseEntity.getBody().get(0);
            returnReasonTableDTO = new ReturnReasonTableDTO();
            returnReasonTableDTO.setPrimaryReason(returnReasonMap.get("primaryReason").toString());
            returnReasonTableDTO.setPrimaryReason(returnReasonMap.get("secondaryReason").toString());
        }
        return returnReasonTableDTO;
    }

    public ReturnOrderAddressUpdate getReturnOrderAddress(Integer incrementId){
        log.info("[getReturnOrderAddress] incrementId : {}", incrementId);
        ReturnOrderAddressUpdate returnOrderAddressUpdate = null;
        try{
            String url = returnBaseUrl + "return/details/v1.0/return-address-update/"+ incrementId;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<ReturnDetailAddressUpdateDTO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, ReturnDetailAddressUpdateDTO.class);
            log.info("[getReturnOrderAddress] incrementId : {}, response : {}", incrementId, responseEntity);
            returnOrderAddressUpdate = getReturnOrderAddressUpdateDTO(responseEntity);
        }catch (Exception exception){
            log.error("[getReturnOrderAddress] incrementId : + " + incrementId+ " exception while fetching returns : "+exception);
        }
        log.info("[getReturnOrderAddress] incrementId : {}, returnDetailsDTO : {}", incrementId, returnOrderAddressUpdate);
        return returnOrderAddressUpdate;
    }

    private ReturnOrderAddressUpdate getReturnOrderAddressUpdateDTO(ResponseEntity<ReturnDetailAddressUpdateDTO> responseEntity) {
        log.info("[getReturnOrderAddressUpdateDTO] responseEntity : {}", responseEntity);
        ReturnOrderAddressUpdate returnOrderAddressUpdate = null;
        if(responseEntity.getStatusCode().is2xxSuccessful()){
            ReturnDetailAddressUpdateDTO returnDetailAddressUpdateDTO = responseEntity.getBody();
            log.info("[getReturnOrderAddressUpdateDTO] returnDetailAddressUpdateDTO : {}", returnDetailAddressUpdateDTO);
            if(returnDetailAddressUpdateDTO != null){
                returnOrderAddressUpdate = new ReturnOrderAddressUpdate();
                returnOrderAddressUpdate.setFirstName(returnDetailAddressUpdateDTO.getFirstName());
                returnOrderAddressUpdate.setLastName(returnDetailAddressUpdateDTO.getLastName());
                returnOrderAddressUpdate.setCity(returnDetailAddressUpdateDTO.getCity());
                returnOrderAddressUpdate.setState(returnDetailAddressUpdateDTO.getState());
                returnOrderAddressUpdate.setCountry(returnDetailAddressUpdateDTO.getCountry());
                returnOrderAddressUpdate.setEmail(returnDetailAddressUpdateDTO.getEmail());
                returnOrderAddressUpdate.setCreatedAt(returnDetailAddressUpdateDTO.getCreatedAt());
                returnOrderAddressUpdate.setGroupId(returnDetailAddressUpdateDTO.getGroupId());
                returnOrderAddressUpdate.setIncrement_id(returnDetailAddressUpdateDTO.getIncrement_id());
                returnOrderAddressUpdate.setPostcode(returnDetailAddressUpdateDTO.getPostcode());
                returnOrderAddressUpdate.setStreet1(returnDetailAddressUpdateDTO.getStreet1());
                returnOrderAddressUpdate.setStreet2(returnDetailAddressUpdateDTO.getStreet2());
                returnOrderAddressUpdate.setTelephone(returnDetailAddressUpdateDTO.getTelephone());
            }
        }
        log.info("[getReturnOrderAddressUpdateDTO] returnOrderAddressUpdate : {}", returnOrderAddressUpdate);
        return returnOrderAddressUpdate;
    }
}