package com.lenskart.financeConsumer.util;

import com.lenskart.financeConsumer.model.unireports.UniReportD365SyncDetails;
import com.lenskart.financeConsumer.unireports.readOnlyRepository.UniReportD365SyncDetailsReadRepository;
import com.lenskart.financeConsumer.unireports.writeRepository.UnireportSaleOrderSyncRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
public class ManualForwardFlowUtil {

    @Autowired
    private UniReportD365SyncDetailsReadRepository uniReportD365SyncDetailsReadRepository;

    @Autowired
    private UnireportSaleOrderSyncRepository unireportSaleOrderSyncRepository;

    public void updateResponseInUnireportsDB(String payloadType, String saleOrderNumber, String warehouse, String responseStatus, String responseMessage) {
        if (Objects.nonNull(responseMessage)) {
            responseMessage = responseMessage.substring(0, Math.min(responseMessage.length(), 255));
        } else {
            responseMessage = Constants.POSD365SyncStatus.Empty_response;
        }
        UniReportD365SyncDetails uniReportD365SyncDetails = uniReportD365SyncDetailsReadRepository.findByIdentifierAndPayloadType(saleOrderNumber, payloadType);
        try {
            if (Objects.isNull(uniReportD365SyncDetails)) {
                insertUniReportD365SyncDetails(payloadType, saleOrderNumber, warehouse, responseStatus, responseMessage);
            } else {
                updateUniReportD365SyncDetails(uniReportD365SyncDetails, saleOrderNumber, responseStatus, responseMessage);
            }
        } catch (Exception e) {
            log.error("Exception occured while checking if saleOrder entry alreadyExists for saleOrder {} , Error Message: {}",
                    saleOrderNumber, e.getMessage());
        }
    }

    public void insertUniReportD365SyncDetails(String payloadType, String saleOrderNumber, String warehouse, String responseStatus, String responseMessage) {
        try {
            UniReportD365SyncDetails uniReportD365SyncDetails = new UniReportD365SyncDetails();
            uniReportD365SyncDetails.setFacilityCode(warehouse);
            uniReportD365SyncDetails.setD365Status(responseStatus);
            uniReportD365SyncDetails.setIdentifier(saleOrderNumber);
            uniReportD365SyncDetails.setPayloadType(payloadType);
            uniReportD365SyncDetails.setRetryCount(0);
            uniReportD365SyncDetails.setReason(responseMessage);
            uniReportD365SyncDetails.setSyncedAt(new Date());
            uniReportD365SyncDetails.setCreatedAt(new Date());
            uniReportD365SyncDetailsReadRepository.save(uniReportD365SyncDetails);
        } catch (Exception e) {
            log.error("Exception occurred while inserting D365 response to uniReport_d365_sync_details table for "
                            + "identifier :{}, payload_type :\"SO\",  d365_status :{}, status :{}, response :{}, Exception Message: {}",
                    saleOrderNumber, warehouse, responseStatus, responseMessage, e
            );
        }
    }

    public void updateUniReportD365SyncDetails(UniReportD365SyncDetails uniReportD365SyncDetails, String saleOrderNumber, String responseStatus, String responseMessage) {
        try {
            uniReportD365SyncDetails.setSyncedAt(new Date());
            uniReportD365SyncDetails.setReason(responseMessage);
            uniReportD365SyncDetails.setD365Status(responseStatus);
            uniReportD365SyncDetails.setRetryCount(uniReportD365SyncDetails.getRetryCount() + 1);
            uniReportD365SyncDetailsReadRepository.save(uniReportD365SyncDetails);
        } catch (Exception e) {
            log.error("Exception occurred while inserting D365 response to uniReport_d365_sync_details table for "
                            + "identifier :{}, payload_type :\"SO\",  d365_status :{}, status :{}, response :{}, Exception Message: {}",
                    saleOrderNumber, responseStatus, responseMessage, e
            );
        }
    }
}
