package com.lenskart.financeConsumer.util;

import com.lenskart.financeConsumer.dto.d365requests.ItemMasterDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;

import java.util.Map;

@Slf4j
@Component
public class ProductUtil {

    public static  Map<String, String> CountryCurrencyMapping;
   public static  Map<String,String> StateMapping;

   public static Map<String,String> CountryMapping;

    @PostConstruct
    public void init(){
        setCountryCurrencyMappings();
        setStateMapping();
        log.info("country currency,State code successfull");
    }

    public static void setFinancialDimensions(ItemMasterDTO payload,Integer classificationId, String classificationDisplayName, String facilityCode)throws Exception {
        try {

            payload.setFinancialDimensionBrand(payload.getBrand());
            payload.setItemClassification(getClassificationDisplayName(classificationId,classificationDisplayName));
            log.info("[ItemMasterServiceImpl][setFinancialDimensions] payload "+ payload);
        }
        catch(Exception e){
            log.error(e.getMessage());
            throw e;
        }
    }

    public static String getClassificationDisplayName(Integer classificationId,String classificationDisplayName){
        if(classificationId==21567){
            classificationDisplayName = "Sunglasses";
        }
        else if(classificationId==19429){
            classificationDisplayName = "Contact Lens";
        }
        return classificationDisplayName;
    }

    public static void setCountryCurrencyMappings() {
        CountryCurrencyMapping = new HashMap<String, String>();
        CountryCurrencyMapping.put("IN","INR");
        CountryCurrencyMapping.put("SG","SGD");
        CountryCurrencyMapping.put("US","USD");
        CountryCurrencyMapping.put("AE","AED");
        CountryCurrencyMapping.put("VN","VND");
        CountryCurrencyMapping.put("MY","MYR");
        CountryCurrencyMapping.put("ID","IDR");
        CountryCurrencyMapping.put("ID","IDR");
    }

    public static void setCountryMapping(){
        CountryMapping = new HashMap<String, String>();
        CountryMapping.put("AF","AFG");
        CountryMapping.put("AL","ALB");
        CountryMapping.put("DZ","DZA");
        CountryMapping.put("AS","ASM");
        CountryMapping.put("AD","AND");
        CountryMapping.put("AO","AGO");
        CountryMapping.put("AI","AIA");
        CountryMapping.put("AQ","ATA");
        CountryMapping.put("AG","ATG");
        CountryMapping.put("AR","ARG");
        CountryMapping.put("AM","ARM");
        CountryMapping.put("AW","ABW");
        CountryMapping.put("AU","AUS");
        CountryMapping.put("AT","AUT");
        CountryMapping.put("AZ","AZE");
        CountryMapping.put("BS","BHS");
        CountryMapping.put("BH","BHR");
        CountryMapping.put("BD","BGD");
        CountryMapping.put("BB","BRB");
        CountryMapping.put("BY","BLR");
        CountryMapping.put("BE","BEL");
        CountryMapping.put("BZ","BLZ");
        CountryMapping.put("BJ","BEN");
        CountryMapping.put("BM","BMU");
        CountryMapping.put("BT","BTN");
        CountryMapping.put("BO","BOL");
        CountryMapping.put("BA","BIH");
        CountryMapping.put("BW","BWA");
        CountryMapping.put("BR","BRA");
        CountryMapping.put("IO","IOT");
        CountryMapping.put("VG","VGB");
        CountryMapping.put("BN","BRN");
        CountryMapping.put("BG","BGR");
        CountryMapping.put("BF","BFA");
        CountryMapping.put("BI","BDI");
        CountryMapping.put("KH","KHM");
        CountryMapping.put("CM","CMR");
        CountryMapping.put("CA","CAN");
        CountryMapping.put("CV","CPV");
        CountryMapping.put("KY","CYM");
        CountryMapping.put("CF","CAF");
        CountryMapping.put("TD","TCD");
        CountryMapping.put("CL","CHL");
        CountryMapping.put("CN","CHN");
        CountryMapping.put("CX","CXR");
        CountryMapping.put("CC","CCK");
        CountryMapping.put("CO","COL");
        CountryMapping.put("KM","COM");
        CountryMapping.put("CK","COK");
        CountryMapping.put("CR","CRI");
        CountryMapping.put("HR","HRV");
        CountryMapping.put("CU","CUB");
        CountryMapping.put("CW","CUW");
        CountryMapping.put("CY","CYP");
        CountryMapping.put("CZ","CZE");
        CountryMapping.put("CD","COD");
        CountryMapping.put("DK","DNK");
        CountryMapping.put("DJ","DJI");
        CountryMapping.put("DM","DMA");
        CountryMapping.put("DO","DOM");
        CountryMapping.put("TL","TLS");
        CountryMapping.put("EC","ECU");
        CountryMapping.put("EG","EGY");
        CountryMapping.put("SV","SLV");
        CountryMapping.put("GQ","GNQ");
        CountryMapping.put("ER","ERI");
        CountryMapping.put("EE","EST");
        CountryMapping.put("ET","ETH");
        CountryMapping.put("FK","FLK");
        CountryMapping.put("FO","FRO");
        CountryMapping.put("FJ","FJI");
        CountryMapping.put("FI","FIN");
        CountryMapping.put("FR","FRA");
        CountryMapping.put("PF","PYF");
        CountryMapping.put("GA","GAB");
        CountryMapping.put("GM","GMB");
        CountryMapping.put("GE","GEO");
        CountryMapping.put("DE","DEU");
        CountryMapping.put("GH","GHA");
        CountryMapping.put("GI","GIB");
        CountryMapping.put("GR","GRC");
        CountryMapping.put("GL","GRL");
        CountryMapping.put("GD","GRD");
        CountryMapping.put("GU","GUM");
        CountryMapping.put("GT","GTM");
        CountryMapping.put("GG","GGY");
        CountryMapping.put("GN","GIN");
        CountryMapping.put("GW","GNB");
        CountryMapping.put("GY","GUY");
        CountryMapping.put("HT","HTI");
        CountryMapping.put("HN","HND");
        CountryMapping.put("HK","HKG");
        CountryMapping.put("HU","HUN");
        CountryMapping.put("IS","ISL");
        CountryMapping.put("IN","IND");
        CountryMapping.put("ID","IDN");
        CountryMapping.put("IR","IRN");
        CountryMapping.put("IQ","IRQ");
        CountryMapping.put("IE","IRL");
        CountryMapping.put("IM","IMN");
        CountryMapping.put("IL","ISR");
        CountryMapping.put("IT","ITA");
        CountryMapping.put("CI","CIV");
        CountryMapping.put("JM","JAM");
        CountryMapping.put("JP","JPN");
        CountryMapping.put("JE","JEY");
        CountryMapping.put("JO","JOR");
        CountryMapping.put("KZ","KAZ");
        CountryMapping.put("KE","KEN");
        CountryMapping.put("KI","KIR");
        CountryMapping.put("XK","XKX");
        CountryMapping.put("KW","KWT");
        CountryMapping.put("KG","KGZ");
        CountryMapping.put("LA","LAO");
        CountryMapping.put("LV","LVA");
        CountryMapping.put("LB","LBN");
        CountryMapping.put("LS","LSO");
        CountryMapping.put("LR","LBR");
        CountryMapping.put("LY","LBY");
        CountryMapping.put("LI","LIE");
        CountryMapping.put("LT","LTU");
        CountryMapping.put("LU","LUX");
        CountryMapping.put("MO","MAC");
        CountryMapping.put("MK","MKD");
        CountryMapping.put("MG","MDG");
        CountryMapping.put("MW","MWI");
        CountryMapping.put("MY","MYS");
        CountryMapping.put("MV","MDV");
        CountryMapping.put("ML","MLI");
        CountryMapping.put("MT","MLT");
        CountryMapping.put("MH","MHL");
        CountryMapping.put("MR","MRT");
        CountryMapping.put("MU","MUS");
        CountryMapping.put("YT","MYT");
        CountryMapping.put("MX","MEX");
        CountryMapping.put("FM","FSM");
        CountryMapping.put("MD","MDA");
        CountryMapping.put("MC","MCO");
        CountryMapping.put("MN","MNG");
        CountryMapping.put("ME","MNE");
        CountryMapping.put("MS","MSR");
        CountryMapping.put("MA","MAR");
        CountryMapping.put("MZ","MOZ");
        CountryMapping.put("MM","MMR");
        CountryMapping.put("NA","NAM");
        CountryMapping.put("NR","NRU");
        CountryMapping.put("NP","NPL");
        CountryMapping.put("NL","NLD");
        CountryMapping.put("AN","ANT");
        CountryMapping.put("NC","NCL");
        CountryMapping.put("NZ","NZL");
        CountryMapping.put("NI","NIC");
        CountryMapping.put("NE","NER");
        CountryMapping.put("NG","NGA");
        CountryMapping.put("NU","NIU");
        CountryMapping.put("KP","PRK");
        CountryMapping.put("MP","MNP");
        CountryMapping.put("NO","NOR");
        CountryMapping.put("OM","OMN");
        CountryMapping.put("PK","PAK");
        CountryMapping.put("PW","PLW");
        CountryMapping.put("PS","PSE");
        CountryMapping.put("PA","PAN");
        CountryMapping.put("PG","PNG");
        CountryMapping.put("PY","PRY");
        CountryMapping.put("PE","PER");
        CountryMapping.put("PH","PHL");
        CountryMapping.put("PN","PCN");
        CountryMapping.put("PL","POL");
        CountryMapping.put("PT","PRT");
        CountryMapping.put("PR","PRI");
        CountryMapping.put("QA","QAT");
        CountryMapping.put("CG","COG");
        CountryMapping.put("RE","REU");
        CountryMapping.put("RO","ROU");
        CountryMapping.put("RU","RUS");
        CountryMapping.put("RW","RWA");
        CountryMapping.put("BL","BLM");
        CountryMapping.put("SH","SHN");
        CountryMapping.put("KN","KNA");
        CountryMapping.put("LC","LCA");
        CountryMapping.put("MF","MAF");
        CountryMapping.put("PM","SPM");
        CountryMapping.put("VC","VCT");
        CountryMapping.put("WS","WSM");
        CountryMapping.put("SM","SMR");
        CountryMapping.put("ST","STP");
        CountryMapping.put("SA","SAU");
        CountryMapping.put("SN","SEN");
        CountryMapping.put("RS","SRB");
        CountryMapping.put("SC","SYC");
        CountryMapping.put("SL","SLE");
        CountryMapping.put("SG","SGP");
        CountryMapping.put("SX","SXM");
        CountryMapping.put("SK","SVK");
        CountryMapping.put("SI","SVN");
        CountryMapping.put("SB","SLB");
        CountryMapping.put("SO","SOM");
        CountryMapping.put("ZA","ZAF");
        CountryMapping.put("KR","KOR");
        CountryMapping.put("SS","SSD");
        CountryMapping.put("ES","ESP");
        CountryMapping.put("LK","LKA");
        CountryMapping.put("SD","SDN");
        CountryMapping.put("SR","SUR");
        CountryMapping.put("SJ","SJM");
        CountryMapping.put("SZ","SWZ");
        CountryMapping.put("SE","SWE");
        CountryMapping.put("CH","CHE");
        CountryMapping.put("SY","SYR");
        CountryMapping.put("TW","TWN");
        CountryMapping.put("TJ","TJK");
        CountryMapping.put("TZ","TZA");
        CountryMapping.put("TH","THA");
        CountryMapping.put("TG","TGO");
        CountryMapping.put("TK","TKL");
        CountryMapping.put("TO","TON");
        CountryMapping.put("TT","TTO");
        CountryMapping.put("TN","TUN");
        CountryMapping.put("TR","TUR");
        CountryMapping.put("TM","TKM");
        CountryMapping.put("TC","TCA");
        CountryMapping.put("TV","TUV");
        CountryMapping.put("VI","VIR");
        CountryMapping.put("UG","UGA");
        CountryMapping.put("UA","UKR");
        CountryMapping.put("AE","ARE");
        CountryMapping.put("GB","GBR");
        CountryMapping.put("US","USA");
        CountryMapping.put("UY","URY");
        CountryMapping.put("UZ","UZB");
        CountryMapping.put("VU","VUT");
        CountryMapping.put("VA","VAT");
        CountryMapping.put("VE","VEN");
        CountryMapping.put("VN","VNM");
        CountryMapping.put("WF","WLF");
        CountryMapping.put("EH","ESH");
        CountryMapping.put("YE","YEM");
        CountryMapping.put("ZM","ZMB");
        CountryMapping.put("ZW","ZWE");
    }

    public static void setStateMapping(){
        StateMapping =new HashMap<String, String>();
        StateMapping.put("ANDAMANANDNICOBARISLANDS","AN");
        StateMapping.put("ANDHRAPRADESH","AP");
        StateMapping.put("ARUNACHALPRADESH","AR");
        StateMapping.put("ASSAM","AS");
        StateMapping.put("BIHAR","BH");
        StateMapping.put("CHATTISGARH","CT");
        StateMapping.put("CHANDIGARH","CH");
        StateMapping.put("DAMANANDDIU","DD");
        StateMapping.put("DELHI","DL");
        StateMapping.put("DADRAANDNAGARHAVELI","DN");
        StateMapping.put("GOA","GA");
        StateMapping.put("GUJARAT","GJ");
        StateMapping.put("HIMACHALPRADESH","HP");
        StateMapping.put("HARYANA","HR");
        StateMapping.put("JAMMUANDKASHMIR","JK");
        StateMapping.put("JHARKHAND","JH");
        StateMapping.put("KERALA","KL");
        StateMapping.put("KARNATAKA","KA");
        StateMapping.put("LAKSHADWEEP","LD");
        StateMapping.put("MEGHALAYA","ME");
        StateMapping.put("MAHARASHTRA","MH");
        StateMapping.put("MANIPUR","MN");
        StateMapping.put("MADHYA PRADESH","MP");
        StateMapping.put("MIZORAM","MI");
        StateMapping.put("NAGALAND","NL");
        StateMapping.put("ORISSA","OR");
        StateMapping.put("ODISHA","OR");
        StateMapping.put("PUNJAB","PB");
        StateMapping.put("PONDICHERRY","PY");
        StateMapping.put("RAJASTHAN","RJ");
        StateMapping.put("SIKKIM","SK");
        StateMapping.put("TAMILNADU","TN");
        StateMapping.put("TRIPURA","TR");
        StateMapping.put("TELANGANA","TS");
        StateMapping.put("UTTARAKHAND","UT");
        StateMapping.put("UTTARPRADESH","UP");
        StateMapping.put("WESTBENGAL","WB");
    }

    public static String getHSNCode(String code){
        int len=code.length();
        if(len>8){
            code= code.substring(0,9);
        }
        else{
            len=8-len;
            for(int i=0;i<len;i++){
                code = code+"0";
            }
        }
        return code;
    }

    public static String getCurrencyCode(String contryCode) {
        return CountryCurrencyMapping.get(contryCode);
    }




}
