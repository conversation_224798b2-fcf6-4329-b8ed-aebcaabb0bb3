package com.lenskart.financeConsumer.connector.impl;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.lenskart.financeConsumer.connector.VsmConnectorService;
import com.lenskart.financeConsumer.constant.Common;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.vsm.soap.api.markupdateshippingpackage.MarkUpdateShippingPackage;
import com.lenskart.vsm.soap.api.markupdateshippingpackage.MarkUpdateShippingPackageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.List;

@Slf4j
@Service
public class VsmConnectorServiceImpl implements VsmConnectorService {

    @Value("${vsm.web.wsdl.url}")
    private String vsmWsdlUrl ;

    @Value("${scm.fc.vsm.timeout}")
    private int vsmTimeout;

    private static String SOAP_ENVELOPE_BODY_PREFIX = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
            "    <soapenv:Body>\n";
    private static String SOAP_ENVELOPE_BODY_SUFFIX = "</soapenv:Body>\n" +
            "</soapenv:Envelope>";
    private static String XML_TAG = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>";
    private static String UPDATE_SHIPPING_PACKAGE_ID_RESPONSE = "MarkUpdateShippingPackageResponse";

    @Override
    public boolean updateShippingPackageIdOnVsm(String shippingPackageId, List<Integer> orderItemIds) throws Exception {
        StringBuilder st =new StringBuilder();
        orderItemIds.stream().forEach(Id->st.append(Id+","));
        MarkUpdateShippingPackage shippingPackageRequest =new MarkUpdateShippingPackage();
        shippingPackageRequest.setShippingPackageId(shippingPackageId);
        shippingPackageRequest.setUwItems(st.toString());

        MarkUpdateShippingPackageResponse markUpdateShippingPackageResponse = (MarkUpdateShippingPackageResponse) makeVsmCallWithRetry(convertRequestXml(shippingPackageRequest, Common.VSM_REQUEST_TYPE.SHIPPING_PACKAGE_REQUEST), Common.VSM_REQUEST_TYPE.SHIPPING_PACKAGE_REQUEST);
        if(markUpdateShippingPackageResponse!=null && markUpdateShippingPackageResponse.getReturn().getSuccessCode().equalsIgnoreCase("success")){
            log.error("[SCM][updateShippingPackageIdOnVsm] shippingPackageId:{} Success",shippingPackageId);
            return true;
        }else{
            log.error("[SCM][updateShippingPackageIdOnVsm] shippingPackageId:{}",shippingPackageId);
            throw new InvalidRequestException("SCM-VSM-SHIPMENT_UPDATE_FAILED","[SCM][updateShippingPackageIdOnVsm] " +
                    "shippingPackageId:"+shippingPackageId);
        }
    }



    @Retryable( value = Exception.class, maxAttemptsExpression = "${retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${retry.maxDelay}"))
    public Object makeVsmCallWithRetry(String request,String requestType) throws Exception {
        return  makeVsmCall(request,requestType);
    }

    private Object makeVsmCall(String request,String requestType) throws Exception {
        RestTemplate template =new RestTemplate();
        SimpleClientHttpRequestFactory rf = new SimpleClientHttpRequestFactory();
        rf.setReadTimeout(vsmTimeout);
        rf.setConnectTimeout(vsmTimeout);
        template.setRequestFactory(rf);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.TEXT_XML);
        HttpEntity<String> httpEntity = new HttpEntity(request, httpHeaders);
        ResponseEntity<String> responseEntity = template.exchange(vsmWsdlUrl, HttpMethod.POST, httpEntity, String.class, (Object) null);
        String response = responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null ? responseEntity.getBody() : null;
        return convertXmlResponse(response,requestType);
    }


    private  Object convertXmlResponse(String response,String requestType) throws XMLStreamException, IOException, XMLStreamException {
        XmlMapper xmlMapper = ObjectHelper.getXmlMapper();
        XMLInputFactory f = XMLInputFactory.newFactory();
        XMLStreamReader sr = f.createXMLStreamReader(new StringReader(response));
        while (sr.hasNext()) {
            int type = sr.next();
            if(requestType.equalsIgnoreCase(Common.VSM_REQUEST_TYPE.SHIPPING_PACKAGE_REQUEST)){

                if (type == XMLStreamReader.START_ELEMENT && UPDATE_SHIPPING_PACKAGE_ID_RESPONSE.equals(sr.getLocalName())) {
                    MarkUpdateShippingPackageResponse res = xmlMapper.readValue(sr, MarkUpdateShippingPackageResponse.class);
                    return res;
                }
            }
        }
        return null;
    }


    public static String convertRequestXml(Object source, String requestType) {
        String result;
        StringWriter sw = new StringWriter();
        try {
            JAXBContext context=null;
            if(requestType.equalsIgnoreCase(Common.VSM_REQUEST_TYPE.SHIPPING_PACKAGE_REQUEST))
                context= JAXBContext.newInstance(MarkUpdateShippingPackage.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.marshal(source, sw);
            result = sw.toString();
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }
        result = result.replace(XML_TAG, "");
        result = SOAP_ENVELOPE_BODY_PREFIX + result + SOAP_ENVELOPE_BODY_SUFFIX;
        return result;
    }
}
