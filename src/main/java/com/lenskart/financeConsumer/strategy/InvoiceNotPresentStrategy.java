package com.lenskart.financeConsumer.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.connector.InventoryAdaptorConnector;
import com.lenskart.financeConsumer.constant.StrategyName;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemSyncDto;
import com.lenskart.financeConsumer.dto.d365requests.InvoiceRequest;
import com.lenskart.financeConsumer.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

@Slf4j
@Service(StrategyName.INVOICE_NOT_PRESENT)
public class InvoiceNotPresentStrategy implements FailureEventStrategy {
    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Autowired
    private Map<String, FailureEventStrategy> failureEventStrategyMap;

    @Value("${kafka.topic.generate_invoice_data}")
    private String invoiceDetailsTopic;

    @Value("${fc.fetch.invoice-no.enable:true}")
    private boolean fetchInvoiceNoEnable;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UwOrdersRepository uwOrdersRepository;

    @Autowired
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @Autowired
    private InventoryAdaptorConnector inventoryAdaptorConnector;

    @Autowired
    RetryUtil retryUtil;

    @Override
    public FinanceSourceSystemSyncDto execute(FinanceSourceSystemSyncDto financeSourceSystemSyncDto) {
        log.info("InvoiceNotPresentStrategy started:{}",financeSourceSystemSyncDto);
        InvoiceRequest invoiceRequest = new InvoiceRequest();
        List<Integer> uwItemIds = uwOrdersRepository.getUwItemIdsByShippingPackageId(financeSourceSystemSyncDto.getEntityId());
        invoiceRequest.setUwItemIds(uwItemIds);
        try {
            //if fetch invoice no from unicom enable
            if (fetchInvoiceNoEnable) {
                List<UwOrder> uwOrders =
                        uwOrdersRepository.findByUwItemIdIn(uwItemIds);
                UwOrder uwOrder = uwOrders.get(0);
                String invoiceNumber = inventoryAdaptorConnector.fetchInvoiceNumber(uwOrder.getUnicomOrderCode(),
                        uwOrder.getFacilityCode());
                log.info("InvoiceNotPresentStrategy financeSourceSystemSyncDto getEntityId:{} invoiceNumber:{}",
                        financeSourceSystemSyncDto.getEntityId(),invoiceNumber);
                invoiceRequest.setInvoiceCode(invoiceNumber);
            }
            log.info("InvoiceNotPresentStrategy kafka message:{}", objectMapper.writeValueAsString(invoiceRequest));
            kafkaProducerTemplate.send(invoiceDetailsTopic, objectMapper.writeValueAsString(invoiceRequest));
            retryUtil.updateRetryEntry(financeSourceSystemSyncDto);
        } catch (JsonProcessingException e) {
            log.error("InvoiceNotPresentStrategy error: "+e.getMessage(), e);
        }
        log.info("InvoiceNotPresentStrategy started:{}",financeSourceSystemSyncDto);
        return financeSourceSystemSyncDto;
    }

    @PostConstruct
    private void init() {
        failureEventStrategyMap.put(StrategyName.INVOICE_NOT_PRESENT, this);
    }
}
