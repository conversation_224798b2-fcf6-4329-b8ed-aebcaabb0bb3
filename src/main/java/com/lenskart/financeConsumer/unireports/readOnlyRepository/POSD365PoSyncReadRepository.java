package com.lenskart.financeConsumer.unireports.readOnlyRepository;

import com.lenskart.financeConsumer.model.unireports.UniReportPurchaseOrder;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import javax.persistence.Tuple;
import java.util.List;

public interface POSD365PoSyncReadRepository extends CrudRepository<UniReportPurchaseOrder,String> {

    @Query(value="SELECT upo.purchaseOrderCode, vendorCode, deliveryDate, upo.facilityCode, upo.created, purchaseOrderStatus,"
            + " purchaseOrderCreatedBy, fld_row_last_update_on, itemTypeSku, quantity, unitPrice,recieveQuantity, rejectedQuantity, pendingQuantity"
            + " FROM uniReport_purchase_order upo"
            + " LEFT JOIN uniReport_d365_sync_details udsd ON upo.purchaseOrderCode = udsd.identifier AND upo.facilityCode=udsd.facility_code"
            + " WHERE (upo.created > ?1 AND upo.created < ?2 ) AND (IFNULL(udsd.payload_type,\"PO\")=\"PO\")"
            + " AND upo.facilityCode IN ?3 AND vendorCode NOT IN ?4 AND purchaseOrderStatus IN (\"COMPLETE\", \"APPROVED\") AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") and unitPrice>=0;", nativeQuery = true)
    List<UniReportPurchaseOrder> getUniReportPO(String startDate, String endDate, List<String> enabledfacilityCodesList, List<String> disabledVendorCodeList);

    @Query(value="SELECT upo.purchaseOrderCode, vendorCode, deliveryDate, upo.facilityCode, upo.created, purchaseOrderStatus,"
            + " purchaseOrderCreatedBy, fld_row_last_update_on, itemTypeSku, quantity, unitPrice,recieveQuantity, rejectedQuantity, pendingQuantity"
            + " FROM uniReport_purchase_order upo"
            + " LEFT JOIN uniReport_d365_sync_details udsd ON upo.purchaseOrderCode = udsd.identifier AND upo.facilityCode=udsd.facility_code"
            + " WHERE (upo.created > ?1 AND upo.created < ?2 ) AND (IFNULL(udsd.payload_type,\"PO\")=\"PO\")"
            + " AND vendorCode NOT IN ?3 AND purchaseOrderStatus IN (\"COMPLETE\", \"APPROVED\") AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") and unitPrice>=0;", nativeQuery = true)
    List<UniReportPurchaseOrder> getUniReportPOForAllFacilityCode(String startDate, String endDate, List<String> disabledVendorCodeList);


    @Query(value="SELECT upo.purchaseOrderCode, vendorCode, deliveryDate, upo.facilityCode, upo.created, purchaseOrderStatus,"
            + " purchaseOrderCreatedBy, fld_row_last_update_on, itemTypeSku, quantity, unitPrice,recieveQuantity, rejectedQuantity, pendingQuantity"
            + " FROM uniReport_d365_sync_details udsd"
            + " LEFT JOIN uniReport_purchase_order upo ON upo.purchaseOrderCode = udsd.identifier AND upo.facilityCode=udsd.facility_code"
            + " WHERE (udsd.synced_at > ?1) AND (udsd.synced_at < ?2) AND purchaseOrderStatus IN (\"COMPLETE\", \"APPROVED\")"
            + " AND (udsd.payload_type=\"PO\")"
            + " AND upo.facilityCode IN ?3 AND vendorCode NOT IN ?4"
            + " AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") and unitPrice>=0;", nativeQuery = true)
    List<UniReportPurchaseOrder> getUniReportPOWithFailedSyncStatus(String startDate, String endDate, List<String> enabledfacilityCodesList, List<String> disabledVendorCodeList);

    @Query(value="SELECT upo.purchaseOrderCode, vendorCode, deliveryDate, upo.facilityCode, upo.created, purchaseOrderStatus,"
            + " purchaseOrderCreatedBy, fld_row_last_update_on, itemTypeSku, quantity, unitPrice,recieveQuantity, rejectedQuantity, pendingQuantity"
            + " FROM uniReport_d365_sync_details udsd "
            + " LEFT JOIN uniReport_purchase_order upo ON upo.purchaseOrderCode = udsd.identifier AND upo.facilityCode=udsd.facility_code"
            + " WHERE (udsd.synced_at > ?1) AND (udsd.synced_at < ?2) AND purchaseOrderStatus IN (\"COMPLETE\", \"APPROVED\")"
            + " AND (udsd.payload_type=\"PO\")"
            + " AND vendorCode NOT IN ?3 AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") and unitPrice>=0;", nativeQuery = true)
    List<UniReportPurchaseOrder> getUniReportPOWithFailedSyncStatusForAllFacilityCode(String startDate, String endDate, List<String> disabledVendorCodeList);

    @Query(value="SELECT upo.purchaseOrderCode, vendorCode, deliveryDate, upo.facilityCode, upo.created, purchaseOrderStatus,"
            + " purchaseOrderCreatedBy, fld_row_last_update_on, itemTypeSku, quantity, unitPrice,recieveQuantity, rejectedQuantity, pendingQuantity"
            + " FROM uniReport_purchase_order upo"
            + " LEFT JOIN uniReport_d365_sync_details udsd ON upo.purchaseOrderCode = udsd.identifier AND upo.facilityCode=udsd.facility_code"
            + " WHERE upo.purchaseOrderCode=?1 AND purchaseOrderStatus IN (\"COMPLETE\", \"APPROVED\") AND upo.facilityCode IN ?2 "
            + " AND (IFNULL(udsd.payload_type,\"PO\")=\"PO\") AND upo.created > ?4 "
            + " AND vendorCode NOT IN ?3 AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") and unitPrice>=0;", nativeQuery = true)
    List<UniReportPurchaseOrder> getUniReportPOFromPurchaseOrderCode(String purchaseOrderCode, List<String> enabledfacilityCodesList, List<String> disabledVendorCodeList, String financialYearStartDate);

    @Query(value="SELECT upo.purchaseOrderCode, vendorCode, deliveryDate, upo.facilityCode, upo.created, purchaseOrderStatus,"
            + " purchaseOrderCreatedBy, fld_row_last_update_on, itemTypeSku, quantity, unitPrice,recieveQuantity, rejectedQuantity, pendingQuantity"
            + " FROM uniReport_purchase_order upo"
            + " LEFT JOIN uniReport_d365_sync_details udsd ON upo.purchaseOrderCode = udsd.identifier AND upo.facilityCode=udsd.facility_code"
            + " WHERE upo.purchaseOrderCode=?1 AND purchaseOrderStatus IN (\"COMPLETE\", \"APPROVED\") AND vendorCode NOT IN ?2 "
            + " AND (IFNULL(udsd.payload_type,\"PO\")=\"PO\") AND upo.created > ?3 "
            + " AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") and unitPrice>=0;", nativeQuery = true)
    List<UniReportPurchaseOrder> getUniReportPOFromPurchaseOrderCodeForAllFacilityCode(String purchaseOrderCode, List<String> disabledVendorCodeList, String financialYearStartDate);

    @Query(value="SELECT exchange_rate FROM uniReport_currency_exchange_rates WHERE from_currency=?1 AND to_currency=?2 AND exchange_rate_date=?3 order by id desc limit 1", nativeQuery = true)
    Double exchangeRate(String fromCurrency, String toCurrency, String date);

    @Query(value = "SELECT count(*) FROM uniReport_d365_sync_details WHERE identifier = ?1 AND payload_type=\"PO\"", nativeQuery = true )
    int checkPOSyncEntryExists(String identifier);

    @Query(value = "SELECT retry_count FROM uniReport_d365_sync_details WHERE identifier = ?1 AND payload_type=\"PO\"", nativeQuery = true )
    int getRetryCount(String purchaseOrderNumber);

    @Query(value = "SELECT vendorCode FROM uniReport_purchase_order WHERE purchaseOrderCode = ?1 AND created>\"2023-04-01\" LIMIT 1", nativeQuery = true )
    String getVendorCodeFromPurchaseOrderCode(String purchaseOrderCode);

    @Query(value = "SELECT DISTINCT itemtypeSku FROM uniReport_purchase_order WHERE purchaseOrderCode = ?1 AND created > ?2 ", nativeQuery = true )
    List<Integer> getPoItemIds(String purchaseOrderCode, String financialYearStartDate);

    @Query(value = "SELECT reason FROM uniReport_d365_sync_details WHERE identifier = ?1 AND payload_type=\"PO\" LIMIT 1", nativeQuery = true )
    String getFailureReason(String purchaseOrderCode);

    @Query(value = "select purchaseOrderCode,unitPrice from unireports.uniReport_purchase_order where purchaseOrderCode in ?1", nativeQuery = true)
    List<Tuple> getPurchaseOrderAndUnitPrice(List<String> idList);

    @Query(value = "SELECT facilityCode FROM uniReport_purchase_order WHERE purchaseOrderCode = ?1 AND created>?2 LIMIT 1", nativeQuery = true )
    String getFacilityCodeFromPurchaseOrderCode(String purchaseOrderCode, String financialYearStartDate);
}
