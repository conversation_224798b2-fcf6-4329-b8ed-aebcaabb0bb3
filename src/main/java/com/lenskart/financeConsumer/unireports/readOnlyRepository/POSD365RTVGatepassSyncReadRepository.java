package com.lenskart.financeConsumer.unireports.readOnlyRepository;

import com.lenskart.financeConsumer.model.unireports.UnireportGatepass;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface POSD365RTVGatepassSyncReadRepository extends CrudRepository<UnireportGatepass,String> {

    @Query(value = "SELECT ug.gatePassCode, ug.itemCode, ug.facilityCode, ug.toParty, ug.status, ug.createdAt, " +
            " ug.updatedAt, ug.gatePassCreatedBy, ug.quantity, ug.brand, ug.type, ug.itemSkuCode, " +
            " ug.unitPriceWithoutTax ,ug.fldRowLastUpdateOn, ug.grnCreatedAt, ug.poCode, " +
            " ug.hsnCode, ug.CGSTRate, ug.IGSTRate, ug.SGSTRate, ug.UTGSTRate" +
            " FROM unireports.uniReport_gatepass_for_all_facility_for_d365_sync ug " +
            " LEFT JOIN " +
            " (SELECT * FROM uniReport_d365_sync_details WHERE payload_type=\"RTV_GATEPASS\") udsd  " +
            " ON ug.gatePassCode = udsd.identifier AND ug.facilityCode = udsd.facility_code " +
            " WHERE (ug.updatedAt > ?1 AND ug.updatedAt < ?2 ) " +
            " AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") AND (ug.type = \"RETURN_TO_VENDOR\") " +
            " AND ug.facilityCode IN ?3  " +
            " AND (IFNULL(udsd.payload_type,\"RTV_GATEPASS\")=\"RTV_GATEPASS\")",
            nativeQuery = true)
    List<UnireportGatepass> getUniReportRTVGatepass(String startDate, String endDate, List<String> enabledfacilityCodesList);

    @Query(value = "SELECT ug.gatePassCode, ug.itemCode, ug.facilityCode, ug.toParty, ug.status, ug.createdAt," +
            " ug.updatedAt, ug.gatePassCreatedBy, ug.quantity, ug.brand, ug.type, ug.itemSkuCode, " +
            " ug.unitPriceWithoutTax ,ug.fldRowLastUpdateOn, ug.grnCreatedAt, ug.poCode, " +
            " ug.hsnCode, ug.CGSTRate, ug.IGSTRate, ug.SGSTRate, ug.UTGSTRate" +
            " FROM unireports.uniReport_gatepass_for_all_facility_for_d365_sync ug " +
            " LEFT JOIN " +
            " (SELECT * FROM uniReport_d365_sync_details WHERE payload_type=\"RTV_GATEPASS\") udsd  " +
            " ON ug.gatePassCode = udsd.identifier AND ug.facilityCode = udsd.facility_code " +
            " WHERE (ug.updatedAt > ?1 AND ug.updatedAt < ?2 ) AND (IFNULL(udsd.payload_type,\"RTV_GATEPASS\")=\"RTV_GATEPASS\")" +
            " AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\") AND (ug.type = \"RETURN_TO_VENDOR\");",
            nativeQuery = true)
    List<UnireportGatepass> getUniReportRTVGatepassForAllFacilityCode(String startDate, String endDate);

    @Query(value = "SELECT ug.gatePassCode, ug.itemCode, ug.facilityCode, ug.toParty, ug.status, ug.createdAt,"
            + " ug.updatedAt, ug.gatePassCreatedBy, ug.quantity, ug.brand, ug.type, ug.itemSkuCode,"
            + " ug.unitPriceWithoutTax ,ug.fldRowLastUpdateOn, ug.grnCreatedAt, ug.poCode, "
            + " ug.hsnCode, ug.CGSTRate, ug.IGSTRate, ug.SGSTRate, ug.UTGSTRate "
            + " FROM uniReport_d365_sync_details  udsd"
            + " INNER JOIN unireports.uniReport_gatepass_for_all_facility_for_d365_sync ug ON "
            + " udsd.identifier = ug.gatePassCode AND "
            + " udsd.facility_code= ug.facilityCode"
            + " WHERE (udsd.synced_at > ?1) AND (udsd.synced_at < ?2) AND( ug.type = \"RETURN_TO_VENDOR\")"
            + " AND (udsd.payload_type=\"RTV_GATEPASS\") AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\""
            + "  AND ug.facilityCode IN ?3 );",
            nativeQuery = true)
    List<UnireportGatepass> getUniReportRTVGatepassWithFailedSyncStatus(String startDate, String endDate, List<String> enabledWarehousesList);

    @Query(value = "SELECT ug.gatePassCode, ug.itemCode, ug.facilityCode, ug.toParty, ug.status, ug.createdAt,"
            + " ug.updatedAt, ug.gatePassCreatedBy, ug.quantity, ug.brand, ug.type, ug.itemSkuCode,"
            + " ug.unitPriceWithoutTax ,ug.fldRowLastUpdateOn, ug.grnCreatedAt, ug.poCode, "
            + " ug.hsnCode, ug.CGSTRate, ug.IGSTRate, ug.SGSTRate, ug.UTGSTRate"
            + " FROM uniReport_d365_sync_details  udsd"
            + " INNER JOIN unireports.uniReport_gatepass_for_all_facility_for_d365_sync ug ON "
            + " udsd.identifier = ug.gatePassCode AND "
            + " udsd.facility_code= ug.facilityCode"
            + " WHERE (udsd.synced_at > ?1) AND (udsd.synced_at < ?2) AND( ug.type = \"RETURN_TO_VENDOR\")"
            + " AND (udsd.payload_type=\"RTV_GATEPASS\") AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\");",
            nativeQuery = true)
    List<UnireportGatepass> getUniReportRTVGatepassWithFailedSyncStatusForAllFacilityCode(String startDate, String endDate);

    @Query(value = "SELECT ug.gatePassCode, ug.itemCode, ug.facilityCode, ug.toParty, ug.status, ug.createdAt, "
            + " ug.updatedAt, ug.gatePassCreatedBy, ug.quantity, ug.brand, ug.type, ug.itemSkuCode, "
            + " ug.unitPriceWithoutTax ,ug.fldRowLastUpdateOn, ug.grnCreatedAt, ug.poCode, "
            + " ug.hsnCode, ug.CGSTRate, ug.IGSTRate, ug.SGSTRate, ug.UTGSTRate"
            + " FROM uniReport_gatepass_for_all_facility_for_d365_sync ug "
            + " LEFT JOIN "
            + " (SELECT * FROM uniReport_d365_sync_details WHERE identifier =?1 and payload_type=\"RTV_GATEPASS\") udsd  "
            + " ON udsd.identifier = ug.gatePassCode AND"
            + " ug.facilityCode=udsd.facility_code"
            + " WHERE ug.gatePasscode=?1 AND( ug.type = \"RETURN_TO_VENDOR\") AND "
            + " (IFNULL(udsd.payload_type,\"RTV_GATEPASS\")=\"RTV_GATEPASS\") "
            + " AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\")"
            + " AND ug.facilityCode IN ?2", nativeQuery = true)
    List<UnireportGatepass> getUniReportRTVFromGatepassCode(String GatepassCode, List<String> enabledWarehousesList);

    @Query(value = "SELECT ug.gatePassCode, ug.itemCode, ug.facilityCode, ug.toParty, ug.status, ug.createdAt, "
            + " ug.updatedAt, ug.gatePassCreatedBy, ug.quantity, ug.brand, ug.type, ug.itemSkuCode, "
            + " ug.unitPriceWithoutTax ,ug.fldRowLastUpdateOn, ug.grnCreatedAt, ug.poCode, "
            + " ug.hsnCode, ug.CGSTRate, ug.IGSTRate, ug.SGSTRate, ug.UTGSTRate"
            + " FROM uniReport_gatepass_for_all_facility_for_d365_sync ug "
            + " LEFT JOIN "
            + " (SELECT * FROM uniReport_d365_sync_details WHERE identifier =?1 and payload_type=\"RTV_GATEPASS\") udsd  "
            + " ON udsd.identifier = ug.gatePassCode AND"
            + " ug.facilityCode=udsd.facility_code"
            + " WHERE ug.gatePasscode=?1 AND( ug.type = \"RETURN_TO_VENDOR\") AND "
            + " (IFNULL(udsd.payload_type,\"RTV_GATEPASS\")=\"RTV_GATEPASS\") "
            + " AND (IFNULL(udsd.d365_status,\"NOT_SYNCED\") = \"NOT_SYNCED\")", nativeQuery = true)
    List<UnireportGatepass> getUniReportRTVFromGatepassCodeForAllFacilityCode(String GatepassCode);

    @Query(value = "SELECT count(*) FROM uniReport_d365_sync_details WHERE identifier = ?1 AND " +
            " payload_type=\"RTV_GATEPASS\"", nativeQuery = true )
    int checkRTVGatepassSyncEntryExists(String identifier);

    @Query(value = "SELECT retry_count FROM uniReport_d365_sync_details WHERE identifier = ?1 AND" +
            " payload_type=\"RTV_GATEPASS\"", nativeQuery = true )
    int getRetryCount(String gatePassCode);
}

