package com.lenskart.financeConsumer.unireports.writeRepository;

import com.lenskart.financeConsumer.model.unireports.UniReportGrn;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;


public interface POSD365GRNSyncRepository extends CrudRepository<UniReportGrn, String>{

    @Modifying
    @Transactional(value = "uniReportsTransactionManager")
    @Query(value="UPDATE uniReport_d365_sync_details SET d365_status = :d365_status, reason=:reason, retry_count=:retry_count, synced_at=CURRENT_TIMESTAMP WHERE identifier=:identifier AND facility_code=:facility_code", nativeQuery = true)
    void updateD365Response(String identifier, String facility_code, String d365_status, String reason, int retry_count);

    @Modifying
    @Transactional(value = "uniReportsTransactionManager")
    @Query(value = "INSERT INTO uniReport_d365_sync_details (identifier, facility_code, d365_status,payload_type, reason, synced_at, created_at) values (:identifier, :facility_code, :d365_status,\"GRN\", :reason, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)", nativeQuery = true)
    void insertD365Response(String identifier, String facility_code, String d365_status, String reason);
}
