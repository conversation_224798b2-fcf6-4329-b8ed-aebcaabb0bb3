package com.lenskart.financeConsumer.clients;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.dto.d365requests.FetchLineItemDetailsResponse;
import com.lenskart.financeConsumer.dto.d365requests.FetchShippingPackageDetailsResponse;
import com.lenskart.financeConsumer.dto.d365requests.FetchSummaryResponse;
import com.lenskart.inventoryadapter.client.InventoryAdapterClient;
import com.lenskart.inventoryadapter.request.CustomRequest;
import com.lenskart.inventoryadapter.request.GetShippingPackageDetailsRequest;
import com.lenskart.inventoryadapter.response.CustomResponse;
import com.lenskart.inventoryadapter.response.GetShippingPackageDetailsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class UnicomClient {

    @Autowired
    @Qualifier("restTemplateWithTimeout")
    private RestTemplate restTemplate;

    @Autowired
    private InventoryAdapterClient inventoryAdapterClient;

    @Autowired
    private ObjectMapper objectMapper;


    public FetchShippingPackageDetailsResponse fetchShippingPackageDetails(String unicomOrderCode,String facility) throws Exception{
        try {
            GetShippingPackageDetailsRequest customRequest = new GetShippingPackageDetailsRequest();
            customRequest.setUnicomOrderCode(unicomOrderCode);
            customRequest.setFacility(facility);
            log.info("[UnicomClient][fetchShippingPackageDetails] sending request body for unicomOrderCode {} {} ",unicomOrderCode,customRequest);
            GetShippingPackageDetailsResponse getShippingPackageDetailsResponse = inventoryAdapterClient.getOrderDetailsByUnicomSaleOrderAndFacility(customRequest);
            if (Objects.nonNull(getShippingPackageDetailsResponse)){
                FetchShippingPackageDetailsResponse fetchShippingPackageDetailsResponse = objectMapper.readValue(getShippingPackageDetailsResponse.getResponseString(), FetchShippingPackageDetailsResponse.class);
                log.info("[UnicomClient][fetchShippingPackageDetails] response for unicomOrderCode {} {}",unicomOrderCode,fetchShippingPackageDetailsResponse);
                return fetchShippingPackageDetailsResponse;
            }
            throw new Exception("[UnicomClient][fetchShippingPackageDetails] response is blank");
        }
        catch (Exception e){
            log.error("[UnicomClient][fetchShippingPackageDetails] error {} {}",unicomOrderCode,e);
            throw e;
        }
    }

    public FetchLineItemDetailsResponse fetchLineItemDetails(String unicomOrderCode, String facility, List<String> lineItemIdentifiers) throws Exception{
        try {
            Map<String, Object> requestPayload = new HashMap<>(1);
            requestPayload.put("code", unicomOrderCode);
            requestPayload.put("lineItemIdentifiers", lineItemIdentifiers);
            CustomRequest customRequest = new CustomRequest();
            customRequest.setPayload(requestPayload);
            customRequest.setFacility(facility);
            log.info("[UnicomClient][fetchLineItemDetails] sending request body for unicomOrderCode {} {} ",unicomOrderCode,customRequest);
            String response = inventoryAdapterClient.fetchLineItemDetails(customRequest);
            if(StringUtils.isNotBlank(response)){
                CustomResponse customResponse = objectMapper.readValue(response, CustomResponse.class);
                if(StringUtils.isNotBlank(customResponse.getResponse())){
                    FetchLineItemDetailsResponse fetchLineItemDetailsResponse = objectMapper.readValue(customResponse.getResponse(), FetchLineItemDetailsResponse.class);
                    log.info("[UnicomClient][fetchLineItemDetails] response for unicomOrderCode {} {}",unicomOrderCode,fetchLineItemDetailsResponse);
                    return fetchLineItemDetailsResponse;
                }
            }
            throw new Exception("[UnicomClient][fetchLineItemDetails] response is blank");
        }
        catch (Exception e){
            log.error("[UnicomClient][fetchLineItemDetails] error {} {}",unicomOrderCode,e);
            throw e;
        }
    }


    public FetchSummaryResponse fetchSummary(String unicomOrderCode,String facility) throws Exception{
        try {
            Map<String, Object> requestPayload = new HashMap<>(1);
            requestPayload.put("code", unicomOrderCode);
            CustomRequest customRequest = new CustomRequest();
            customRequest.setPayload(requestPayload);
            customRequest.setFacility(facility);
            log.info("[UnicomClient][fetchSummary] sending request body for unicomOrderCode {} {} ",unicomOrderCode,customRequest);
            String response = inventoryAdapterClient.fetchSummary(customRequest);
            if(StringUtils.isNotBlank(response)) {
                CustomResponse customResponse = objectMapper.readValue(response, CustomResponse.class);
                if (StringUtils.isNotBlank(customResponse.getResponse())) {
                    FetchSummaryResponse fetchSummaryResponse = objectMapper.readValue(customResponse.getResponse(), FetchSummaryResponse.class);
                    log.info("[UnicomClient][fetchSummary] response for unicomOrderCode {} {}",unicomOrderCode,fetchSummaryResponse);
                    return fetchSummaryResponse;
                }
            }
            throw new Exception("[UnicomClient][fetchSummary] response is blank");
        }
        catch (Exception e){
            log.error("[UnicomClient][fetchSummary] error {} {}",unicomOrderCode,e);
            throw e;
        }
    }


}
