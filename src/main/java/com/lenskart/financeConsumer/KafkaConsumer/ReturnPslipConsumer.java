package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnCreateRequest;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnPSlipRequest;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.ReturnOrderService;
import com.lenskart.financeConsumer.util.ReturnUtil;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.concurrent.CountDownLatch;

@Service
@Slf4j
public class ReturnPslipConsumer {

    @Autowired
    private ReturnOrderService returnOrderService;

    @Autowired
    private ReturnUtil returnUtil;

    private CountDownLatch latch = new CountDownLatch(1);

    @Trace(dispatcher=true,metricName = "ReturnPslipConsumer")
    @KafkaListener(topics = "${d365.return.pslip.kafka.topic}", groupId = "${d365.return.pslip.kafka.topic.group}")
    public void listenConsumer1(String message, Acknowledgment ack) throws InvalidRequestException {
        if (shouldProcessData()) {
            processMessage(message);
        }
        latch.countDown();
        ack.acknowledge();
    }

    public void processMessage(String message) {
        ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));
        ReturnPSlipRequest returnPSlipRequestMessage = null;
        ReturnCreateRequest returnCreateRequest = null;
        Integer uwItemId = null;

        log.info("[ReturnPSlipConsumer][processMessage] message received : {}", message);
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnPSlipConsumer][processMessage] empty message received");
                return;
            }
            try {
                returnCreateRequest = mapper.readValue(message, ReturnCreateRequest.class);
                PackingSlip packingSlip = null;
                log.info("[ReturnPSlipConsumer][processMessage] returnCreateRequest : " + returnCreateRequest);
                if (returnCreateRequest != null) {
                    uwItemId = returnCreateRequest.getUwItemId();
                    packingSlip = returnOrderService.generatePackingSlipPayloadRetry(returnCreateRequest);
                }
                if (packingSlip != null) {
                    returnOrderService.createReturnPSlipRetry(packingSlip, uwItemId, returnCreateRequest);
                }
            } catch (Exception e) {
                log.error("[ReturnPSlipConsumer][processMessage] PackingSlip retry failed for uwItemId : {} : ",uwItemId, e);
            }

        } catch (Exception e) {
            log.error("[ReturnPSlipConsumer][processMessage] exception occurred PackingSlip retry for uwItemId : {} : ",uwItemId,e);
        }
    }

    private boolean shouldProcessData() {
        return returnUtil.getPSlipRetryFlag();
    }
}
