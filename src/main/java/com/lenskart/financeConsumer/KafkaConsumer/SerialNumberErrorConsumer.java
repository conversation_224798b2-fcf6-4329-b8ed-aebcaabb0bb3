package com.lenskart.financeConsumer.KafkaConsumer;

import com.lenskart.core.model.D365ReturnTracking;
import com.lenskart.financeConsumer.dao.D365ReturnTrackingRepository;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dto.InventoryCorrectionResponse;
import com.lenskart.financeConsumer.dto.SerialNumberErrorDto;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.service.InventoryCorrectionService;
import com.lenskart.financeConsumer.service.impl.ReturnOrderServiceImpl;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import com.newrelic.api.agent.Trace;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Generated
@Slf4j
@Service
public class SerialNumberErrorConsumer {

    @Autowired
    private InventoryCorrectionService inventoryCorrectionService;

    @Autowired
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Value("${packingSlip.kafka.topic}")
    private String packingSlipTopic;

    @Value("${serial.numberError.consumer.enabled:true}")
    private boolean isSerialNumberConsumerEnabled;

    @Autowired
    D365ReturnTrackingRepository d365ReturnTrackingRepository;

    public static final Pattern hasNotBeenCreatedPattern = Pattern.compile(".*has not been created for item.*",
            Pattern.CASE_INSENSITIVE);


    @Value("${max.retry.packing.slip:5}")
    private int maxRetryPackingSlip;

    @Value("${max.retry.return.packing-slip.correction:10}")
    private int returnPSMAxRetryLimit;

    @Autowired
    private ReturnOrderServiceImpl returnOrderService;

    @Value("#{'${inventory-correction.allowed.events}'.split(',')}")
    private List<String> allowedEvents;

    @Value("${return.ps.serial-has-not-been-created.enabled:false}")
    boolean isSerialNumberHasNotBeenCreatedCorrectionEnabled;

    @Trace(dispatcher = true, metricName = "SerialNumberErrorConsumer")
    @KafkaListener(topics = "${d365.serialNumberError.kafka.topic}", groupId = "${d365.serialNumberError.kafka.groupId}")
    public void consumeSerialNumberErrorInfo(String message, Acknowledgment ack) {
        try {
            if (!isSerialNumberConsumerEnabled || StringUtils.isEmpty(message)) {
                return;
            }

            SerialNumberErrorDto serialNumberErrorDto = ObjectHelper.readValueWithDateTime(message, SerialNumberErrorDto.class);
            log.info("[SerialNumberErrorConsumer][consumeSerialNumberErrorInfo] : started processing error serial number : {}", serialNumberErrorDto);

            if(!isInventoryCorrectionAllowed(serialNumberErrorDto.getSource())){
                return;
            }
            FinanceSourceSystemSync financeSourceSystemSync=null;
            if("SCM".equals(serialNumberErrorDto.getSource())){
                financeSourceSystemSync = fetchFinanceSourceEntity(serialNumberErrorDto);
                if(Constants.Common.SUCCESS.equalsIgnoreCase(financeSourceSystemSync.getD365SyncStatus())){
                    return;
                }
                if(financeSourceSystemSync.getRetryCount() > maxRetryPackingSlip)
                    return;
            }

            if(Objects.nonNull(financeSourceSystemSync) && Objects.isNull(serialNumberErrorDto.getId())){
                serialNumberErrorDto.setId(financeSourceSystemSync.getId());
            }
            if(Objects.nonNull(financeSourceSystemSync) && StringUtils.isBlank(serialNumberErrorDto.getPayload()))
                serialNumberErrorDto.setPayload(financeSourceSystemSync.getPayload());

            InventoryCorrectionResponse inventoryCorrectionResponse =null;
            boolean isSuccess =false;
            if("RETURN".equalsIgnoreCase(serialNumberErrorDto.getSource()) &&
                    hasNotBeenCreatedPattern.matcher(serialNumberErrorDto.getErrorString()).find() ){

                if(!isSerialNumberHasNotBeenCreatedCorrectionEnabled)
                    return;

                isSuccess = inventoryCorrectionService.processMovementJournalForSerialNumberHasNotBeenCreatedReturnError(serialNumberErrorDto.getErrorString(),
                        serialNumberErrorDto.getEntityId(), serialNumberErrorDto.getFacilityCode(), serialNumberErrorDto.getEventTime(), serialNumberErrorDto.getLegalEntity(), serialNumberErrorDto.getPid(), serialNumberErrorDto.getSource());
            }else{
                inventoryCorrectionResponse = inventoryCorrectionService.processMovementJournalForSerialNumberError(serialNumberErrorDto.getErrorString(),
                        serialNumberErrorDto.getEntityId(), serialNumberErrorDto.getFacilityCode(), serialNumberErrorDto.getEventTime(), serialNumberErrorDto.getLegalEntity(), serialNumberErrorDto.getPid(), serialNumberErrorDto.getSource());
            }

            log.info("[SerialNumberErrorConsumer][consumeSerialNumberErrorInfo][inventoryCorrectionResponse] movementJournalResponse {}", inventoryCorrectionResponse);

            if ("SCM".equals(serialNumberErrorDto.getSource())) {

                if(inventoryCorrectionResponse.getStatus() && StringUtils.isNotEmpty(serialNumberErrorDto.getPayload())){
                    log.info("[SerialNumberErrorConsumer][consumeSerialNumberErrorInfo][published] serialNumberErrorDto {}",serialNumberErrorDto);
                    kafkaProducerTemplate.send(packingSlipTopic,serialNumberErrorDto.getEntityId(), serialNumberErrorDto.getPayload());
                }
            }else if("RETURN".equalsIgnoreCase(serialNumberErrorDto.getSource()) &&
                    hasNotBeenCreatedPattern.matcher(serialNumberErrorDto.getErrorString()).find() ){
                if( isSuccess ){   // both pos and neg movement journals done.
                    List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingRepository.findByReturnId(Integer.valueOf(serialNumberErrorDto.getEntityId()));
                    if(!CollectionUtils.isEmpty(d365ReturnTrackingList) && d365ReturnTrackingList.get(0).getPSlipRetryCount() <= returnPSMAxRetryLimit){
                        log.info("trying to sync returnOrder with returnId : {}",serialNumberErrorDto.getEntityId());
                        returnOrderService.syncReturnOrderByReturnId(Integer.valueOf(serialNumberErrorDto.getEntityId()));
                    }
                }
            }
            else if (inventoryCorrectionResponse.getStatus() && serialNumberErrorDto.getSource().equals("RETURN") ){

                List<D365ReturnTracking> d365ReturnTrackingList = d365ReturnTrackingRepository.findByReturnId(Integer.valueOf(serialNumberErrorDto.getEntityId()));
                if(!CollectionUtils.isEmpty(d365ReturnTrackingList) && d365ReturnTrackingList.get(0).getPSlipRetryCount() <= returnPSMAxRetryLimit){
                    log.info("trying to sync returnOrder with returnId : {}",serialNumberErrorDto.getEntityId());
                    returnOrderService.syncReturnOrderByReturnId(Integer.valueOf(serialNumberErrorDto.getEntityId()));
                }
            }

        } catch (Exception e) {
            log.error("[SerialNumberErrorConsumer][consumeSerialNumberErrorInfo] : Error occurred while syncing error serial number to D365 : {} , for input : {}", e, message);
        } finally {
            log.info("[SerialNumberErrorConsumer][consumeSerialNumberErrorInfo] : finished processing error serial number : {}", message);
            ack.acknowledge();
        }

    }

    public FinanceSourceSystemSync fetchFinanceSourceEntity(SerialNumberErrorDto serialNumberErrorDto) throws RecordNotFoundException {
        FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository
                .findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.PACKING_SLIP, FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                        serialNumberErrorDto.getEntityId(), serialNumberErrorDto.getFacilityCode());

        if (Objects.nonNull(financeSourceSystemSync)) {
            return financeSourceSystemSync;

        }
        throw new RecordNotFoundException("PackagingSlip Not found");
    }



    private boolean isInventoryCorrectionAllowed(String source){
        return allowedEvents.contains(source);
    }

}
