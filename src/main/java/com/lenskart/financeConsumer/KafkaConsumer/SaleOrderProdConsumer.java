package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.facade.FinanceSourceSystemSyncFacade;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.SalesOrderService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.util.*;
import com.lenskart.financeConsumer.util.PayloadBuildUtils;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SaleOrderProdConsumer{

    @Autowired
    private UwOrdersService uwOrdersService;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate<String, String> kafkaProducerTemplate;

    @Autowired
    private PayloadBuildUtils payloadBuildUtils;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private FinanceSourceSystemSyncFacade financeSourceSystemSyncFacade;

    @Autowired
    private SalesOrderService salesOrderService;

    @Trace(dispatcher = true, metricName = "SaleOrderProdConsumer")
    @KafkaListener(topics = "${d365.salesorder.kafka.prod.temp.topic}", groupId = "${d365.salesorder.kafka.prod.temp.topic.group}")
    public void listenConsumer1(String message, Acknowledgment ack) throws InvalidRequestException {
        processMessage(message);
        ack.acknowledge();
    }

    public void processMessage(String message) {
        ObjectMapper mapper = new ObjectMapper();
        log.info("[SaleOrderProdConsumer][processMessage] Processing back sync message - hasMessage: {}",
                !StringUtils.isEmpty(message));
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[SaleOrderProdConsumer][processMessage] Invalid empty message received from kafka");
                return;
            }
            List<Integer> uwItemIds = mapper.readValue(message, List.class);
            UwOrder uwOrder = uwOrdersService.getUwOrderByUwItemIdForShipmentId(uwItemIds.get(0));
            String shipmentId = String.valueOf(uwOrder.getShippingPackageId());
            genericClientService.saveInitLog(shipmentId, message, Constants.SalesOrder.OrderType, Constants.Common.InProcess);
            salesOrderService.createSalesOrder(shipmentId, uwItemIds, Constants.SalesOrder.CREATE_PROD_URL, null);
            log.info("[SaleOrderProdConsumer][processMessage] createSalesOrder finished : {}", message);
        } catch (Exception e) {
            log.info("[SalesOrderConsumer][processMessage] " + e);
        }
    }
}
