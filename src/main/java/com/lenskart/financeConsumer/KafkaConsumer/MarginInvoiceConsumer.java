package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lenskart.financeConsumer.dto.MarginSyncDTO;
import com.lenskart.financeConsumer.dto.SoLine;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.MarginInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

@Service
    @Slf4j
    public class MarginInvoiceConsumer {


        @Autowired
        MarginInvoiceService marginInvoiceService;

        @Autowired
        private GenericClientService genericClientService;

        @KafkaListener(topics = "${d365.pos.margin.sync.topic:pos-margin-invoices-d365-sync}",  groupId = "${d365.pos.margin.sync.topic.group:pos-margin-invoices-d365-sync-consumers}") //to change later
        public void listen(String message, Acknowledgment ack) throws InvalidRequestException {
            processMessage(message);
            ack.acknowledge();
        }

        public void processMessage(String message) {
            try {
                log.info("[MarginInvoiceConsumer][processMessage] msg : {}", message);
                if (StringUtils.isEmpty(message)) {
                    log.info("[MarginInvoiceConsumer][processMessage] empty message");
                    return;
                }

                JsonObject jsonPayload = JsonParser.parseString(message).getAsJsonObject().get("requestPayload").getAsJsonObject();
                if(Objects.nonNull(jsonPayload)) {
                    JsonObject contract = jsonPayload.getAsJsonObject("_contract");
                    log.info("contract {}", contract);
                    MarginSyncDTO salesOrderHeader = new ObjectMapper()
                            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                            .readValue(contract.getAsJsonObject("_salesOrderHeader").toString(), MarginSyncDTO.class);
                    log.info("Margin Invoice Sync payload:" + new ObjectMapper().writeValueAsString(salesOrderHeader));
                    marginInvoiceService.d365MarginSync(salesOrderHeader);
                }
            } catch(Exception e) {
                log.error("Exception occurred in margin sync: {}", e.getMessage());
            }

        }
    }
