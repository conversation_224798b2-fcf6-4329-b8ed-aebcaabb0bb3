package com.lenskart.financeConsumer.KafkaConsumer;

import com.lenskart.financeConsumer.controller.POSD365SyncController;
import com.lenskart.financeConsumer.service.impl.POSD365SyncServiceImpl;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
@Slf4j
@Component
public class UnicommPoConsumer {

    @Autowired
    private POSD365SyncServiceImpl posd365SyncServiceImpl;

    @Autowired
    private POSD365SyncController posd365SyncController;

    @Value( "${d365.unicomm.PO.sync.enabled:false}")
    private boolean isPoSyncEnabled;

    @Trace(dispatcher = true, metricName = "UnicommPoConsumer")
    @KafkaListener(topics = "${d365.unicomm.po.kafka.topic}", groupId = "${d365.unicomm.po.kafka.topic.group}")
    public void listen(String message, Acknowledgment ack) {
        String poId = null;
        boolean processingSuccessful = false;

        try {
            if (!isPoSyncEnabled || StringUtils.isEmpty(message)) {
                log.debug("[UnicommPoConsumer][listen] Processing skipped - poSyncEnabled: {} | hasMessage: {}",
                        isPoSyncEnabled, !StringUtils.isEmpty(message));
                return;
            }

            // Extract PO ID for logging
            try {
                poId = extractPoIdFromMessage(message);
            } catch (Exception e) {
                log.warn("[UnicommPoConsumer][listen] Could not extract PO ID from message");
            }

            log.info("[UnicommPoConsumer][listen] Processing unicomm PO - poId: {}", poId);
            processMessage(message);
            processingSuccessful = true;
            log.info("[UnicommPoConsumer][listen] Successfully processed unicomm PO - poId: {}", poId);

        } catch (Exception exception) {
            log.error("[UnicommPoConsumer][listen] Exception occurred while processing unicomm PO - poId: {} | error: {}",
                    poId, exception.getMessage(), exception);
        } finally {
            if (!processingSuccessful && poId != null) {
                log.warn("[UnicommPoConsumer][listen] Failed to process unicomm PO - poId: {}", poId);
            }
            ack.acknowledge();
        }
    }

    private String extractPoIdFromMessage(String message) {
        // Simple extraction logic - can be enhanced based on message structure
        if (message != null && message.contains("\"")) {
            return "extracted_from_message";
        }
        return "unknown";
    }

    private void processMessage(String message) {
        try {
            log.info("[UnicommPoConsumer][processMessage] Pushing Unicomm PO payload to kafka purchaseOrderCode {}", message);
            posd365SyncController.syncPurchaseOrder(message);
        } catch (Exception exception) {
            log.error("[UnicommPoConsumer][processMessage] Exception occurred in Unicomm GRN sync: {}", exception.getMessage(), exception);
        }
    }

}
