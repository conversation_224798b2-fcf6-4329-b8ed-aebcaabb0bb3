package com.lenskart.financeConsumer.KafkaConsumer;

import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.ManualPackingSlipService;
import com.lenskart.financeConsumer.util.ManualForwardFlowUtil;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ManualPackingSlipConsumer {

    @Autowired
    private ManualPackingSlipService manualPackingSlipService;

    @Autowired
    private ManualForwardFlowUtil manualForwardFlowUtil;

    @Trace(dispatcher=true,metricName = "ManualPackingSlipConsumer")
    @KafkaListener(topics = "${d365.unireport.packingslip.kafka.topic}", groupId = "${d365.unireport.packingslip.kafka.topic.group}")
    public void listenConsumer(String message, Acknowledgment ack) throws InvalidRequestException {
        processMessage(message);
        ack.acknowledge();
    }

    public void processMessage(String message){
        try {
            log.info("[UnireportPackingSlipConsumer][processMessage] message consumed " + message);
            manualPackingSlipService.createPackingSlip(message);
        }
        catch (Exception e){
            log.error("[UnireportPackingSlipConsumer][processMessage] message "+message,e);
        }
    }
}
