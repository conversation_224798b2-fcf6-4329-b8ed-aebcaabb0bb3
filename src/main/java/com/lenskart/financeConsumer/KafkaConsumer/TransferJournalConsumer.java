package com.lenskart.financeConsumer.KafkaConsumer;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.lenskart.financeConsumer.dto.d365requests.TransferJournalDTO.D365TransferJournalDTO;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.TransferJournal.TransferJournalService;
import com.lenskart.financeConsumer.service.TransferOrderService;
import com.lenskart.financeConsumer.service.impl.TransferJournalServiceV1Impl;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.ReturnUtil;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TransferJournalConsumer {
    @Autowired
    private TransferJournalService transferJournalService;

    @Autowired
    private TransferJournalServiceV1Impl transferJournalServiceV1;
    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private TransferOrderService transferOrderService;

    @Autowired
    private ReturnUtil returnUtil;

    @Autowired
    ObjectMapper objectMapper;

    @KafkaListener(topics = "${d365.transferjournal.kafka.topic}", groupId = "${d365.transferjournal.kafka.topic.group}")
    public void listenConsumer1(String message, Acknowledgment ack) throws InvalidRequestException {
        if (shouldProcessData()) {
            processMessage(message);
        }
        ack.acknowledge();
    }

    private boolean shouldProcessData() {
        return returnUtil.getTOrderRetryFlag();
    }
    public void processMessage(String message) throws InvalidRequestException {
        ObjectMapper mapper = new ObjectMapper();
        //Response response = new Response();
        log.info("[TransferJournalConsumer][processMessage] Back Sync consumed message : {}", message);
        try {
            if (StringUtils.isEmpty(message)) {
                log.info("[TransferJournalConsumer][processMessage] empty message found for transferJournal");
                return;
            }
            String filteredMessage = message.replace("\\", "");
            log.info("[TransferJournalConsumer][processMessage] filteredMessage in transferJournal consumer {}", filteredMessage);
            D365TransferJournalDTO d365TransferJournalDTO;
            try {
                d365TransferJournalDTO = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(message, D365TransferJournalDTO.class);
                if (d365TransferJournalDTO!= null && d365TransferJournalDTO.getUwItemId()!=null) {
                    genericClientService.saveInitLog(String.valueOf(d365TransferJournalDTO.getUwItemId()), message, Constants.TransferJournal.OrderType,
                            Constants.Common.InProcess);
                    log.info("[TransferJournalConsumer][processMessage] Message deserialized successfully d365TransferJournalDTO {} ",d365TransferJournalDTO);
                    if(d365TransferJournalDTO.getIsTransferOrder() != null && d365TransferJournalDTO.getIsTransferOrder()){
                        transferOrderService.createTransferOrder(d365TransferJournalDTO);
                    }else{
                        transferJournalService.createTransferJournalAsync(d365TransferJournalDTO);
                    }
                }
            } catch (Exception e) {
                log.error("[TransferJournalConsumer][processMessage] Cannot deserialized the message : ",e);
                //throw e;
            }

        } catch (Exception e) {
            log.error("[TransferJournalConsumer][processMessage] exception occurred during TransferJournal creation : ",e);
            //throw e;
        }
    }

    @Trace(dispatcher=true,metricName = "TransferJournalConsumer")
    @KafkaListener(topics = "${d365.transfer_journal.kafka.topic}", groupId = "${d365.transfer_journal.kafka.group:transferJournalKafkaConsumerGroup}")
    public void consumeTransferJournal(String id, Acknowledgment ack){
        try{
            if(StringUtils.isEmpty(id)){
                return;
            }
            Long transferJournalId = objectMapper.readValue(id, Long.class);
            log.info("[TransferJournalConsumer] : started processing transferJournal : {}",transferJournalId);
            transferJournalServiceV1.createTransferJournalById(transferJournalId);
        }catch (Exception e){
            log.error("[TransferJournalConsumer] : Exception occurred while creating transfer journal  : {} , to D365 : {} ",id,e);
        }finally {
            ack.acknowledge();
        }
    }
}