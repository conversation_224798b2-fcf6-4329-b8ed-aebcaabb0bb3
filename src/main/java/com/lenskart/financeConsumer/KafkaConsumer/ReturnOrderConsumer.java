package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.financeConsumer.dao.ReturnOrderItemRepository;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnCreateRequest;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnRequestMessage;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.ReturnOrderService;
import com.lenskart.financeConsumer.util.ReturnUtil;
import com.lenskart.orderops.model.ReturnOrderItem;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.concurrent.CountDownLatch;

@Slf4j
@Service
public class ReturnOrderConsumer {

    @Autowired
    private ReturnOrderService returnOrderService;

    private CountDownLatch latch = new CountDownLatch(1);

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    ReturnUtil returnUtil;

    @Autowired
    ReturnOrderItemRepository returnOrderItemRepository;


    @Trace(dispatcher=true,metricName = "ReturnOrderConsumer")
    @KafkaListener(topics = "${d365.returnorder.kafka.topic}", groupId = "${d365.returnorder.kafka.topic.group}")
    public void listenConsumer1(String message, Acknowledgment ack) throws InvalidRequestException {
        if (shouldProcessData()) {
            processMessage(message);
        }
        latch.countDown();
        ack.acknowledge();
    }

    private boolean shouldProcessData() {
        return returnUtil.getPSlipRetryFlag();
    }

    public void processMessage(String message) {
        ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setDefaultSetterInfo(JsonSetter.Value.forContentNulls(Nulls.AS_EMPTY));
        ReturnRequestMessage returnRequestMessage = null;
        ReturnCreateRequest returnCreateRequest = null;
        ReturnOrderItem returnOrderItem = null;
        log.info("[ReturnOrderConsumer][processMessage] message received : {}", message);
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[ReturnOrderConsumer][processMessage] empty message received");
                return;
            }
            try {
                returnRequestMessage = mapper.readValue(message, ReturnRequestMessage.class);
                if (returnRequestMessage != null) {
                    log.info("[ReturnOrderConsumer][processMessage] Message deserialized successfully for uwItemId : " + returnRequestMessage.getUwItemId());
                    log.info("[ReturnOrderConsumer][processMessage] returnCreateRequest : " + returnRequestMessage.toString());
                    returnCreateRequest = new ReturnCreateRequest();
                    returnCreateRequest.setUwItemId(returnRequestMessage.getUwItemId());
                    if (returnRequestMessage.getReturnOrderItem() != null) {
                        returnOrderItem = new ReturnOrderItem();
                        BeanUtils.copyProperties(returnRequestMessage.getReturnOrderItem(), returnOrderItem);
                        returnCreateRequest.setReturnOrderItem(returnOrderItem);
                    }
                    if(null!=returnRequestMessage.getReturnId()){
                        returnCreateRequest.setReturnId(returnRequestMessage.getReturnId());
                    }
                    if (returnRequestMessage.isRetryFlag()) {
                        log.error("[ReturnOrderConsumer] retry flag found for uwItemId : {}",returnCreateRequest.getUwItemId());
                        returnCreateRequest.setRetryFlag(true);
                    }
                    returnCreateRequest.setInventoryClearanceFlow(returnRequestMessage.isInventoryClearanceFlow());
                }
            } catch (Exception e) {
                log.error("[ReturnOrderConsumer][processMessage] Cannot deserialized the message in consumer class : ",e);
                return;
            }
             if (returnCreateRequest != null) {
                 log.info("[ReturnOrderConsumer][processMessage] uwItemId : {}, returnCreateRequest : {}", returnRequestMessage.getUwItemId(), returnCreateRequest);
                 Integer uwItemId = returnCreateRequest.getUwItemId();
                 log.info("[ReturnOrderConsumer][processMessage] returnCreateRequest in finance-consumer : {}", new Gson().toJson(returnCreateRequest));
                 boolean isLoyaltyService = returnUtil.validateReturnRequest(returnCreateRequest);
                 boolean shouldCreateReturn = returnUtil.shouldCreateReturn(returnCreateRequest);
                 boolean shouldNotUpdateD365Tracking = returnUtil.shouldNotUpdateD365Tracking(returnCreateRequest.getUwItemId());
                 log.info("[ReturnOrderConsumer] isLoyaltyService:{}, shouldCreateReturn:{}, shouldNotUpdateD365Tracking:{} for uwItemId:{}", isLoyaltyService, shouldCreateReturn, shouldNotUpdateD365Tracking, uwItemId);
                 if (shouldNotUpdateD365Tracking) return;
                 if (!isLoyaltyService && shouldCreateReturn) {
                     returnOrderService.createReturnNew(returnCreateRequest);
                 } else {
                     log.error("ReturnOrderConsumer : Unprocessable return found for uwItemId : {}", uwItemId);
                     String responseMessage = "Unprocessable return found";
                     log.error("ReturnOrderConsumer : updating returnTracking for uwItemId : {}", uwItemId);
                     returnUtil.updateD365Tracking(returnCreateRequest, responseMessage);
                 }
             }
        } catch (Exception e) {
            log.error("[ReturnOrderConsumer][processMessage] exception found in finance-consumer for message : {}", message,e);
        }
    }
}