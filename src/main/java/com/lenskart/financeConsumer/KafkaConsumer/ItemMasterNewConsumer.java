package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.financeConsumer.facade.FinanceSourceSystemSyncFacade;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.ItemMasterService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.PayloadBuildUtils;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ItemMasterNewConsumer {

    @Autowired
    private ItemMasterService itemMasterService;

    @Autowired
    private ProductService productService;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private FinanceSourceSystemSyncFacade financeSourceSystemSyncFacade;

    @Autowired
    private PayloadBuildUtils payloadBuildUtils;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("#{'${finance.blocked.pid.list}'.split(',')}")
    private List<String> pidList;

    @Value("${enable.new.item.flow}")
    private String enableNewItemFlow;

    @Trace(dispatcher=true,metricName = "ItemMasterNewConsumer")
    @KafkaListener(topics = Constants.ItemMaster.ItemNewKafkaTopic, groupId = Constants.ItemMaster.ItemNewKafkaGroup)
    public void listenConsumer1(String message, Acknowledgment ack) throws Exception {
        processMessage(message);
        ack.acknowledge();
    }

    public void processMessage(String message) throws Exception {
        boolean enableNewItemFlowFlag = Boolean.parseBoolean(enableNewItemFlow);
        if(!enableNewItemFlowFlag){
            log.debug("[ItemMasterNewConsumer][processMessage] New flow disabled, skipping processing - enableNewItemFlow: {}",
                    enableNewItemFlowFlag);
            return;
        }

        log.info("[ItemMasterNewConsumer][processMessage] Processing item message - hasMessage: {} | newFlowEnabled: {}",
                !StringUtils.isEmpty(message), enableNewItemFlowFlag);
        log.info("[ItemMasterNewConsumer][processMessage] Back Sync consumed message : {}", message);
        try {
            if (StringUtils.isEmpty(message) || pidList.contains(message)) {
                log.error("[ItemMasterConsumer] [listenConsumer1] Null or empty request payload: Invalid Request");
                return;
            }
            Integer productId = objectMapper.readValue(message, Integer.class);
            boolean isValidProduct = itemMasterService.checkIsProduct(productId);
            if(!isValidProduct){
                log.error("[ItemMasterNewConsumer][processMessage] product does not exist or invalid: {} ", productId);
                return;
            }
           /* financeSourceSystemSyncFacade.saveToFinanceSourceSystemSyncIfApplicable(String.valueOf(productId), productId,
                    FinanceSourceSystemSyncEvent.ITEM_MASTER,
                    FinanceSourceSystemSyncEntityType.PRODUCT_ID,
                    null,
                    null
            );*/
            itemMasterService.syncToD365(productId);
        } catch (Exception e) {
            log.error("[ItemMasterConsumer][processMessage]  {} {}", message, e);
        }
    }
}
