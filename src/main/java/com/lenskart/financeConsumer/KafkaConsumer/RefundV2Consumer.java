package com.lenskart.financeConsumer.KafkaConsumer;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.Refund;
import com.lenskart.financeConsumer.dto.d365requests.RefundKafkaRequest;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.RefundService;
import com.lenskart.financeConsumer.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class RefundV2Consumer {

    @Autowired
    RefundService refundService;

    @Autowired
    private GenericClientService genericClientService;

    @KafkaListener(topics = "${d365.refund.v2.kafka.topic:D365RefundV2Topic}", groupId = "${d365.refund.kafka.topic.group:D365RefundV2Group}")
    public void listen(String message, Acknowledgment ack) throws InvalidRequestException {
        processMessage(message);
        ack.acknowledge();
    }

    private void processMessage(String message) {
        Refund refund = null;
        Integer refundId = null;
        try {
            log.info("[RefundConsumer][processMessage] refund received in consumer : {}", message);
            if(StringUtils.isEmpty(message)){
                log.info("[RefundConsumer][processMessage] Invalid refund message");
                return;
            }
            RefundKafkaRequest refundKafkaRequest = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                    .readValue(message, RefundKafkaRequest.class);

            log.info("[RefundConsumer][processMessage] refundKafkaRequest: {}",refundKafkaRequest);
            log.info("[RefundConsumer][processMessage] refundKafkaRequest for refundId {}",refundKafkaRequest.getRefund().getId());
            genericClientService.saveInitLog(String.valueOf(refundKafkaRequest.getRefund().getId()), message,
                    Constants.ORDER_TYPES.REFUND, Constants.Common.InProcess);
            // validating refund
            if (refundKafkaRequest != null) {
                refund = refundKafkaRequest.getRefund();
                refundKafkaRequest.setApiVersion("V2");
                refundId = refund.getId();
            }
            if (!StringUtils.isEmpty(refund.getStatus()) && refund.getStatus().equalsIgnoreCase("refund_reject")) {
                log.info("[RefundConsumer][processMessage] refund_reject status cannot process to D365");
            } else {
                log.info("[RefundConsumer][processMessage] processing refund request to D365 for refundId : {}",refundId);
                refundService.createRefund(refundKafkaRequest);
            }
        }
        catch (Exception e){
            log.error("[RefundConsumer][processMessage] : exception found to process refund request to d365 : {} : ",refundId,e);
        }
    }

}