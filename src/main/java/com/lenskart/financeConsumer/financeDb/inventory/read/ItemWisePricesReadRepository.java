package com.lenskart.financeConsumer.financeDb.inventory.read;

import com.lenskart.core.model.ItemWisePriceDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface ItemWisePricesReadRepository extends CrudRepository<ItemWisePriceDetails, Integer> {

    ItemWisePriceDetails findByItemId(Integer itemId);

    List<ItemWisePriceDetails> findByItemIdIn(List<Integer> itemIds);

    @Query(value = "select iwp.* from exchange_orders eo\n" +
            "inner join uw_orders uw\n" +
            "on eo.uw_item_id = uw.uw_item_id\n" +
            "inner join item_wise_prices iwp\n" +
            "on uw.`item_id` = iwp.`item_id`\n" +
            "where eo.`exchange_increment_id` = ?1", nativeQuery = true)
    ItemWisePriceDetails getPricingDetails(Integer orderId);

    List<ItemWisePriceDetails> findByIncrementId(Integer incrementId);

}
