package com.lenskart.financeConsumer.financeDb.inventory.read;

import com.lenskart.core.model.MpOrderDetails;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface MpOrderDetailsReadRepository extends CrudRepository<MpOrderDetails,Integer> {
    @Query(value = "select * from mp_order_details where increment_id = ?1 limit 1", nativeQuery = true)
    MpOrderDetails findOneByIncrementIdNew(Integer incrementId);
}
