package com.lenskart.financeConsumer.financeDb.writeRepository;

import com.lenskart.financeConsumer.model.financeDb.GrnReport;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface GrnReportRepository extends CrudRepository<GrnReport, Long> {

    @Query(value = "SELECT * FROM grn_report where grn_date >= ?1", nativeQuery = true)
    public List<GrnReport> findAllData(String grnReportFinanceYear, Pageable pageable);

    @Query(value = "SELECT * FROM grn_report WHERE grn_date BETWEEN ?1 AND ?2", nativeQuery = true)
    List<GrnReport> findByGrnDate(LocalDateTime startDate, LocalDateTime endDate);

    @Transactional
    @Modifying
    @Query(value = "UPDATE grn_report SET status = ?3, grn_failure_reason = ?2 WHERE grn_code IN ?1", nativeQuery = true)
    void updateGrnReport(List<String> grnCode, String response, String status);

    List<GrnReport> findByGrnDateBetweenAndStatus(LocalDateTime startDate, LocalDateTime endDate, String status);

    @Query(value = "SELECT * FROM grn_report where grn_date >= ?1 and vendor_id in ?2", nativeQuery = true)
    public List<GrnReport> findAllDataByVendorId(String grnReportFinanceYear, List<String> vendorIds, Pageable pageable);
}
