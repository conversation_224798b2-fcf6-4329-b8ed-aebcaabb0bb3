package com.lenskart.financeConsumer.financeDb.writeRepository;

import com.lenskart.financeConsumer.model.financeDb.EventRules;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RuleEngineRepository extends JpaRepository<EventRules, Long> {
    @Query(value = "SELECT * FROM event_rules WHERE EVENT =:event", nativeQuery = true)
    EventRules findRulesByEvent(@Param("event") String event);

    @Query(value = "SELECT DISTINCT EVENT FROM events", nativeQuery = true)
    List<String> findAllEvents();
}
