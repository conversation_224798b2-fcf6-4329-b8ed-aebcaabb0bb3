package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.HubMaster;
import com.lenskart.financeConsumer.dao.*;
import com.lenskart.financeConsumer.dto.d365requests.PurchaseOrderDTOs.PurchaseOrderContract;
import com.lenskart.financeConsumer.dto.d365requests.PurchaseOrderDTOs.PurchaseOrderHeader;
import com.lenskart.financeConsumer.dto.d365requests.PurchaseOrderDTOs.PurchaseOrderLine;
import com.lenskart.financeConsumer.model.*;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.POCreationService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.wm.model.JitOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class POCreationServiceImpl implements POCreationService {

    private static final Logger logger = LoggerFactory.getLogger(POCreationServiceImpl.class.getSimpleName());

    @Value("${financeAdapter.url}")
    private String financeAdapterUrl;

    @Value("${D365.forward.url}")
    private String D365BaseUrl;

    @Autowired
    private PurchaseOrderRepository purchaseOrderRepository;

    @Autowired
    private PurchaseOrderItemRepository purchaseOrderItemRepository;

    @Autowired
    private HubMasterRepository hubMasterRepository;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private StockSuppliersRepository stockSuppliersRepository;

    @Autowired
    private StockInPODetailsRepository stockinPODetailsRepository;

    @Autowired
    private StockInPoItemDetailsRepository stockInPoItemDetailsRepository;

    @Autowired
    private StockInInvoiceDetailsRepository stockInInvoiceDetailsRepository;

    @Autowired
    private ProductService productService;

    @Autowired
    private ClassificationService classificationService;

    @Autowired
    private PoGrnRepository poGrnRepository;

    @Autowired
    private JitOrderRepository jitOrderRepository;

    @Autowired
    private PoDataRepository poDataRepository;

    @Override
    public void manualPOCreation(String poCode, boolean isJIT) throws Exception{
        PurchaseOrderContract purchaseOrderContract = new PurchaseOrderContract();
        try{
            if(isJIT){
                logger.info("PO is jit order so cannot process it further "+poCode);
                return;
            }
            PoGrn pogrn = poGrnRepository.getPoGrnDetails(poCode);
            logger.info("[POCreationServiceImpl][manualPOCreation] purchaseOrder : {}", pogrn);
            if(pogrn == null){
                logger.info("Purchase order not present");
                throw new Exception("exception");
            }
            List<JitOrder> jitOrderList = jitOrderRepository.getJitOrder(pogrn.getUnicomPoId());
            PurchaseOrderHeader purchaseOrderHeader = getMaunualPurchaseOrderHeader(pogrn.getUnicomPoId(), jitOrderList.get(0));

            logger.info("[POCreationServiceImpl][manualPOCreation] purchaseOrderItem : {}", jitOrderList);
            List<PurchaseOrderLine> purchaseOrderLineList = getManualPurchaseOrderLines(jitOrderList, jitOrderList.get(0).getFacilityCode(), isJIT);
            logger.info("[POCreationServiceImpl][manualPOCreation] purchaseOrderLine : {}", purchaseOrderLineList);
            purchaseOrderHeader.setPurchaseLineList(purchaseOrderLineList);
            purchaseOrderContract.setContract(purchaseOrderHeader);
            ResponseEntity responseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.PurchaseOrderMaster.CREATE_URL,
                    new HttpHeaders(), HttpMethod.POST, purchaseOrderContract);
            logger.info("[POCreationServiceImpl][manualPOCreation] response "+ responseEntity.getBody());
        } catch (Exception ex){
            genericClientService.saveLog(String.valueOf(poCode),genericClientService.getErrorMessage(ex) , purchaseOrderContract,D365BaseUrl+Constants.PurchaseOrderMaster.URL,Constants.PurchaseOrderMaster.OrderType, Constants.Common.FAILURE);
            logger.error("[POCreationServiceImpl][manualPOCreation] Exception : {}", ex.getMessage());
            logger.error(ex.getMessage(), ex);
        }
    }

    private PurchaseOrderHeader getMaunualPurchaseOrderHeader(String purchaseOrderId, JitOrder jitOrder) throws JsonProcessingException {
        PurchaseOrderHeader purchaseOrderHeader = new PurchaseOrderHeader();
        HubMaster hubMaster = getHubMaster(jitOrder.getFacilityCode());
        StockSuppliers stockSuppliers = getStockSuppliers(null, jitOrder.getSupplierId());
        Date currentDate = new Date();

        if(hubMaster.getCountry().equalsIgnoreCase(stockSuppliers.getCountryId())){
            purchaseOrderHeader.setImportOrder(Constants.PurchaseOrderConstant.NO_IMPORT_ORDER);
        } else {
            purchaseOrderHeader.setImportOrder(Constants.PurchaseOrderConstant.YES_IMPORT_ORDER);
        }

        purchaseOrderHeader.setPurchaseOrderNumber(purchaseOrderId);
        purchaseOrderHeader.setPurchaseType(Constants.PurchaseOrderConstant.PURCHASE_TYPE);
        purchaseOrderHeader.setDeliveryName(hubMaster.getName());
        StringBuilder address = new StringBuilder(hubMaster.getAddress1());
        if(!StringUtils.isEmpty(hubMaster.getAddress2())){
            address.append(" ").append(hubMaster.getAddress2());
        }
        purchaseOrderHeader.setDeliveryAddress(address.toString());
        purchaseOrderHeader.setCurrency(ProductUtil.getCurrencyCode(hubMaster.getCountry()));
        SimpleDateFormat dt = new SimpleDateFormat("yyyy-MM-dd");
        purchaseOrderHeader.setDeliveryDate(dt.format(jitOrder.getPoCreatedDate()));
        purchaseOrderHeader.setWarehouse(jitOrder.getFacilityCode());
        purchaseOrderHeader.setCreatedBy(dt.format(jitOrder.getPoCreatedDate()));
        dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        purchaseOrderHeader.setCreatedAt(dt.format(new Date()));
        purchaseOrderHeader.setUnits(jitOrder.getFacilityCode());
        if(jitOrder.getFacilityCode().equalsIgnoreCase("LKH03") || jitOrder.getFacilityCode().equalsIgnoreCase("DK02")){
            purchaseOrderHeader.setLegalEntity(Constants.LegalEntity.INDIA_LEGAL_ENTITY);
            purchaseOrderHeader.setCostCentre("10025");
        } else if(jitOrder.getFacilityCode().equalsIgnoreCase("LKST607") || jitOrder.getFacilityCode().equalsIgnoreCase("SGDK01")){
            purchaseOrderHeader.setLegalEntity(Constants.LegalEntity.SG_LEGAL_ENTITY);
            purchaseOrderHeader.setCostCentre("10016");
        }
        purchaseOrderHeader.setVendorNumber(stockSuppliers.getSupplierCode());
        purchaseOrderHeader.setInvoiceAccount(stockSuppliers.getSupplierCode());
        return purchaseOrderHeader;
    }

    private List<PurchaseOrderLine> getManualPurchaseOrderLines(List<JitOrder> purchaseOrderItemList, String facilityCode, boolean isJIT) throws Exception {
        List<PurchaseOrderLine> purchaseOrderLineList = new ArrayList<>();
        HashMap<Integer, PurchaseOrderLine> productIdCountMap = new HashMap<>();
        for(JitOrder purchaseOrderItem : purchaseOrderItemList) {
            if(productIdCountMap.get(purchaseOrderItem.getProductId()) != null){
                PurchaseOrderLine purchaseOrderLine = productIdCountMap.get(purchaseOrderItem.getProductId());
                purchaseOrderLine.setQuantity(purchaseOrderLine.getQuantity()+1);
                productIdCountMap.put(purchaseOrderItem.getProductId(), purchaseOrderLine);
            } else {
                PurchaseOrderLine purchaseOrderLine = new PurchaseOrderLine();
                purchaseOrderLine.setPurchaseOrderNumber(purchaseOrderItem.getUnicomPoId());
                purchaseOrderLine.setItemNumber(purchaseOrderItem.getProductId());
                purchaseOrderLine.setLineNumber(purchaseOrderItem.getProductId());
                purchaseOrderLine.setQuantity(1);
                PoData poData = poDataRepository.findTopByProductId(purchaseOrderItem.getProductId());
                purchaseOrderLine.setUnitPrice(poData.getTotalAmount());
                purchaseOrderLine.setUnit("Pcs");
                purchaseOrderLine.setWarehouse(facilityCode);
                purchaseOrderLine.setUnits(facilityCode);
                if(isJIT){
                    purchaseOrderLine.setCostCentre("10025");
                }
                if(facilityCode.equalsIgnoreCase("LKST607") || facilityCode.equalsIgnoreCase("SGDK01")){
                    purchaseOrderLine.setCostCentre("10016");
                }
                String brandVal = getBrand(purchaseOrderItem.getProductId());
                String brand = brandVal.equalsIgnoreCase("Free") ? Constants.JITOrderBrand.LENSKART_BRAND : brandVal;
                purchaseOrderLine.setBrand(brand);
                purchaseOrderLine.setItemClassification(getItemClassification(purchaseOrderItem.getProductId()));
                productIdCountMap.put(purchaseOrderItem.getProductId(), purchaseOrderLine);
            }
        }
        for(Map.Entry<Integer, PurchaseOrderLine> entry : productIdCountMap.entrySet()){
            purchaseOrderLineList.add(entry.getValue());
        }
        return purchaseOrderLineList;
    }

    @Override
    public void autoPOCreation(String poCode, boolean isJIT) throws Exception{
        PurchaseOrderContract purchaseOrderContract = new PurchaseOrderContract();
        try{
            StockinPODetails stockinPODetails = stockinPODetailsRepository.getStockInPODetailsByPOCode(poCode);
            logger.info("[POCreationServiceImpl][autoPOCreation] purchaseOrder : {}", stockinPODetails.toString());
            if(stockinPODetails == null){
                logger.info("Purchase order not present");
                return;
            }
            PurchaseOrderHeader purchaseOrderHeader = getAutoPurchaseOrderHeader(stockinPODetails);
            List<StockinPoItemDetails> stockinPoItemDetailsList = stockInPoItemDetailsRepository.getStockInPOItemDetailsListUsingPoCode(poCode);
            List<StockInInvoiceDetails> stockInInvoiceDetailsList = stockInInvoiceDetailsRepository.getStockInInvoiceDetailsByInvoiceNumber(stockinPODetails.getInvoiceNumber());
            HashMap<Integer, Double> unitPriceMap = new HashMap<>(stockInInvoiceDetailsList.size());
            for(StockInInvoiceDetails stockInInvoiceDetail : stockInInvoiceDetailsList){
                unitPriceMap.put(stockInInvoiceDetail.getMagentoItemId(), stockInInvoiceDetail.getUnitPrice());
            }
            logger.info("[POCreationServiceImpl][autoPOCreation] purchaseOrderItem : {}", stockinPoItemDetailsList);
            List<PurchaseOrderLine> purchaseOrderLineList = getAutoPurchaseOrderLines(stockinPoItemDetailsList, stockinPODetails.getFacilityCode(), unitPriceMap, isJIT);
            logger.info("[POCreationServiceImpl][autoPOCreation] purchaseOrderLine : {}", purchaseOrderLineList);
            purchaseOrderHeader.setPurchaseLineList(purchaseOrderLineList);
            purchaseOrderContract.setContract(purchaseOrderHeader);
            ResponseEntity responseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.PurchaseOrderMaster.CREATE_URL,
                    new HttpHeaders(), HttpMethod.POST, purchaseOrderContract);
            logger.info("[POCreationServiceImpl][autoPOCreation] response "+ responseEntity.getBody());
        } catch (Exception ex){
            genericClientService.saveLog(String.valueOf(poCode),genericClientService.getErrorMessage(ex) ,
                    purchaseOrderContract,D365BaseUrl+Constants.PurchaseOrderMaster.URL,
                    Constants.PurchaseOrderMaster.OrderType, Constants.Common.FAILURE);
            logger.error("[POCreationServiceImpl][autoPOCreation] Exception : {}", ex.getMessage());
            logger.error(ex.getMessage(), ex);
        }
    }

    private PurchaseOrderHeader getAutoPurchaseOrderHeader(StockinPODetails stockinPODetails) throws JsonProcessingException {
        PurchaseOrderHeader purchaseOrderHeader = new PurchaseOrderHeader();
        HubMaster hubMaster = getHubMaster(stockinPODetails.getFacilityCode());
        StockSuppliers stockSuppliers = getStockSuppliers(null, stockinPODetails.getSupplierId());
        Date currentDate = new Date();
        purchaseOrderHeader.setVendorNumber(stockSuppliers.getSupplierCode());
        if(hubMaster.getCountry().equalsIgnoreCase(stockSuppliers.getCountryId())){
           purchaseOrderHeader.setImportOrder(Constants.PurchaseOrderConstant.NO_IMPORT_ORDER);
        } else {
           purchaseOrderHeader.setImportOrder(Constants.PurchaseOrderConstant.YES_IMPORT_ORDER);
        }

        purchaseOrderHeader.setLegalEntity(Constants.LegalEntity.INDIA_LEGAL_ENTITY);
        purchaseOrderHeader.setPurchaseOrderNumber(stockinPODetails.getPoCode());
        purchaseOrderHeader.setPurchaseType(Constants.PurchaseOrderConstant.PURCHASE_TYPE);
        purchaseOrderHeader.setDeliveryName(hubMaster.getName());
        StringBuilder address = new StringBuilder(hubMaster.getAddress1());
        if(!StringUtils.isEmpty(hubMaster.getAddress2())){
            address.append(" ").append(hubMaster.getAddress2());
        }
        purchaseOrderHeader.setDeliveryAddress(address.toString());
        purchaseOrderHeader.setCurrency(ProductUtil.getCurrencyCode(hubMaster.getCountry()));
        purchaseOrderHeader.setInvoiceAccount(stockSuppliers.getSupplierCode());
        SimpleDateFormat dt = new SimpleDateFormat("MM/dd/yyyy");
        purchaseOrderHeader.setDeliveryDate(dt.format(currentDate));
        purchaseOrderHeader.setWarehouse(stockinPODetails.getFacilityCode());
        purchaseOrderHeader.setCreatedBy("athena system");
        dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        purchaseOrderHeader.setCreatedAt(dt.format(new Date()));
        purchaseOrderHeader.setUnits(stockinPODetails.getFacilityCode());
        purchaseOrderHeader.setCostCentre("10025");
        purchaseOrderHeader.setBrand(Constants.JITOrderBrand.LENSKART_BRAND);
        return purchaseOrderHeader;
    }

    private List<PurchaseOrderLine> getAutoPurchaseOrderLines(List<StockinPoItemDetails> stockinPoItemDetailsList, String facilityCode, HashMap<Integer, Double> unitPriceMap, boolean isJIT) throws Exception {
        List<PurchaseOrderLine> purchaseOrderLineList = new ArrayList<>();
        HashMap<Integer, PurchaseOrderLine> productIdCountMap = new HashMap<>();
        for(StockinPoItemDetails stockinPoItemDetails : stockinPoItemDetailsList) {
            if(productIdCountMap.get(stockinPoItemDetails.getPoSkuId()) != null){
                PurchaseOrderLine purchaseOrderLine = productIdCountMap.get(stockinPoItemDetails.getPoSkuId());
                purchaseOrderLine.setQuantity(purchaseOrderLine.getQuantity()+1);
                productIdCountMap.put(stockinPoItemDetails.getPoSkuId(), purchaseOrderLine);
            } else {
                PurchaseOrderLine purchaseOrderLine = new PurchaseOrderLine();
                purchaseOrderLine.setPurchaseOrderNumber(stockinPoItemDetails.getPoCode());
                purchaseOrderLine.setItemNumber(stockinPoItemDetails.getPoSkuId());
                purchaseOrderLine.setLineNumber(stockinPoItemDetails.getPoSkuId());
                purchaseOrderLine.setQuantity(1);
                purchaseOrderLine.setUnitPrice(unitPriceMap.get(stockinPoItemDetails.getUwItemId()));
                purchaseOrderLine.setUnit("Pcs");
                purchaseOrderLine.setWarehouse(facilityCode);
                purchaseOrderLine.setCostCentre("10025");
                purchaseOrderLine.setUnits(facilityCode);
                purchaseOrderLine.setBrand(Constants.JITOrderBrand.LENSKART_BRAND);
                purchaseOrderLine.setItemClassification(getItemClassification(stockinPoItemDetails.getPoSkuId()));
                productIdCountMap.put(stockinPoItemDetails.getPoSkuId(), purchaseOrderLine);
            }
        }
        for(Map.Entry<Integer, PurchaseOrderLine> entry : productIdCountMap.entrySet()){
            purchaseOrderLineList.add(entry.getValue());
        }
        return purchaseOrderLineList;
    }

    private HubMaster getHubMaster(String facilityCode) throws JsonProcessingException {
        String hubMasterValue = (String) redisTemplate.opsForValue().get("PO_"+facilityCode);
        HubMaster hubMaster = null;
        if(StringUtils.isEmpty(hubMasterValue)){
            hubMaster = hubMasterRepository.findByFacilityCode(facilityCode);
            hubMasterValue = new ObjectMapper().writeValueAsString(hubMaster);
            redisTemplate.opsForValue().set("PO_"+facilityCode, hubMasterValue, 24, TimeUnit.HOURS);
        } else {
            hubMaster = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(hubMasterValue, HubMaster.class);
        }
        return hubMaster;
    }

    private StockSuppliers getStockSuppliers(String vendorCode, int supplierId) throws JsonProcessingException {
        StockSuppliers stockSuppliers = null;
        if(!StringUtils.isEmpty(vendorCode)){
            String stockSuppliersValue = (String) redisTemplate.opsForValue().get(vendorCode);
            if(StringUtils.isEmpty(stockSuppliersValue)){
                stockSuppliers = stockSuppliersRepository.findBySupplierCode(vendorCode);
                stockSuppliersValue = new ObjectMapper().writeValueAsString(stockSuppliers);
                redisTemplate.opsForValue().set(vendorCode, stockSuppliersValue, 24 , TimeUnit.HOURS);
            } else {
                stockSuppliers = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(stockSuppliersValue, StockSuppliers.class);
            }
        } else {
            String stockSuppliersValue = (String) redisTemplate.opsForValue().get("StockSupplier_"+supplierId);
            if(StringUtils.isEmpty(stockSuppliersValue)){
                stockSuppliers = stockSuppliersRepository.getStockSupplierBySupplierId(supplierId);
                stockSuppliersValue = new ObjectMapper().writeValueAsString(stockSuppliers);
                redisTemplate.opsForValue().set("StockSupplier_"+supplierId, stockSuppliersValue, 24 , TimeUnit.HOURS);
            } else {
                stockSuppliers = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(stockSuppliersValue, StockSuppliers.class);
            }
        }
        return stockSuppliers;
    }

    private String getItemClassification(int productId) throws Exception {
        String itemClassificationValue = (String) redisTemplate.opsForValue().get("ItemClassification_"+productId);
        if(StringUtils.isEmpty(itemClassificationValue)){
            int classificationId = productService.getProduct(productId).getClassification();
            itemClassificationValue = classificationService.getClassification(classificationId).getDisplayName();
            redisTemplate.opsForValue().set("ItemClassification_"+productId, itemClassificationValue, 24 , TimeUnit.HOURS);
        }
        return itemClassificationValue;
    }

    private String getBrand(int productId) throws Exception {
        return productService.getProduct(productId).getBrand();
    }

}
