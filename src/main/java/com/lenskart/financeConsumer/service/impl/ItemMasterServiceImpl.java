package com.lenskart.financeConsumer.service.impl;

import com.lenskart.core.model.Classification;
import com.lenskart.core.model.Product;
import com.lenskart.financeConsumer.constant.Common;
import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dao.mongo.FinanceConsumerLogsRepository;
import com.lenskart.financeConsumer.dto.d365requests.D365RequestDto;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemSyncDto;
import com.lenskart.financeConsumer.dto.d365requests.ItemMasterDTO;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.financeDb.writeRepository.ItemMasterRepository;
import com.lenskart.financeConsumer.model.financeDb.ItemD365Sync;
import com.lenskart.financeConsumer.model.financeDb.ItemMaster;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.model.mongo.D365ApiLog;
import com.lenskart.financeConsumer.model.mongo.FinanceConsumerLogs;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.ItemMasterService;
import com.lenskart.financeConsumer.service.PackageProductMappingService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.LensDetailsUtil;
import com.lenskart.financeConsumer.util.PayloadBuildUtils;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;

@Slf4j
@Service
public class ItemMasterServiceImpl implements ItemMasterService{

    @Autowired
    private PayloadBuildUtils payloadBuildUtils;

    @Value("${d365.item.sync.ignore.classification.ids}")
    private List<String> itemServiceList;

    @Value("${d365.non-serialised.classification.ids}")
    private Set<Integer> nonSerialisedClassificationIds;

    @Value("${d365.service.classification.ids}")
    private Set<Integer> serviceClassificationIds;

    @Value("${hsnCode.regex}")
    private String hsnCodeRegex;
    @Autowired
    FinanceSourceSystemToItemD365SyncDataSyncService financeSourceSystemToItemD365SyncDataSyncService;

    @Value("${enable.new.item.flow}")
    private String enableNewItemFlow;

    @Value("${d365.itemmaster.kafka.topic}")
    private String itemMasterTopic;
    @Value("${d365.itemmaster.new.kafka.topic}")
    private String itemMasterNewTopic;

    @Value("${d365.itemmaster.new.totalRecordsLimit}")
    private int totalRecordsLimit;

    @Value("${d365.itemmaster.new.failureRetryLimit}")
    private int itemFailureRetryLimit;
    @Value("${d365.itemmaster.daysRangeAllowed}")
    private int dayRangeAllowed;

    @Value("${d365.itemmaster.query.pageSize}")
    private int pageSize;
    @Autowired
    private ItemMasterRepository itemMasterRepository;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ClassificationService classificationService;

    @Autowired
    private  LensDetailsUtil lensDetailsUtil;

    @Autowired
    private PackageProductMappingService packageProductMappingService;


    @Autowired
    private D365LogRepository d365LogRepository;

    @Autowired
    private FinanceConsumerLogsRepository financeConsumerLogsRepository;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Autowired
    ItemD365RepositoryService itemD365RepositoryService;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;
    @Value("${financeAdapter.url}")
    private String financeAdapterUrl;

    @Value("${d365.itemmaster.kafka.topic}")
    private String ItemMasterTopic;
    @Value("${isItemMasterNewEntryPersistingBlocked}")
    private boolean isItemMasterNewEntryPersistingBlocked;
    @Value("${D365.forward.url}")
    private String D365BaseUrl;



    public ResponseEntity syncToD365(Integer productId){
        try {
            ResponseEntity responseEntity;
            if (Objects.isNull(productId)) {
                log.error("ItemMasterServiceImpl : null productId cannot be processed!");
                return new ResponseEntity("null productId cannot be processed!", HttpStatus.BAD_REQUEST);
            }
                Object payload = generatePayload(productId);
                try {
                    D365RequestDto requestDto= D365RequestDto.builder()
                            .requestPayload(payload)
                            .id(persisteAndGetIdFromD365ForwardSyncTableForProduct(productId))
                            .event(FinanceSourceSystemSyncEvent.ITEM_MASTER.toString())
                            .build();
                    responseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.ItemMaster.CREATE_URL, new HttpHeaders(), HttpMethod.POST, requestDto);
                }
                catch (Exception e){
                    genericClientService.saveLog(String.valueOf(productId),genericClientService.getErrorMessage(e),payload,D365BaseUrl +Constants.ItemMaster.URL,Constants.ItemMaster.OrderType,Constants.Common.FAILURE);
                    throw e;
                }
                if(!responseEntity.getStatusCode().equals(HttpStatus.OK)){
                    HashMap responseBody = (HashMap) responseEntity.getBody();
                    String message = (String) responseBody.get("Message");
                    genericClientService.saveLog(String.valueOf(productId),message,payload,D365BaseUrl +Constants.ItemMaster.URL,Constants.ItemMaster.OrderType,Constants.Common.FAILURE);
                    throw new RuntimeException("Didn't get Http 200 response from finance-adaptor for productId : "+ productId);    // for gateway , client timeouts we need to mark failure
                }
                HashMap responseBody = (HashMap) responseEntity.getBody();
                String message = (String) responseBody.get("Message");
                Boolean responseStatus = Boolean.valueOf(String.valueOf(responseBody.get("Success")));
                String status;
                if (responseStatus) {
                    status = Constants.Common.SUCCESS;
                    genericClientService.saveLog(String.valueOf(productId),message,payload,D365BaseUrl +Constants.ItemMaster.URL,Constants.ItemMaster.OrderType,status);
                }
                return responseEntity;
        }
        catch(Exception e){
            itemD365RepositoryService.markSyncAsFailure(productId.toString(),
                    FinanceSourceSystemSyncEvent.ITEM_MASTER, FinanceSourceSystemSyncEntityType.PRODUCT_ID,
                    genericClientService.getErrorMessage(e));
            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            Map<String,Object> body = new HashMap<String,Object>();
            body.put("Message",genericClientService.getErrorMessage(e));
            body.put("Success",false);
            ResponseEntity response = new ResponseEntity(body,header, HttpStatus.INTERNAL_SERVER_ERROR);
            log.error("[ItemMasterServiceImpl][syncToD365] error {} {}",productId,e);
            return response;
        }
    }

    private long persisteAndGetIdFromD365ForwardSyncTableForProduct(Integer productId){
        ItemD365Sync itemD365Sync =itemD365RepositoryService.findItemByProductId(productId);
        if(Objects.isNull(itemD365Sync)){
            LocalDateTime currentDate = LocalDateTime.now();
            itemD365Sync = ItemD365Sync.builder().
                    referenceId(0L).
                    productId(productId).
                    createdAt(currentDate).
                    createdBy(Constants.Common.financeConsumer).
                    updatedAt(currentDate).
                    updatedBy(Constants.Common.financeConsumer).
                    eventTime(currentDate).
                    retryCount(0).
                    build();
        }
        itemD365Sync.setD365SyncStatus(Common.IN_PROGRESS);     // entries about to be synced in consumer marked as inprogress
        itemD365Sync = itemD365RepositoryService.save(itemD365Sync);
        return itemD365Sync.getId();
    }

    /***
     * method for syncing Item-master productIds to D365 new instance .
     * this hits finance-adaptor api.
     * @param productId
     * @return
     */
    @SneakyThrows
    public ResponseEntity syncItemToD365(Integer productId) {
        productService.validateProductId(productId);
        ItemMaster itemMaster = itemMasterRepository.findByProductId(productId);
        if(isItemMasterNewEntryPersistingBlocked && ObjectUtils.isEmpty(itemMaster)){
            throw new RecordNotFoundException("item-master record not available in the system", HttpStatus.BAD_REQUEST);
        }
        if(ObjectUtils.isEmpty(itemMaster)){
            itemMaster = saveItemMasterIfNotExists(productId);
        }else if(Constants.ItemMaster.successStatus.equals(itemMaster.getD365SyncStatus())){
           return null;
        }
        itemMaster = markItemInProgress(itemMaster);
        try{
            ItemMasterDTO payload = generatePayload(productId, true);
            D365RequestDto requestDto = D365RequestDto.builder()
                    .id(itemMaster.getId())
                    .event(FinanceSourceSystemSyncEvent.ITEM_MASTER.toString())
                    .requestPayload(payload)
                    .build();
            log.info("ItemMasterServiceImpl : posting item sync request for productId : {}", productId);
            ResponseEntity responseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.ItemMaster.CREATE_URL_NEW, new HttpHeaders(), HttpMethod.POST, requestDto);
            persistResultForItemMasterSyncIfFailed(responseEntity, itemMaster);
            return responseEntity;
        }catch (Exception e){
            log.error("ItemMasterServiceImpl : Exception occured while creating item-master payload for productId : {}  , Exception is : {}",productId,e);
            persistExceptionForItemMasterPayloadCreation(genericClientService.getErrorMessage(e), itemMaster);
            return null;
        }
    }

    private ItemMaster saveItemMasterIfNotExists(Integer productId) {
            ItemMaster itemMasterEntity = ItemMaster.builder()
                    .productId(productId)
                    .d365SyncStatus("NEW")
                    .createdBy(Constants.Common.financeConsumer)
                    .updatedBy(Constants.Common.financeConsumer)
                    .build();
            return itemMasterRepository.save(itemMasterEntity);
    }
    private ItemMaster markItemInProgress(ItemMaster itemMaster) {
        itemMaster.setD365SyncStatus(Constants.Common.InProgress);
        return itemMasterRepository.save(itemMaster);
    }


    private void persistResultForItemMasterSyncIfFailed(ResponseEntity responseEntity , ItemMaster itemMaster){
        HashMap<String,Object> response = (HashMap<String,Object>) (responseEntity.getBody());
        String responseMessage = response.get("Message").toString();
        Boolean responseStatus = Boolean.valueOf(String.valueOf(response.get("Success")));
        if(BooleanUtils.isFalse(responseStatus)){
            itemMaster.setErrorMessage(responseMessage);
            itemMaster.setD365SyncStatus("FAILED");
            itemMaster.setUpdatedBy("finance-consumer");
            itemMasterRepository.save(itemMaster);
        }
    }
    private void persistExceptionForItemMasterPayloadCreation(String errorMessage , ItemMaster itemMaster){
            itemMaster.setErrorMessage(errorMessage);
            itemMaster.setD365SyncStatus("FAILED");
            itemMaster.setUpdatedBy("finance-consumer");
            itemMasterRepository.save(itemMaster);
    }

    public boolean checkIsProduct(Integer productId) throws RecordNotFoundException {
        Product product = productService.getProduct(productId);
        if(product == null){
            log.error("Product does not exist {} ",productId);
            return false;
        }
        if (Objects.nonNull(itemServiceList) && itemServiceList.contains(String.valueOf(product.getClassification()))){
            //loyalty service and insurance check
            return false;
        }
        return true;
    }


    public Object generatePayload(Integer productId)throws Exception {
        return generatePayload(productId,false);
    }

    private ItemMasterDTO generatePayload(Integer productId, boolean isNewInstance) throws Exception {
        ItemMasterDTO payload = new ItemMasterDTO();
        try {
            setProductDetails(payload, productId, isNewInstance);
            if (isNewInstance) {
                payload.setTrackingDimensionGroup("None");
            }
            return payload;
        } catch (Exception e) {
            log.error("error in generate payload item master productId {}", productId, e);
            throw e;
        }
    }

    public void setProductDetails(ItemMasterDTO payload, Integer productId,boolean isNewInstance)throws Exception {
          try {
              Product product = productService.getProduct(productId);
              if(product!=null) {
                  int classificationId = product.getClassification();
                  Classification classification = classificationService.getClassification(classificationId);
                  payload.setItemNumber(StringUtils.defaultString(String.valueOf(productId)).trim());
                  payload.setProductName(StringUtils.defaultString(product.getValue()).trim());
                  payload.setProductSearchName(StringUtils.defaultString(String.valueOf(productId)).trim());
                  payload.setDescription(StringUtils.defaultString(product.getValue()).trim());
                  setProductAdditionalDetails(payload, classificationId);
                  setUnitIds(payload);
                  String hsncode = ProductUtil.getHSNCode(product.getHsnCode());
                  setProductTypeAndItemGroup(payload, classificationId, hsncode);
                  setTrackingDimensionGroup(payload,classificationId);
                  payload.setBrand(StringUtils.defaultString(product.getBrand()).trim());
                  payload.setImageUrl("https://static.lenskart.com/media/catalog/product" + StringUtils.defaultString(product.getProductImage()).trim());
                  payload.setFrameType(StringUtils.defaultString(product.getFrameType()).trim());
                  payload.setMsl(StringUtils.defaultString(String.valueOf(product.getMsl())).trim());
                  lensDetailsUtil.setLensDetails(payload, productId, classificationId);
                  payload.setEnable(StringUtils.defaultString(String.valueOf(product.getEnable())).trim());
                  payload.setTransportationFlag(StringUtils.defaultString(product.getTransportationFlag()).trim());
                  ProductUtil.setFinancialDimensions(payload, classificationId, classification.getDisplayName(), "");
                  log.info("[ItemMasterServiceImpl][setProductDetails] payload {} {}",productId, payload);
              }
          }
          catch(Exception e){
              if(!isNewInstance) {
                  log.error("[ItemMasterServiceImpl][setProductDetails] error {} {}", productId, e);
                  genericClientService.saveLog(String.valueOf(productId), genericClientService.getErrorMessage(e), payload, D365BaseUrl + Constants.ItemMaster.URL, Constants.ItemMaster.OrderType, Constants.Common.FAILURE);
                  payloadBuildUtils.acknowdegeFailureToSourceSystem(String.valueOf(productId), Constants.ItemMaster.OrderType, e, null );
              }
              throw e;
          }
    }

    public void setProductAdditionalDetails(ItemMasterDTO payload, Integer classificationId) {
            try {
                    payload.setStorageDimensionGroup(Constants.ItemMaster.StorageDimensionGroup);
                    payload.setProductSubType(Constants.ItemMaster.productSubType);
                payload.setCustomsImportTariffCode(null);
                payload.setCustomsExportTariffCode(null);
                payload.setLegalEntity(Constants.Common.LegalEntityLenskart);
                payload.setDepth(0.0);
                payload.setWidth(0.0);
                payload.setHeight(0.0);
                  }
            catch(Exception e){
                genericClientService.saveLog(String.valueOf(payload.getItemNumber()),genericClientService.getErrorMessage(e),payload,D365BaseUrl +Constants.ItemMaster.URL,Constants.ItemMaster.OrderType,Constants.Common.FAILURE);
                log.error("[setProductAdditionalDetails] Exception occurred for item number {} {}", String.valueOf(payload.getItemNumber()),e);
            }
    }

    public void setUnitIds(ItemMasterDTO payload) {
            payload.setInventoryUnitId(Constants.ItemMaster.UnitId);
            payload.setPurchaseUnitId(Constants.ItemMaster.UnitId);
            payload.setSalesUnitId(Constants.ItemMaster.UnitId);
            payload.setBomUnitId(Constants.ItemMaster.UnitId);
    }


    private void setProductTypeAndItemGroup(ItemMasterDTO payload, Integer classificationId, String hsnCode) {
        Pattern pattern = Pattern.compile(hsnCodeRegex);
        if (!CollectionUtils.isEmpty(serviceClassificationIds) && serviceClassificationIds.contains(classificationId) || pattern.matcher(hsnCode).find()) {
            payload.setProductType(Constants.ItemMaster.ServiceProductType );
            payload.setItemGroup(Constants.ItemMaster.ItemGroupService);
            payload.setSACCode(hsnCode.substring(0,6));
            payload.setItemModelGroup(Constants.ItemMaster.ItemModelGroupService);
        } else {
            payload.setHsnCode(hsnCode);
            payload.setProductType(Constants.ItemMaster.ItemProductType );
            payload.setItemGroup(Constants.ItemMaster.ItemGroup);
            payload.setItemModelGroup(Constants.ItemMaster.ItemModelGroup);
        }
    }

    private void setTrackingDimensionGroup(ItemMasterDTO payload, Integer classificationId) {
        if (!CollectionUtils.isEmpty(nonSerialisedClassificationIds) && nonSerialisedClassificationIds.contains(classificationId)) {
            payload.setTrackingDimensionGroup("None");
        } else {
            payload.setTrackingDimensionGroup("Serial");
        }
    }

    @Override
    public void retryItemMaster(Date startDate, Date endDate, Integer retryCount) throws Exception{
        try {
            log.info("[retryItemMaster] starting time : {}", new Date());
            int page = 0;
            List<D365ApiLog> d365ApiLogList;
            List<FinanceConsumerLogs> financeConsumerLogsList;
            Pageable pageable = PageRequest.of(page, 10);
            do {
                d365ApiLogList = d365LogRepository.findRetryOrders(D365BaseUrl + Constants.ItemMaster.URL, Constants.ItemMaster.OrderType, Constants.Common.FAILURE, startDate, endDate, pageable);
                for (D365ApiLog d365ApiLog : d365ApiLogList) {
                    if (retryCount != 0) {
                        if (d365ApiLog.getRetry_count() <= retryCount) {
                            log.info("[retryItemMaster] entity Id: " + d365ApiLog.getEntityId());
                            kafkaProducerTemplate.send(ItemMasterTopic, d365ApiLog.getEntityId());
                        }
                    } else {
                        log.info("[retryItemMaster] entity Id: " + d365ApiLog.getEntityId());
                        kafkaProducerTemplate.send(ItemMasterTopic, d365ApiLog.getEntityId());
                    }
                }
                page++;
            } while (d365ApiLogList.size()!=0);

            do{
                financeConsumerLogsList = financeConsumerLogsRepository.findRetryOrders(D365BaseUrl+Constants.ItemMaster.URL, Constants.ItemMaster.OrderType, Constants.Common.FAILURE, startDate, endDate, pageable);
                for(FinanceConsumerLogs financeConsumerLog: financeConsumerLogsList) {
                    if(retryCount!=0) {
                        if(financeConsumerLog.getRetry_count()<=retryCount) {
                            log.info("[retryItemMaster] entity Id: " + financeConsumerLog.getEntityId());
                            kafkaProducerTemplate.send(ItemMasterTopic, financeConsumerLog.getEntityId());
                        }
                    }
                    else{
                        log.info("[retryItemMaster] entity Id: " + financeConsumerLog.getEntityId());
                        kafkaProducerTemplate.send(ItemMasterTopic, financeConsumerLog.getEntityId());
                    }
                }
                page++;
            }
            while (financeConsumerLogsList.size()!=0);
            log.info("[retryItemMaster] ending time : {}", new Date());
        }
        catch(Exception e){
            log.error("[retryItemMaster] error " + genericClientService.getErrorMessage(e));
        }
    }
    public void pushItemToKafka(Integer productId) throws Exception {
        boolean newFlow = Boolean.valueOf(enableNewItemFlow);
        if (newFlow) {
            kafkaProducerTemplate.send(Constants.ItemMaster.ItemNewKafkaTopic, String.valueOf(productId));
        } else {
            kafkaProducerTemplate.send(itemMasterTopic, String.valueOf(productId));
        }
    }

    @Override
    public Object generatePayloadOnly(Integer productId, Boolean newInstance) {
        try {
            return generatePayload(productId,newInstance);
        } catch (Exception e) {
            log.error("ItemMasterServiceImpl: Error occurred while fetching payload for product id : {}",productId,e);
            throw new RuntimeException(e);
        }
    }

    @Transactional(value="financeDbTransactionManager")
    public void syncNonSyncedItems(){
        Pageable pageable;
        int currentPage=0;
        int totalRecordsProcessed = 0;
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(Constants.ItemMaster.itemMasterSyncCron);
        if(Boolean.FALSE.equals(schedulerConfig.getEnabled())){
            log.info("ItemMasterServiceImpl : Terminating new item-master sync job as scheduled cron is disabled");
            return;
        }
        int range = schedulerConfig.getRecordLimit();
        int startId = Integer.parseInt(schedulerConfig.getLastProcessed()) + 1;
        int pageLimit = Integer.parseInt(schedulerConfig.getRecordInterval());
        int endId = startId + range;

        if(startId> itemMasterRepository.findLastId()){
            log.info("ItemMasterServiceImpl : Terminating new item-master sync job as all new rows have been processed, last Id Processed : {}",startId);
            return;
        }
        while(true){
            pageable= PageRequest.of(currentPage,pageLimit, Sort.by("id"));
            List<String> nonSyncedProductIds = itemMasterRepository.findNonSyncedItemRecords(startId,endId,pageable);
            if(nonSyncedProductIds.isEmpty()){
                break;
            }
            nonSyncedProductIds.forEach(this::pushItemToKafkaQ);
            totalRecordsProcessed+=nonSyncedProductIds.size();
            currentPage++;
        }
        schedulerConfigService.updateLastProcessedForSchedulerConfig(schedulerConfig, Integer.toString(endId));
        log.info("ItemMasterServiceImpl : total Item-Master records processed : {}",totalRecordsProcessed);
    }

    public void syncFailedItems() {
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(Constants.ItemMaster.itemMasterRetrySyncCron);
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime updatedAtLimit = currentTime.minusMinutes(Integer.parseInt(schedulerConfig.getRecordInterval()));
        if(Boolean.FALSE.equals(schedulerConfig.getEnabled())){
            log.info("ItemMasterServiceImpl : Terminating failed item-master sync job as  scheduled cron is disabled ");
            return;
        }
        int totalFailedRecordsLimit=schedulerConfig.getRecordLimit();
        List<String> nonSyncedProductIds = itemMasterRepository.findFailedItemRecords(itemFailureRetryLimit, totalFailedRecordsLimit,updatedAtLimit);
        nonSyncedProductIds.forEach(this::pushItemToKafkaQ);
        log.info("ItemMasterServiceImpl : total failed Item-Master records retried : {}", nonSyncedProductIds.size());
    }

    /****
     * This method pushed items to new Item-Master Consumer made .
     * @param productId
     */
    public void pushItemToKafkaQ(String productId){
        try{
            kafkaProducerTemplate.send(itemMasterNewTopic,productId,productId);
        }catch (Exception e){
            log.error("ItemMasterServiceImpl : Exception occured : {} ,    while sending productId to ItemMasterD365 consumer for productId : {}",e,productId);
        }
    }
    public void syncItemMasterDataInIdRange(int startId, int endId) throws InvalidRequestException {
        log.info("ItemMasterServiceImpl : started processing of item-master sync for id range startId : {} ,endId : {}",startId,endId);

        validateAPIRequest(startId,endId);

        Pageable pageable;
        int currentPage=0;
        int totalRecordsProcessed=0;

        while(totalRecordsProcessed<=totalRecordsLimit){
            pageable= PageRequest.of(currentPage,pageSize, Sort.by("id"));
            List<Integer> listOfProductIdsToBeSynced = itemMasterRepository.findProductIdsForIdsInRange(startId,endId, pageable);
            if(listOfProductIdsToBeSynced.isEmpty()){
                break;
            }
            listOfProductIdsToBeSynced.forEach(productId ->  pushItemToKafkaQ(String.valueOf(productId)));
            totalRecordsProcessed+=listOfProductIdsToBeSynced.size();
            currentPage++;
        }
        log.info("ItemMasterServiceImpl : total records process with id range : {} ",totalRecordsProcessed);
    }

    public void syncItemMasterDataInDateRange(String startDate, String endDate) throws InvalidRequestException {
        log.info("ItemMasterServiceImpl : started processing of item-master sync for date range startDate : {} ,endDate : {}",startDate,endDate);
        Pageable pageable;
        int currentPage = 0;
        int totalRecordsProcessed = 0;
        LocalDateTime startCreatedAtDate = DateUtils.getDateFromString(startDate);
        LocalDateTime endCreatedAtDate = DateUtils.getDateFromString(endDate);

        validateAPIRequest(startCreatedAtDate,endCreatedAtDate);

        while (totalRecordsProcessed <= totalRecordsLimit) {
            pageable = PageRequest.of(currentPage, pageSize, Sort.by("id"));
            List<Integer> listOfProductIdsToBeSynced = itemMasterRepository.findProductIdsForDatesInRange(startCreatedAtDate, endCreatedAtDate, pageable);
            if (listOfProductIdsToBeSynced.isEmpty()) {
                break;
            }
            listOfProductIdsToBeSynced.forEach(productId -> pushItemToKafkaQ(String.valueOf(productId)));
            totalRecordsProcessed += listOfProductIdsToBeSynced.size();
            currentPage++;
        }
        log.info("ItemMasterServiceImpl : total records process with date range : {} ", totalRecordsProcessed);
    }

    void validateAPIRequest(int startId, int endId) throws InvalidRequestException {
        if (endId < startId || (endId - startId >= totalRecordsLimit)) {
            throw new InvalidRequestException("Id Range is more than " + totalRecordsLimit + " records");
        }
    }

    void validateAPIRequest(LocalDateTime startDate, LocalDateTime endDate) throws InvalidRequestException {
        if (endDate.isBefore(startDate) || startDate.until(endDate, ChronoUnit.DAYS) > dayRangeAllowed) {
            throw new InvalidRequestException("Date Range is more than " + dayRangeAllowed+ " days");
        }
    }

    @Override
    public void persistItemAndSyncToD365(FinanceSourceSystemSyncDto financeSourceSystemSyncDto){
        ItemD365Sync itemD365Sync =  itemD365RepositoryService.findItemByProductId(Integer.parseInt(financeSourceSystemSyncDto.getEntityId()));
        if(Objects.isNull(itemD365Sync)){
            itemD365Sync = itemD365RepositoryService.save(generateItemD365DtoFromFinanceSourceSystemDto(financeSourceSystemSyncDto));
        }
        syncToD365(itemD365Sync.getProductId());
    }



    private ItemD365Sync generateItemD365DtoFromFinanceSourceSystemDto(FinanceSourceSystemSyncDto financeSourceSystemSyncDto){
        return ItemD365Sync.builder().
                createdAt(DateUtils.getLocalDateObjectFromDate(financeSourceSystemSyncDto.getCreatedAt())).
                updatedAt(DateUtils.getLocalDateObjectFromDate(financeSourceSystemSyncDto.getUpdatedAt())).
                createdBy(financeSourceSystemSyncDto.getCreatedBy()).
                updatedBy(Constants.Common.financeConsumer).
                retryCount(0).
                d365SyncStatus(Common.CREATED).
                eventTime(DateUtils.getLocalDateObjectFromDate(financeSourceSystemSyncDto.getEventTime())).
                productId(Integer.parseInt(financeSourceSystemSyncDto.getEntityId())).
                referenceId(financeSourceSystemSyncDto.getId()).
                build();
    }


    @Override
    public ItemD365Sync generateItemD365DtoFromFinanceSourceSystemSync(FinanceSourceSystemSync financeSourceSystemSync){
        LocalDateTime currentDate = LocalDateTime.now();
        return ItemD365Sync.builder().
                createdAt(currentDate).
                updatedAt(currentDate).
                createdBy(Constants.Common.financeConsumer).
                updatedBy(Constants.Common.financeConsumer).
                retryCount(0).
                eventTime(currentDate).
                d365SyncStatus(Common.CREATED).
                productId(Integer.parseInt(financeSourceSystemSync.getEntityId())).
                referenceId(financeSourceSystemSync.getId()).
                build();
    }

    @Override
    @Transactional(value="financeDbTransactionManager")
    public void saveD365ResponseToItemD365Sync(FinanceSourceSystemSyncDto financeSourceSystemSyncDto){
        ItemD365Sync itemD365Sync = itemD365RepositoryService.findItemByProductId(Integer.parseInt(financeSourceSystemSyncDto.getEntityId()));
        itemD365Sync.setErrorMessage(financeSourceSystemSyncDto.getErrorMessage());
        itemD365Sync.setD365SyncStatus(financeSourceSystemSyncDto.getD365SyncStatus());
        itemD365Sync.setUpdatedAt(LocalDateTime.now());
        itemD365Sync.setUpdatedBy(Constants.Common.financeConsumer);
        itemD365RepositoryService.save(itemD365Sync);
    }


}
