package com.lenskart.financeConsumer.service.impl;

import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.constant.EventErrorType;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.MovementJournalResponse;
import com.lenskart.financeConsumer.dto.MovementJournalRetryRequest;
import com.lenskart.financeConsumer.dto.RetryRequestDTO;
import com.lenskart.financeConsumer.dto.d365requests.MovementJournalRequest;
import com.lenskart.financeConsumer.financeDb.readRepository.ForwardSourceSystemSyncReadRepository;
import com.lenskart.financeConsumer.model.enums.AdjustmentType;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.model.finance.ForwardSourceSystemSync;
import com.lenskart.financeConsumer.service.MovementJournalService;
import com.lenskart.financeConsumer.service.PositiveMovementJournalService;
import com.lenskart.financeConsumer.service.RetryService;
import com.lenskart.financeConsumer.service.SalesOrderService;
import com.lenskart.financeConsumer.service.TransferJournal.NegativeMovementJournalService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.lenskart.financeConsumer.v2.dto.MetaDto;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RetryServiceImpl implements RetryService {

    @Autowired
    private MovementJournalService movementJournalService;
    @Autowired
    private SalesOrderService salesOrderService;
    @Autowired
    private UwOrdersRepository uwOrdersRepository;
    @Autowired
    private SaleOrderUtil saleOrderUtil;
    @Autowired
    private NegativeMovementJournalService negativeMovementJournalService;
    @Autowired
    private PositiveMovementJournalService positiveMovementJournalService;
    @Autowired
    private ForwardSourceSystemSyncReadRepository forwardSourceSystemSyncReadRepository;
    @Autowired
    private FinanceSystemSyncRepository financeSystemSyncRepository;
    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate<String, String> kafkaProducerTemplate;
    @Value("${retry.negative.movementjournal.enabled}")
    private Boolean isRetryNegativeMovementJournalEnabled;
    @Value("${retry.movementjournal.topic}")
    private String topic;

    @Value("${eligible.retry.events}")
    private List<String> eligibleRetryEvents;

    String documentExistsRegex="(.*)Document number already exists(.*)";
    Pattern movementJournalAlreadyExist = Pattern.compile(EventErrorType.MOVEMENT_JOURNAL_ALREADY_EXIST.getErrorMessage(), Pattern.CASE_INSENSITIVE);
    Pattern barcodeAlreadyExistInInventory = Pattern.compile(EventErrorType.BARCODE_ALREADY_EXIST.getErrorMessage(), Pattern.CASE_INSENSITIVE);
    Pattern serialNumberError = Pattern.compile(EventErrorType.SERIAL_NUMBER.getErrorMessage(), Pattern.CASE_INSENSITIVE);
    @SneakyThrows
    @Override
    public Map<String, Object> retryMovementJournalAndPackingSlip(List<MovementJournalRetryRequest> movementJournalRetryRequests) {

/*        if (CollectionUtils.isEmpty(movementJournalRetryRequests) || movementJournalRetryRequests.size() > 20){
            throw new InvalidRequestException(HttpStatus.BAD_REQUEST.toString(), "shippingPackageIds neither empty nor size > 20");
        }*/

        Map<String, Object> result = new LinkedHashMap<>();

        for (MovementJournalRetryRequest request : movementJournalRetryRequests){

            Map<String, Object> positiveMovementJournalMap = new HashMap<>();
            Map<String, Boolean> packingSlipMap = new HashMap<>();
            Map<String, Object> negativeMovementJournalMap = new HashMap<>();

            String shippingPackageId = request.getShippingPackageId();
            List<UwOrder> uwOrders = uwOrdersRepository.findByShippingPackageIdAndFacilityCode(shippingPackageId, request.getFacilityCode());
            if(CollectionUtils.isEmpty(uwOrders)) {
                log.error("RetryServiceImpl -> retryMovementJournalAndPackingSlip :: uwOrders is empty, so skipping ");
                result.put(shippingPackageId+"_"+request.getFacilityCode(), "uwOrders is empty");
                continue;
            }
            if (!validateShippingPackageIdForMovementJournal(request, uwOrders)) {
                result.put(shippingPackageId + "_" + request.getFacilityCode(),
                           " invalid shipping packing id");
                continue;
            }
            /*
            Set<String> barcodes = null;
            if (CollectionUtils.isEmpty(request.getBarcodes())){
                barcodes = uwOrders.stream().map(UwOrder::getBarcode).collect(Collectors.toSet());
            }else {
                barcodes = new HashSet<>(request.getBarcodes());
            }*/
            Set<Integer> uwItemIds = uwOrders.stream().map(UwOrder::getUwItemId).collect(Collectors.toSet());
            Map<String, Boolean> barcodeAndPosMovementJournalStatus = retryMovementJournal(shippingPackageId, Collections.EMPTY_SET, AdjustmentType.POS, FinanceSourceSystemSyncEntityType.POSITIVE_MOVEMENT_JOURNAL_CORRECTION, request.getFacilityCode());
            if (CollectionUtils.isEmpty(barcodeAndPosMovementJournalStatus)){
                log.error("RetryServiceImpl -> retryMovementJournalAndPackingSlip :: barcodeAndPosMovementJournalStatus is empty, so skipping");
                result.put(shippingPackageId+"_"+request.getFacilityCode(), "barcodeAndPositiveMovementJournalStatus is empty");
                continue;
            }
            positiveMovementJournalMap.put(shippingPackageId, barcodeAndPosMovementJournalStatus);
            boolean isPackingSlipSucceed = retryPackingSlip(shippingPackageId, uwItemIds, request.getFacilityCode());
            packingSlipMap.put(shippingPackageId, isPackingSlipSucceed);
            if (!CollectionUtils.isEmpty(barcodeAndPosMovementJournalStatus) && isPackingSlipSucceed && isRetryNegativeMovementJournalEnabled){
                Map<String, Boolean> barcodeAndNegMovementJournalStatus = retryMovementJournal(shippingPackageId, barcodeAndPosMovementJournalStatus.keySet(), AdjustmentType.NEG, FinanceSourceSystemSyncEntityType.NEGATIVE_MOVEMENT_JOURNAL_CORRECTION, request.getFacilityCode());
                negativeMovementJournalMap.put(shippingPackageId, barcodeAndNegMovementJournalStatus);
            }

            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("positiveMovementJournal", positiveMovementJournalMap);
            responseMap.put("packingSlipMap", packingSlipMap);
            responseMap.put("negativeMovementJournal", negativeMovementJournalMap);
            result.put(shippingPackageId+"_"+request.getFacilityCode(), responseMap);
        }
        return result;
    }
    @SneakyThrows
    @Override
    public Map<String, Boolean> retryMovementJournal(String shippingPackageId, Set<String> barcodes, AdjustmentType adjustmentType, FinanceSourceSystemSyncEntityType type, String facilityCode){

        Map<String, Boolean>  barcodeAndMovementJournalStatus= new HashMap<>();
        log.info("started retryMovementJournal adjustmentType : {}, shippingPackageId : {}, barcodes : {}", adjustmentType, shippingPackageId, barcodes);
        MovementJournalRequest request = MovementJournalRequest.from(shippingPackageId, new ArrayList<>(barcodes), adjustmentType, facilityCode, type);
        List<MovementJournalRequest> requests = new ArrayList<>();
        requests.add(request);
        Map<String, Object>  response  = movementJournalService.createMovementJournalWithMultipleBarcodes(requests);
        if (CollectionUtils.isEmpty(response)){
            log.info("RetryServiceImpl -> retryMovementJournal :: response is empty, so returned");
            return new HashMap<>();
        }
        for (Map.Entry<String, Object> entry : response.entrySet()){
            log.info("RetryServiceImpl -> retryMovementJournal :: response is : {}", entry.getValue());
            if (Objects.isNull(entry.getValue())){
                continue;
            }
            MovementJournalResponse movementJournalResponse = null;
            try {
                movementJournalResponse = ObjectHelper.convertValue(entry.getValue(), MovementJournalResponse.class);
            }catch (Exception e){
                log.info("RetryServiceImpl -> retryMovementJournal :: Exception occurred  deserialization : {}", entry.getValue());
                continue;
            }
            if (Objects.nonNull(movementJournalResponse.getSuccess())){
                boolean status = Boolean.parseBoolean(movementJournalResponse.getSuccess());
                if (StringUtils.isNotEmpty(movementJournalResponse.getMessage())) {
                    Matcher barcodeExistMatcher = barcodeAlreadyExistInInventory.matcher(movementJournalResponse.getMessage());
                    log.info("RetryServiceImpl -> response message here : {} ", movementJournalResponse.getMessage());
                    if (barcodeExistMatcher.find()) {
                        log.info("RetryServiceImpl -> skipping movement journal for documentNo : {} , due to barcode already present in inventory", movementJournalResponse.getDocumentNo());
                        continue;
                    }
                }
                if (status){
                    barcodeAndMovementJournalStatus.put(movementJournalResponse.getBarcode(), Boolean.valueOf(movementJournalResponse.getSuccess()));
                }else {
                    log.info("RetryServiceImpl -> checking for matcher for message {}",movementJournalResponse.getMessage() );
                    Matcher matcher = movementJournalAlreadyExist.matcher(movementJournalResponse.getMessage());
                    log.info("RetryServiceImpl -> matcher matches as  {}",matcher.matches() );
                    if (matcher.find()){
                        log.info("RetryServiceImpl -> movement journal already exist so added it in map {}", movementJournalResponse.getBarcode());
                        barcodeAndMovementJournalStatus.put(movementJournalResponse.getBarcode(), true);
                    }
                }
            }
        }
        log.info("End retryMovementJournal adjustmentType : {}, barcodeAndMovementJournalStatus : {}", adjustmentType, barcodeAndMovementJournalStatus);
        return barcodeAndMovementJournalStatus;
    }

    @SneakyThrows
    @Override
    public boolean retryPackingSlip(String shippingPackageId, Set<Integer> uwItemIds, String facilityCode) {
        log.info("started retryPackingSlip shippingPackageId : {}, uwItemIds : {}", shippingPackageId, uwItemIds);
        FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.PACKING_SLIP,
                                                                                                                                         FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                                                                                                                                         shippingPackageId,
                                                                                                                                         facilityCode);
        if (Objects.nonNull(financeSourceSystemSync) && Constants.Common.SUCCESS.equalsIgnoreCase(financeSourceSystemSync.getD365SyncStatus())){
            log.info("retryPackingSlip already exist with success so returning");
            return true;
        }
        ResponseEntity responseEntity = salesOrderService.createPackingSlip(new ArrayList<>(uwItemIds));
        HashMap responseBody = (HashMap) responseEntity.getBody();
        Boolean responseStatus = false;
        String message = null;
        if (Objects.nonNull(responseBody)){
            message = (String) responseBody.get("Message");
            responseStatus = Boolean.valueOf(String.valueOf(responseBody.get("Success")));
        }
        log.info("End retryPackingSlip shippingPackageId : {}, message :{},  responseStatus : {}", shippingPackageId, message, responseStatus);
        return responseStatus;
    }
    @Override
    public void pushToKafka(List<MovementJournalRetryRequest> requests) {

        if (CollectionUtils.isEmpty(requests)){
            return;
        }
        for (MovementJournalRetryRequest request : requests){
            kafkaProducerTemplate.send(topic, request.getShippingPackageId(), ObjectHelper.convertToString(request));
            log.info("[RetryServiceImpl][pushToKafka] -> pushed to topic:{}, request {}", topic, request);
        }
    }

    public boolean validateShippingPackageIdForMovementJournal(MovementJournalRetryRequest movementJournalRetryRequest, List<UwOrder> uwOrders) throws Exception {
        FinanceSourceSystemSync packingSlipfinanceSourceSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(
                FinanceSourceSystemSyncEvent.PACKING_SLIP,
                FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                movementJournalRetryRequest.getShippingPackageId(),
                movementJournalRetryRequest.getFacilityCode());

        if (Objects.isNull(packingSlipfinanceSourceSystemSync)) {
            log.error("Packing slip is null for shipping packing id: {}",
                      movementJournalRetryRequest.getShippingPackageId());
            return false;
        }

        if (Objects.nonNull(packingSlipfinanceSourceSystemSync) && Constants.Common.FAILURE.equals(packingSlipfinanceSourceSystemSync.getD365SyncStatus())) {
            Matcher matcher = serialNumberError.matcher(packingSlipfinanceSourceSystemSync.getErrorMessage());
            if (matcher.find()) {
                return true;
            }
        }
        List<String> barcodes = uwOrders.stream().map(a -> a.getBarcode()).collect(Collectors.toList());

        for (String barcode : barcodes) {
            String entityIdForDocumentNo = saleOrderUtil.getSalesOrderNo(movementJournalRetryRequest.getShippingPackageId()) + "_" + barcode;
            String generatedPositiveDocumentNumber = positiveMovementJournalService.generateDocumentNumber(
                    entityIdForDocumentNo);
            String generatedNegativeDocumentNumber = negativeMovementJournalService.generateDocumentNumber(
                    entityIdForDocumentNo);
            ForwardSourceSystemSync positiveForwardSourceSystemSync = forwardSourceSystemSyncReadRepository
                    .findByEventAndEntityTypeAndEntityId(FinanceSourceSystemSyncEvent.MOVEMENT_JOURNAL,
                                                         FinanceSourceSystemSyncEntityType.POSITIVE_MOVEMENT_JOURNAL_CORRECTION,
                                                         generatedPositiveDocumentNumber);
            ForwardSourceSystemSync negativeForwardSourceSystemSync = forwardSourceSystemSyncReadRepository
                    .findByEventAndEntityTypeAndEntityId(FinanceSourceSystemSyncEvent.MOVEMENT_JOURNAL,
                                                         FinanceSourceSystemSyncEntityType.NEGATIVE_MOVEMENT_JOURNAL_CORRECTION,
                                                         generatedNegativeDocumentNumber);
            /***
             1.  when positive is success but packing slip is failure
             2.  when positive is success but packing slip is success but, -ve journal is failure/missing
             3.  when positive is failure/missing but error case : "serial id" added on the top
             */
            if (Objects.nonNull(
                    positiveForwardSourceSystemSync) && Constants.Common.SUCCESS.equals(
                    positiveForwardSourceSystemSync.getD365SyncStatus())) {
                if (Constants.Common.FAILURE.equals(packingSlipfinanceSourceSystemSync.getD365SyncStatus())) {
                    return true;
                } else if (Constants.Common.SUCCESS.equals(packingSlipfinanceSourceSystemSync.getD365SyncStatus())) {
                    if (Objects.isNull(negativeForwardSourceSystemSync) || Constants.Common.FAILURE.equals(
                            negativeForwardSourceSystemSync.getD365SyncStatus())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void pushToKafkaWithId(List<Long> requests) {

        for (Long id : requests){
            FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository.findById(id).get();
            if (Objects.isNull(financeSourceSystemSync)){
                continue;
            }
            MovementJournalRetryRequest request = new MovementJournalRetryRequest();
            request.setShippingPackageId(financeSourceSystemSync.getEntityId());
            request.setFacilityCode(financeSourceSystemSync.getFacilityCode());
            kafkaProducerTemplate.send(topic, request.getShippingPackageId(), ObjectHelper.convertToString(request));
            log.info("[RetryServiceImpl][pushToKafkaWithId] -> pushed to topic:{}, request {}", topic, request);
        }
    }

    @Override
    public GenericResponseDto<RetryRequestDTO> retryEntityForEvent(RetryRequestDTO retryRequestDTO) {
        log.info("[RetryServiceImpl][retryEntityForEvent] Retrying entities for based on event and time range for request : {}", retryRequestDTO);
        GenericResponseDto<RetryRequestDTO> response = new GenericResponseDto<>();
        response.setData(retryRequestDTO);
        MetaDto meta = new MetaDto();

        if (!eligibleRetryEvents.contains(retryRequestDTO.getEventType().name())) {
            return getRetryRequestDTOGenericResponseDto(meta, response, HttpStatus.BAD_REQUEST, D365ResponseType.FAILED, "Unsupported event type. Please check request");
        }
        try {
            Map<LocalDate, LocalDate> dateMap = getDateMap(retryRequestDTO.getStartDate(),
                                                           retryRequestDTO.getEndDate());
            if (dateMap.isEmpty()) {
                return getRetryRequestDTOGenericResponseDto(meta, response, HttpStatus.BAD_REQUEST, D365ResponseType.FAILED, "Unable to process start and end date. Please check request");
            }

            log.info("[RetryServiceImpl][retryEntityForEvent] Date Map generated : {}", dateMap);

            for (Map.Entry<LocalDate, LocalDate> entry : dateMap.entrySet()) {
                List<Long> eligibleIds = financeSystemSyncRepository.findIdsByEventAndEventTimeAndErrorMessage(retryRequestDTO.getEventType().name(),
                                                                                                                      entry.getKey(),
                                                                                                                      entry.getValue(),
                                                                                                                      retryRequestDTO.getErrorMessage());
                log.info("[RetryServiceImpl][retryEntityForEvent] eligibleIds size : {}", eligibleIds.size());
                salesOrderService.processIdsForPushingShipments(eligibleIds);
            }
            return getRetryRequestDTOGenericResponseDto(meta, response, HttpStatus.OK, D365ResponseType.SUCCESS, "Successfully processed request");
        } catch (Exception e) {
            log.error("[RetryServiceImpl][retryEntityForEvent] Error occurred while processing request : {}", retryRequestDTO, e);

            return getRetryRequestDTOGenericResponseDto(meta, response, HttpStatus.INTERNAL_SERVER_ERROR, D365ResponseType.FAILED, e.getMessage());
        }
    }

    private GenericResponseDto<RetryRequestDTO> getRetryRequestDTOGenericResponseDto(MetaDto meta,
                                                                                     GenericResponseDto<RetryRequestDTO> response,
                                                                                     HttpStatus httpStatus,
                                                                                     D365ResponseType message,
                                                                                     String displayMessage) {
        generateMeta(meta, httpStatus, message.name(), displayMessage);
        response.setMeta(meta);
        return response;
    }

    private void generateMeta(MetaDto meta, HttpStatus status, String message, String displayMessage) {
        meta.setCode(status.name());
        meta.setMessage(message);
        meta.setDisplayMessage(displayMessage);
    }

    private Map<LocalDate, LocalDate> getDateMap(LocalDate startDate, LocalDate endDate) {
        Map<LocalDate, LocalDate> dateMap = new HashMap<>();
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return dateMap;
        }
        LocalDate currentStart = startDate;

        while (currentStart.isBefore(endDate)) {
            LocalDate currentEnd = currentStart.plusDays(10);
            if (currentEnd.isAfter(endDate)) {
                currentEnd = endDate;
            }
            dateMap.put(currentStart, currentEnd);
            currentStart = currentEnd;
        }
        return dateMap;
    }
}
