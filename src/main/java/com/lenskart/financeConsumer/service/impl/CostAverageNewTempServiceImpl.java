package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.dao.CostAverageNewTempRepository;
import com.lenskart.financeConsumer.service.CostAverageNewTempService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CostAverageNewTempServiceImpl implements CostAverageNewTempService {
    public String getCostAvg(Integer productId)
    {
        String costAvg="";
        try {
            costAvg = costAverageNewTempRepository.findAvgCost(productId);
        }
        catch (Exception e){
            log.error("[CostAverageNewTempServiceImpl][getCostAvg] {} {} ",productId,e);
        }
        return costAvg;
    }

    @Autowired
    private CostAverageNewTempRepository costAverageNewTempRepository;
}
