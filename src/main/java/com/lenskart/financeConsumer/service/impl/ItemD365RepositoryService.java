package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.financeDb.writeRepository.ItemD365SyncRepository;
import com.lenskart.financeConsumer.model.financeDb.ItemD365Sync;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

@Slf4j
@Service
public class ItemD365RepositoryService {

    @Autowired
    private ItemD365SyncRepository itemD365SyncRepository;

    public ItemD365Sync save(ItemD365Sync itemD365Sync) {
        try {
            return itemD365SyncRepository.save(itemD365Sync);
        } catch (DataAccessException e) {
            log.error("exception occured while trying to save item :{} ", itemD365Sync, e);
            throw e;
        }
    }

    public ItemD365Sync findItemByProductId(int productId){
    return  itemD365SyncRepository.findByProductId(productId);
    }

    @Transactional(value = "financeDbTransactionManager", rollbackFor = Exception.class)
    public void markSyncAsFailure(String entityId, FinanceSourceSystemSyncEvent financeSourceSystemSyncEvent,
                                  FinanceSourceSystemSyncEntityType financeSourceSystemSyncEntityType,
                                  String failureMessage) {
        ItemD365Sync itemD365Sync = itemD365SyncRepository.findByProductId(Integer.parseInt(entityId));
        if (ObjectUtils.isEmpty(itemD365Sync)) {
            log.error("FinanceSourceSystemToItemD365SyncDataSyncService : Exception invalid productId triggered for marking failure,  productId : {}", entityId);
            return;
        }
        itemD365Sync.setD365SyncStatus(Constants.Common.FAILURE);
        itemD365Sync.setUpdatedAt(LocalDateTime.now());
        itemD365Sync.setErrorMessage(failureMessage);
        itemD365SyncRepository.save(itemD365Sync);
    }

    public ItemD365Sync findItemById(long id){
        return itemD365SyncRepository.findById(id).
                orElseThrow(()->new RecordNotFoundException("item not present in item_d365_sync table for id : "+id));
    }

    public List<ItemD365Sync> findFailedItemRecords(List<String> d365SyncStatusList , int maxAllowedRetryCount , int maxLimit, LocalDateTime updatedAtLimit ){
      return  itemD365SyncRepository.findFailedItemRecords(d365SyncStatusList,maxAllowedRetryCount,maxLimit ,updatedAtLimit);
    }

    public List<ItemD365Sync> findNonsyncedItems(
            String status, Pageable pageable){
        return itemD365SyncRepository.findNonsyncedItems(status,pageable);
    }

    public HashSet<Long> fetchSetOfReferenceIdsFromDate(Date fromDate){
        return itemD365SyncRepository.findItemDataForTimeRange(fromDate);
    }

}
