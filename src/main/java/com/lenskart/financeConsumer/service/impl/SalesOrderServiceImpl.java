package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.core.model.ExchangeOrder;
import com.lenskart.core.model.HubMaster;
import com.lenskart.core.model.ItemWisePriceDetails;
import com.lenskart.core.model.MpOrderDetails;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.clients.JunoClient;
import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.clients.UnicomClient;
import com.lenskart.financeConsumer.connector.impl.InventoryAdaptorConnectorImpl;
import com.lenskart.financeConsumer.constant.EventErrorType;
import com.lenskart.financeConsumer.constant.KafkaConstants;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.HubMasterRepository;
import com.lenskart.financeConsumer.dao.ItemWisePricesRepository;
import com.lenskart.financeConsumer.dao.MpOrderDetailsRepository;
import com.lenskart.financeConsumer.dao.OrderRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dao.mongo.FinanceConsumerLogsRepository;
import com.lenskart.financeConsumer.dao.mongo.ShippingInvoiceDetailsRepository;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.dto.FinanceConsumerResponse.FinanceConsumerResponseDto;
import com.lenskart.financeConsumer.dto.GiftCardEligibilityCheckDto;
import com.lenskart.financeConsumer.dto.SaleOrderPackingSlipPayloadRequestDto;
import com.lenskart.financeConsumer.dto.d365requests.CostPriceResponseBody;
import com.lenskart.financeConsumer.dto.d365requests.D365RequestDto;
import com.lenskart.financeConsumer.dto.d365requests.D365ResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardEligibilityRequestDto;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardSyncDto;
import com.lenskart.financeConsumer.dto.d365requests.InvoiceDetailsResponse;
import com.lenskart.financeConsumer.dto.d365requests.SaleOrderPackingSlipResponseDTO;
import com.lenskart.financeConsumer.dto.d365requests.SaleOrderSync;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.MiscChargesHeader;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.MiscChargesLine;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.NewInstanceDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SaleOrderSyncConsumerDto;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesLineList;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SoLine;
import com.lenskart.financeConsumer.dto.d365requests.ShippingIdFacilityCode;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.failureStrategy.SaleOrderFailureStrategyService;
import com.lenskart.financeConsumer.failureStrategy.SaleOrderStrategy;
import com.lenskart.financeConsumer.financeDb.inventory.read.ExchangeOrdersReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.HubMasterReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.ItemWisePricesReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.MpOrderDetailsReadRepository;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.InventoryCorrectionWriteRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.SaleOrderWriteRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.ShipmentInvoiceDetailsRepository;
import com.lenskart.financeConsumer.model.InvoiceDetails;
import com.lenskart.financeConsumer.model.OrderItemGSTDetail;
import com.lenskart.financeConsumer.model.ShippingInvoiceDetails;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.enums.SuffixHandlerRequestType;
import com.lenskart.financeConsumer.model.enums.SyncRequestType;
import com.lenskart.financeConsumer.model.financeDb.InventoryCorrection;
import com.lenskart.financeConsumer.model.financeDb.SaleOrder;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.model.financeDb.ShipmentInvoiceDetails;
import com.lenskart.financeConsumer.model.logistics.ShipmentDetails;
import com.lenskart.financeConsumer.model.mongo.D365ApiLog;
import com.lenskart.financeConsumer.model.mongo.FinanceConsumerLogs;
import com.lenskart.financeConsumer.service.ApiLogService;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.service.CourierInfoService;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.HubMasterService;
import com.lenskart.financeConsumer.service.InvoiceDetailsService;
import com.lenskart.financeConsumer.service.OrderAddressUpdateService;
import com.lenskart.financeConsumer.service.OrderHeaderService;
import com.lenskart.financeConsumer.service.OrderItemGstDetailsService;
import com.lenskart.financeConsumer.service.OrderService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.service.RuleEngineService;
import com.lenskart.financeConsumer.service.SBRTOrderItemService;
import com.lenskart.financeConsumer.service.SalesOrderService;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import com.lenskart.financeConsumer.service.ShippingStatusService;
import com.lenskart.financeConsumer.service.SystemPreferenceService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.strategy.FailureEventHelper;
import com.lenskart.financeConsumer.util.*;
import com.lenskart.orderops.model.OrderAddressUpdate;
import com.lenskart.orderops.model.UwItemWisePriceInfo;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import com.netflix.ribbon.proxy.annotation.Http;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.util.ListUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@Primary
public class SalesOrderServiceImpl implements SalesOrderService {
    @Autowired
    private UnicomClient unicomClient;

    public static final String FAILURE = "Failure";
    private static final Pattern SUFFIX_PATTERN = Pattern.compile("^_([1-9]|[1-9][0-9])$");

    @Autowired
	private PayloadBuildUtils payloadBuildUtils;

	@Autowired
	private ObjectMapper objectMapper;
    @Autowired
    private InventoryAdaptorConnectorImpl inventoryAdaptorConnector;


    @Autowired
	private SalesOrderServiceImplTemp salesOrderServiceImplTemp;

    @Value("${gift-card.enabled}")
    boolean isGiftCardEnabled;

	@Autowired
	private ShippingInvoiceDetailsRepository shippingInvoiceDetailsRepository;

	private static final String NOT_FOUND_SHIPPING_PACKAGE_ID = "Not Found Shipping package id for uw item id ";

	@Value("#{'${sale.order.eligible.country}'.split(',')}")
	private List<String> saleOrderEligibleCountry;

	@Value("#{'${packing.slip.eligible.country}'.split(',')}")
	private List<String> packingSlipEligibleCountry;

	@Value("${taxRateForShippingCharges:18.00}")
	private Double taxRateForShippingCharges;

	@Value("#{'${gold.productIds}'.split(',')}")
	private List<Integer> goldMembershipProductIds;

	@Value("${isShippingChargesPayloadEnabled:false}")
	private boolean isShippingChargesPayloadEnabled;
    @Value("${isEligibleToSyncWithUnderScoreOne:false}")
    private boolean isEligibleToSyncWithUnderScoreOne;

    @Value("${hsn.eligible.productIds}")
    private Set<Integer> hsnEligibleProductsList;

    @Value("${sbrt.item.flag}")
    private String SBRTItemFlag;

    @Value("${non-sbrt.item.flag}")
    private String nonSBRTItemFlag;

    @Autowired
	private UwOrdersRepository uwOrdersRepository;

    @Autowired
    private UwOrdersReadRepositoryFinance uwOrdersReadRepository;

	@Autowired
	private GenericClientService genericClientService;

	@Autowired
	private OrderService orderService;

	@Autowired
	private ProductService productService;

	@Autowired
	private OrderAddressUpdateService orderAddressUpdateService;

	@Autowired
	private ShippingStatusService shippingStatusService;

	@Autowired
	private SaleOrderUtil saleOrderUtil;

	@Autowired
	private OrderHeaderService orderHeaderService;

	@Autowired
	private OrderItemGstDetailsService orderItemGstDetailsService;

	@Autowired
	private UwOrdersService uwOrdersService;

	@Autowired
	private ClassificationService classificationService;

	@Autowired
	private InvoiceDetailsService invoiceDetailsService;

	@Autowired
	private ApiLogService apiLogService;

	@Autowired
	private SystemPreferenceService systemPreferenceService;

    @Autowired
    SchedulerConfigService schedulerConfigService;

	@Autowired
	private CourierInfoService courierInfoService;

    @Autowired
    private RuleEngineService ruleEngineService;

	@Autowired
	private HubMasterRepository hubMasterRepository;
    @Autowired
    private HubMasterReadRepository hubMasterReadRepository;

	@Autowired
	private OrderRepository orderRepository;

	@Autowired
	private MpOrderDetailsRepository mpOrderDetailsRepository;
    @Autowired
    private MpOrderDetailsReadRepository mpOrderDetailsReadRepository;

	@Autowired
	private D365LogRepository d365LogRepository;

	@Autowired
	private FinanceConsumerLogsRepository financeConsumerLogsRepository;

	@Autowired
	private ItemWisePricesRepository itemWisePricesRepository;
    @Autowired
    private ItemWisePricesReadRepository itemWisePricesReadRepository;

	@Autowired
	private ExchangeOrdersReadRepository exchangeOrdersReadRepository;

	@Autowired
	private HubMasterService hubMasterService;

	@Autowired
	private PackingSlipUtil packingSlipUtil;

    @Autowired
    FinanceSystemSyncRepository financeSystemSyncRepository;

    @Autowired
    ForwardTransferOrderService forwardTransferOrderService;

    @Autowired
    ForwardTransferJournalService forwardTransferJournalService;

	@Autowired
	@Qualifier("kafkaProducerTemplate")
	private KafkaTemplate kafkaProducerTemplate;

	@Value("${financeAdapter.url}")
	private String financeAdapterUrl;

    @Autowired
    private JunoClient junoClient;

	@Value("${D365.base.url}")
	private String D365BaseUrl;

	@Value("${D365.forward.url}")
	private String D365ProdUrl;

    @Value("${d365.gift-card.kafka.topic}")
    private String giftCardTopic;

	@Value("${d365.salesorder.kafka.topic}")
	private String d365SaleOrderTopic;

	@Value("${d365.salesorder.kafka.prod.temp.topic}")
	private String d365SaleOrderProdTopic;

	@Value("${packingSlip.kafka.topic}")
	private String PackingSlipTopic;

	@Value("${packingslip.live}")
	private String packingSlipLive;

	@Value("${finance.classification.blank.hsncode}")
	private Set<String> blankHsnCodeClassifications;
    @Value("${inventoryAdaptor.invoiceDetailsFetch.enabled}")
    boolean invoiceDetailsFetchEnabled;

	private final String EXCHANGE = "exchangep";

	private final String ORDER_ID = "order_id";

    private final DateTimeFormatter isoDateFormatter = DateTimeFormatter.ISO_LOCAL_DATE;

	@Value("${D365.exchange.topic:D365_Exchange}")
	private String D365ExchangeTopic;
    @Value("${sale.order.retry.max.count}")
    private int maxCount;
    @Value("${sale.order.retry.page.limit}")
    private int pageLimit;
    @Autowired
    private SaleOrderWriteRepository saleOrderWriteRepository;
    @Value("${sale.order.retry.kafka.newinstance.topic}")
    private String saleOrderRetryTopicForNewInstance;
    @Value("${sale.order.failure.strategy.kafka.topic}")
    private String saleOrderFailureStrategyTopic;
    @Autowired
    private NexsClient nexsClient;
    @Value("${d365.saleOrder.new.failureRetryLimit:40}")
    private int retryCount;
    @Value("${d365.packingSlip.new.failureRetryLimit:40}")
    private int packingSlipRetryCount;
    @Value("${d365.service.classification.ids}")
    private Set<Integer> serviceClassificationIds;
    private final Set<String> saleOrderEligibleStatus = new HashSet<>(Arrays.asList(D365ResponseType.FAILED.name(), D365ResponseType.NEW.name()));
    @Autowired
    private FailureEventHelper failureEventHelper;
    @Value("${underscoreCharacterForReSync}")
    private String underscoreCharacterForReSync;
    @Autowired
    ShipmentInvoiceDetailsRepository shipmentInvoiceDetailsRepository;
    @Autowired
    SaleOrderFailureStrategyService saleOrderFailureStrategyService;
    @Autowired
    private SBRTOrderItemService sbrtOrderItemService;

    @Autowired
    private InventoryCorrectionWriteRepository inventoryCorrectionWriteRepository;

    @Value("${dummy.barcode.suffix.value}")
    private String dummyBarCodeSuffixValue;

    @Value("${saleOrder.sbrt.sync.enabled}")
    private boolean isSBRTFlowEnabledForSalesOrder;

    @Value("${exempt.tax.product.ids}")
    private List<String> exemptTaxProductIds;

    @Value("${d365.doc-to-sql.kafka.topic}")
    private String d365DocToSqlKafkaTopic;

    private final Set<String> packingSlipEligibleStatus = new HashSet<>(Arrays.asList(D365ResponseType.NEW.name(),D365ResponseType.FAILED.name()));
    private final Set<String> eligibleEventErrorTypesForSaleOrders = new HashSet<>(Arrays.asList(EventErrorType.INVOICE_NOT_PRESENT.name(),
                                                                                                         EventErrorType.NOT_FOUND_SHIPPING_PACKAGE_ID.name(),
                                                                                                         EventErrorType.ITEM_DOES_NOT_EXIST.name(),
                                                                                                         EventErrorType.WAREHOUSE_DOES_NOT_EXIST.name(),
                                                                                                         EventErrorType.INVALID_DIMENTION.name()));
    public boolean validateSaleOrderRequest(List<Integer> uwItemIds){
        List<UwOrder> uwOrders = uwOrdersRepository.findByUwItemIdIn(uwItemIds);
        Map<String, List<Integer>> inputDataForSaleOrder = apiLogService.getUwItemIdsForKafka(uwOrders);
        if(inputDataForSaleOrder.size() == 0){
            return false;
        } else {
             return true;
        }
    }

    @Override
    public void createSaleOrderTemp(SaleOrderSync saleOrderSync) {
        try {
            log.info("[SalesOrderServiceImpl][createSaleOrderTemp] sending event to topic: {} with payload: {}",
                    KafkaConstants.SALES_ORDER_TEMP_TOPIC, saleOrderSync);
            kafkaProducerTemplate.send(KafkaConstants.SALES_ORDER_TEMP_TOPIC, objectMapper.writeValueAsString(saleOrderSync));
        } catch (JsonProcessingException e) {
            log.error("[SalesOrderServiceImpl][createSaleOrderTemp] error while sending event to topic: {} with payload: {}",
                    KafkaConstants.SALES_ORDER_TEMP_TOPIC, saleOrderSync);
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean shipmentIdRepopulate(List<Integer> uwItemIds) {
        try {
            log.info("shipmentIdRepopulate uwItemIds:{}", uwItemIds);
            List<UwOrder> uwOrders =
                    uwOrdersRepository.findByUwItemIdIn(uwItemIds);
            UwOrder uwOrder = uwOrders.get(0);
            boolean isSuccess = saleOrderUtil.generateMissingShipmentId(uwOrder.getUnicomOrderCode(),
                    uwOrder.getFacilityCode(),
                    uwItemIds);
            return isSuccess;
        } catch (Exception e) {
            log.error("[shipmentIdRepopulate] uwItemIds:{} | error :{}", uwItemIds, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean shipmentIdRepopulateUnicomOrderCode(String unicomOrderCode) {
        try {
            log.info("shipmentIdRepopulateUnicomOrderCode unicomOrderCode:{}",unicomOrderCode);
            List<UwOrder> uwOrders =
                    uwOrdersRepository.findByUnicomOrderCode(unicomOrderCode);
            UwOrder uwOrder = uwOrders.get(0);
            List<Integer> uwItemIds = uwOrders.stream().map(UwOrder::getUwItemId).collect(Collectors.toList());
            boolean isSuccess = saleOrderUtil.generateMissingShipmentId(uwOrder.getUnicomOrderCode(),
                    uwOrder.getFacilityCode(),uwItemIds);
            return isSuccess;
        } catch (Exception e) {
            log.error("[shipmentIdRepopulateUnicomOrderCode] unicomOrderCode:{} | error :{}",
                    unicomOrderCode, e.getMessage(), e);
        }
        return false;
    }
    @Override
    public void shipmentIdRepopulateForUnicomOrderCodes(List<String> unicomOrderCodes) {
        int sizeOfUnicomOrdersList = unicomOrderCodes.size();
        log.info("repopulating shipmentIds for  starting unicomOrderCode:{} and ending unicomOrderCode: {}",
                unicomOrderCodes.get(0),unicomOrderCodes.get(sizeOfUnicomOrdersList-1));
        for(int i=0;i<unicomOrderCodes.size();i++){
            String unicomOrderCode=unicomOrderCodes.get(i);
            shipmentIdRepopulateUnicomOrderCode(unicomOrderCode);
        }
        log.info("finished repopulating shipmentIds!");
    }

    private Pair<Set<String>, Set<String>> getEligibleSaleOrderAndPackingSlipStatusBasedOnRequest(SyncRequestType syncRequestType) {
        switch (syncRequestType) {
            case SALE_ORDER:
                return Pair.of(saleOrderEligibleStatus, Collections.EMPTY_SET);
            case PACKING_SLIP:
                return Pair.of(Collections.singleton(D365ResponseType.SUCCESS.name()),
                               packingSlipEligibleStatus);
            case SALE_ORDER_AND_PACKING_SLIP:
                return Pair.of(saleOrderEligibleStatus, packingSlipEligibleStatus);
            default:
                return Pair.of(Collections.EMPTY_SET, Collections.EMPTY_SET);
        }
    }
    public void syncSaleOrder(LocalDateTime fromDateCalendar, LocalDateTime toDateCalendar, SyncRequestType syncRequestType) {
        log.info(
                "SaleOrderService : sale order sync request by date range  fromDateCalendar: {} | toDateCalendar: {} | syncRequestType:{}",
                fromDateCalendar,
                toDateCalendar,
                syncRequestType);
        try {
            int page = 0;
            int count = 0;
            Pair<Set<String>, Set<String>> saleOrderAndPackingSlipEligibleStatus = getEligibleSaleOrderAndPackingSlipStatusBasedOnRequest(
                    syncRequestType);
            while (count < maxCount) {
                Pageable pageable = PageRequest.of(page, pageLimit, Sort.by("updated_at").ascending());
                List<SaleOrder> saleOrderEligibleItemList = saleOrderWriteRepository.findSaleOrderDataForBySaleOrderAndPackingSlipStatusAndDateRange(
                        fromDateCalendar,
                        toDateCalendar,
                        saleOrderAndPackingSlipEligibleStatus.getLeft(),
                        saleOrderAndPackingSlipEligibleStatus.getRight(),
                        pageable);
                log.info(
                        "SaleOrderService : sale order eligible Items filtered by Date range size : {} | page:{} | maxCount:{}",
                        saleOrderEligibleItemList.size(),
                        page,
                        maxCount);
                if (CollectionUtils.isEmpty(saleOrderEligibleItemList)) {
                    break;
                }
                page = page + 1;
                count = count + saleOrderEligibleItemList.size();
                if (SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(syncRequestType)) {
                    filterAndPublishPackagingSlipOnlyEvents(saleOrderEligibleItemList);
                }
                publishSaleOrderItems(saleOrderEligibleItemList, syncRequestType);
            }
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred while sale order sync by date range", ex);
        }
    }

    @Override
    public void syncSaleOrder(Set<Long> listOfIds, SyncRequestType syncRequestType) {
        log.info(
                "SaleOrderService : sale order sync request by list of Ids  listOfIds: {} | syncRequestType:{}",
                listOfIds,
                syncRequestType);
        try {
            int page = 0;
            int count = 0;
            Pair<Set<String>, Set<String>> saleOrderAndPackingSlipEligibleStatus = getEligibleSaleOrderAndPackingSlipStatusBasedOnRequest(
                    syncRequestType);
            while (count < maxCount) {
                Pageable pageable = PageRequest.of(page, pageLimit, Sort.by("updated_at").ascending());
                List<SaleOrder> saleOrderEligibleItemList = saleOrderWriteRepository.findSaleOrderDataByListOfIdAndSaleOrderAndPackingSlipStatus(
                        listOfIds,
                        saleOrderAndPackingSlipEligibleStatus.getLeft(),
                        saleOrderAndPackingSlipEligibleStatus.getRight(),
                        pageable);
                log.info(
                        "SaleOrderService : sale order eligible Items filtered by list of Id : {} | page:{} | maxCount:{}",
                        saleOrderEligibleItemList.size(),
                        page,
                        maxCount);
                if (CollectionUtils.isEmpty(saleOrderEligibleItemList)) {
                    break;
                }
                page = page + 1;
                count = count + saleOrderEligibleItemList.size();
                if (SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(syncRequestType)) {
                    filterAndPublishPackagingSlipOnlyEvents(saleOrderEligibleItemList);
                }
                publishSaleOrderItems(saleOrderEligibleItemList, syncRequestType);
            }
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred while sale order sync by list of Ids", ex);
        }
    }

    @Override
    public void syncSaleOrder(Long startId, Long endId, SyncRequestType syncRequestType) {
        log.info(
                "SaleOrderService : sale order sync request by Id range startId: {} | endId:{} | syncRequestType:{}",
                startId,
                endId,
                syncRequestType);
        try {
            int page = 0;
            int count = 0;
            Pair<Set<String>, Set<String>> saleOrderAndPackingSlipEligibleStatus = getEligibleSaleOrderAndPackingSlipStatusBasedOnRequest(
                    syncRequestType);
            while (count < maxCount) {
                Pageable pageable = PageRequest.of(page, pageLimit, Sort.by("updated_at").ascending());
                List<SaleOrder> saleOrderEligibleItemList = saleOrderWriteRepository.findSaleOrderDataByIdRangeAndSaleOrderAndPackingSlipStatus(
                        startId,
                        endId,
                        saleOrderAndPackingSlipEligibleStatus.getLeft(),
                       saleOrderAndPackingSlipEligibleStatus.getRight(),
                        pageable);
                log.info(
                        "SaleOrderService : sale order eligible Items filtered by Id range size: {} | page:{} | maxCount:{}",
                        saleOrderEligibleItemList.size(),
                        page,
                        maxCount);
                if (CollectionUtils.isEmpty(saleOrderEligibleItemList)) {
                    break;
                }
                page = page + 1;
                count = count + saleOrderEligibleItemList.size();
                if (SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(syncRequestType)) {
                    filterAndPublishPackagingSlipOnlyEvents(saleOrderEligibleItemList);
                }
                publishSaleOrderItems(saleOrderEligibleItemList, syncRequestType);
            }
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred while sale order sync by id range", ex);
        }
    }

    @Override
    public void syncSaleOrderNewEntries(SchedulerConfig schedulerConfig) {
        try {
            int page = 0;
            int count = 0;
            int totalRecordsProcessed = 0;
            long startId = Long.parseLong(schedulerConfig.getLastProcessed()) + 1;
            long endId = startId + Long.parseLong(schedulerConfig.getRecordInterval());
            if (startId >= saleOrderWriteRepository.findLastId()) {
                log.info(
                        "Terminating new sale-order sync job as all new rows have been processed, last Id Processed : {}",
                        startId);
                return;
            }
            while (count < Long.parseLong(schedulerConfig.getRecordInterval())) {
                Pageable pageable = PageRequest.of(page, schedulerConfig.getRecordLimit(), Sort.by("id").ascending());
                List<SaleOrder> saleOrderEligibleItemList = saleOrderWriteRepository.findNonSyncedSaleOrderRecords(
                        startId,
                        endId,
                        pageable);
                log.info(
                        "SaleOrderService : sale order eligible Items filtered by Id range size: {} | page:{} | maxCount:{}",
                        saleOrderEligibleItemList.size(),
                        page,
                        schedulerConfig.getRecordInterval());
                if (CollectionUtils.isEmpty(saleOrderEligibleItemList)) {
                    break;
                }
                page = page + 1;
                count = count + saleOrderEligibleItemList.size();
                filterAndPublishPackagingSlipOnlyEvents(saleOrderEligibleItemList);
                publishSaleOrderItems(saleOrderEligibleItemList, SyncRequestType.SALE_ORDER_AND_PACKING_SLIP);
                totalRecordsProcessed=totalRecordsProcessed+saleOrderEligibleItemList.size();
            }
            schedulerConfigService.updateLastProcessedForSchedulerConfig(schedulerConfig,
                                                                          String.valueOf(endId));
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred while sale order sync by id range", ex);
        }
    }

    @Override
    public void syncSaleOrderRetryForFailureEntries(SchedulerConfig schedulerConfig) {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(Long.parseLong(schedulerConfig.getRecordInterval()));
        List<SaleOrder> saleOrderList = saleOrderWriteRepository.findSaleOrderFailureRecords(schedulerConfig.getRecordLimit(),
                                                                                             retryCount,
                                                                                             eligibleEventErrorTypesForSaleOrders,
                                                                                             localDateTime);
        log.info("SaleOrderService : sale order eligible failure Items size: {} | retry count:{} | limit:{}",
                saleOrderList.size(),
                retryCount,
                schedulerConfig.getRecordLimit());
        filterAndPublishPackagingSlipOnlyEvents(saleOrderList);
        publishSaleOrderItems(saleOrderList, SyncRequestType.SALE_ORDER);
    }
    @Override
    public void syncPackingSlipRetryForFailureEntries(SchedulerConfig schedulerConfig) {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(Long.parseLong(schedulerConfig.getRecordInterval()));
        List<SaleOrder> saleOrderList = saleOrderWriteRepository.findPackingSlipFailureRecords(schedulerConfig.getRecordLimit(),
                                                                                             packingSlipRetryCount,
                                                                                             eligibleEventErrorTypesForSaleOrders,
                                                                                             localDateTime);
        log.info("SaleOrderService : packing slip eligible failure Items size: {} | retry count:{} | limit:{}",
                 saleOrderList.size(),
                 packingSlipRetryCount,
                 schedulerConfig.getRecordLimit());
        filterAndPublishPackagingSlipOnlyEvents(saleOrderList);
        publishSaleOrderItems(saleOrderList, SyncRequestType.PACKING_SLIP);
    }

    @Override
    public void syncSaleOrderNewEntriesByDateRange(SchedulerConfig schedulerConfig) {
        try {
            int page = 0;
            int count = 0;
            LocalDateTime startDate = DateUtils.getDateFromString(schedulerConfig.getLastProcessed());
            LocalDateTime toDate =  startDate.plusDays(Long.parseLong(schedulerConfig.getRecordInterval()));
            while (count < schedulerConfig.getRecordLimit()) {
                Pageable pageable = PageRequest.of(page, pageLimit, Sort.by("updated_at").ascending());
                log.info(
                        "SaleOrderService : sale order sync new entries by date range from Date: {} | to Date: {} ",
                        startDate,
                        toDate);
                List<SaleOrder> saleOrderEligibleItemList = saleOrderWriteRepository.findNonSyncedSaleOrderRecordsByDateRange(
                        startDate,
                        toDate,
                        pageable);
                if (ListUtils.isEmpty(saleOrderEligibleItemList)) {
                    startDate = toDate;
                    toDate.plusDays(Long.valueOf(schedulerConfig.getRecordInterval()));
                }
                log.info(
                        "SaleOrderService : sale order sync new entries eligible Items filtered by Date range size : {} | page:{} | maxCount:{}",
                        saleOrderEligibleItemList.size(),
                        page,
                        schedulerConfig.getRecordLimit());
                if (CollectionUtils.isEmpty(saleOrderEligibleItemList)) {
                    break;
                }
                page = page + 1;
                count = count + saleOrderEligibleItemList.size();
                filterAndPublishPackagingSlipOnlyEvents(saleOrderEligibleItemList);
                publishSaleOrderItems(saleOrderEligibleItemList, SyncRequestType.SALE_ORDER_AND_PACKING_SLIP);
            }
            schedulerConfigService.updateLastProcessedForSchedulerConfig(schedulerConfig,
                                                                        DateUtils.getStringFromDate(toDate));
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred while sale order sync by date range", ex);
        }
    }


    private void filterAndPublishPackagingSlipOnlyEvents(List<SaleOrder> saleOrderEligibleItemList) {
        List<SaleOrder> saleOrderListWithSuccess = new ArrayList<>();
        saleOrderEligibleItemList.stream().forEach(saleOrder -> {
            if (D365ResponseType.SUCCESS.equals(saleOrder.getD365SyncSaleOrderStatus())) {
                saleOrderListWithSuccess.add(saleOrder);
            }
        });
        saleOrderEligibleItemList.removeAll(saleOrderListWithSuccess);
        if (!saleOrderListWithSuccess.isEmpty()) {
            publishSaleOrderItems(saleOrderListWithSuccess, SyncRequestType.PACKING_SLIP);
        }
    }

    @Override
    public void processSaleOrderForNewInstance(SaleOrderSyncConsumerDto saleOrderSyncConsumerDto) throws Exception {
        log.error("Processing sale order for new instance :{}", saleOrderSyncConsumerDto);
        SaleOrder saleOrder = saleOrderWriteRepository.findById(saleOrderSyncConsumerDto.getId()).orElseThrow(() -> new RecordNotFoundException(
                "Sale order not found"));
        List<Integer> uwItemIds = Stream.of(saleOrderSyncConsumerDto.getUwItemIds().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        switch (saleOrderSyncConsumerDto.getSyncRequestType()) {
            case SALE_ORDER:
                if(D365ResponseType.SUCCESS.equals(saleOrder.getD365SyncSaleOrderStatus())) {
                    return;
                }
                saleOrder.setD365SyncSaleOrderStatus(D365ResponseType.IN_PROGRESS);
                saleOrderWriteRepository.save(saleOrder);
                createSalesOrderForNewInstance(saleOrderSyncConsumerDto.getEntityId(),saleOrder.getFacilityCode(),
                                               uwItemIds,
                                               Constants.SalesOrder.CREATE_PROD_URL_NEW_INSTANCE,
                                               null,
                                               NewInstanceDto.builder()
                                                       .isNewInstance(true)
                                                       .id(saleOrderSyncConsumerDto.getId())
                                                       .syncRequestType(SyncRequestType.SALE_ORDER)
                                                       .build());
                break;
            case SALE_ORDER_AND_PACKING_SLIP:
                if (D365ResponseType.SUCCESS.equals(saleOrder.getD365SyncSaleOrderStatus())) {
                    if (D365ResponseType.SUCCESS.equals(saleOrder.getD365SyncPackingSlipStatus())) {
                        return;
                    } else {
                        saleOrder.setD365SyncPackingSlipStatus(D365ResponseType.IN_PROGRESS);
                        saleOrderWriteRepository.save(saleOrder);
                        createPackingSlipForNewInstance(uwItemIds,
                                                        NewInstanceDto.builder()
                                                                .isNewInstance(true)
                                                                .id(saleOrderSyncConsumerDto.getId())
                                                                .syncRequestType(SyncRequestType.PACKING_SLIP)
                                                                .build(), saleOrder.getFacilityCode());
                    }
                } else {
                    saleOrder.setD365SyncPackingSlipStatus(D365ResponseType.IN_PROGRESS);
                    saleOrder.setD365SyncSaleOrderStatus(D365ResponseType.IN_PROGRESS);
                    saleOrderWriteRepository.save(saleOrder);
                    createSalesOrderForNewInstance(saleOrderSyncConsumerDto.getEntityId(),saleOrder.getFacilityCode(),
                                                   uwItemIds,
                                                   Constants.SalesOrder.CREATE_PROD_URL_NEW_INSTANCE,
                                                   null,
                                                   NewInstanceDto.builder()
                                                           .isNewInstance(true)
                                                           .id(saleOrderSyncConsumerDto.getId())
                                                           .syncRequestType(SyncRequestType.SALE_ORDER_AND_PACKING_SLIP)
                                                           .build());
                }
                break;
            case PACKING_SLIP:
                if(D365ResponseType.SUCCESS.equals(saleOrder.getD365SyncPackingSlipStatus())) {
                    return;
                }
                saleOrder.setD365SyncPackingSlipStatus(D365ResponseType.IN_PROGRESS);
                saleOrderWriteRepository.save(saleOrder);
                createPackingSlipForNewInstance(uwItemIds,
                                                NewInstanceDto.builder()
                                                        .isNewInstance(true)
                                                        .id(saleOrderSyncConsumerDto.getId())
                                                        .syncRequestType(SyncRequestType.PACKING_SLIP)
                                                        .build(),saleOrder.getFacilityCode());
                break;
            default:
                log.error("Invalid syncRequestType :{}", saleOrderSyncConsumerDto.getSyncRequestType());
        }
    }
    @Override
    @Transactional(transactionManager = "financeDbTransactionManager")
    public void acknowledgeD365Response(D365ResponseDto d365ResponseDto, String updatedBy) throws RecordNotFoundException {
        log.info("SalesOrderServiceImpl: persisting d365ResponseDto acknowledgement response :{}", d365ResponseDto);
        FinanceSourceSystemSyncEvent financeSourceSystemSyncEvent = d365ResponseDto.getEvent();
        SaleOrder saleOrder = saleOrderWriteRepository.findById(d365ResponseDto.getId()).orElseThrow(() -> new RecordNotFoundException(
                "Sale order not found"));
        if (Objects.nonNull(saleOrder)) {
            saleOrder.setUpdatedBy(updatedBy);
            saleOrder.setErrorMessage(d365ResponseDto.getErrorMessage());
            switch (financeSourceSystemSyncEvent) {
                case SALE_ORDER:
                    if (SaleOrderUtil.isSaleOrderSynced(d365ResponseDto.getErrorMessage())) {
                        log.info("already exist check ");
                       d365ResponseDto.setD365SyncStatus(D365ResponseType.SUCCESS.name());
                    }
                    saleOrder.setD365SyncSaleOrderStatus(D365ResponseType.valueOf(d365ResponseDto.getD365SyncStatus()));
                    saleOrder.setRetryCount(saleOrder.getRetryCount() + 1);
                    saleOrder.setErrorType(failureEventHelper.findEventErrorType(d365ResponseDto.getErrorMessage()));
                    if (D365ResponseType.FAILED.equals(D365ResponseType.valueOf(d365ResponseDto.getD365SyncStatus())) && D365ResponseType.IN_PROGRESS.equals(
                            saleOrder.getD365SyncPackingSlipStatus())) {
                        saleOrder.setD365SyncPackingSlipStatus(D365ResponseType.FAILED);
                    }
                    break;
                case PACKING_SLIP:
                    saleOrder.setD365SyncPackingSlipStatus(D365ResponseType.valueOf(d365ResponseDto.getD365SyncStatus()));
                    saleOrder.setPackingSlipRetryCount(saleOrder.getPackingSlipRetryCount() + 1);
                    saleOrder.setErrorType(failureEventHelper.findEventErrorType(d365ResponseDto.getErrorMessage()));
                    break;
                default:
                    log.error("invalid event type FinanceSourceSystemSyncEvent", d365ResponseDto.getEvent());
                    break;
            }
            saleOrderWriteRepository.save(saleOrder);
        }
    }

    @Override
    public void triggerFailureStrategySaleOrder(SchedulerConfig schedulerConfig) {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(Long.parseLong(schedulerConfig.getRecordInterval()));
        try {
            int page = 0;
            int count = 0;
            while (count < maxCount) {
                Pageable pageable = PageRequest.of(page, pageLimit, Sort.by("updated_at").ascending());
                List<SaleOrder> saleOrderWithFailure = saleOrderWriteRepository.findFailureEntriesByErrorTypesSaleOrder(pageable,
                                                                                                                        eligibleEventErrorTypesForSaleOrders,
                                                                                                                        retryCount,
                                                                                                                        localDateTime);
                log.info(
                        "SaleOrderService : sale order eligible Items filtered by status size: {} | page:{} | maxCount:{}",
                        saleOrderWithFailure.size(),
                        page,
                        maxCount);
                if (CollectionUtils.isEmpty(saleOrderWithFailure)) {
                    break;
                }
                page = page + 1;
                count = count + saleOrderWithFailure.size();
                publishSaleOrderItemsForFailureStrategy(saleOrderWithFailure);
            }
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred for sale order failure strategy:{}",
                      ex.getMessage(),
                      ex);
        }
    }

    @Override
    public void triggerFailureStrategyPackingSlip(SchedulerConfig schedulerConfig) {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(Long.parseLong(schedulerConfig.getRecordInterval()));
        try {
            int page = 0;
            int count = 0;
            while (count < maxCount) {
                Pageable pageable = PageRequest.of(page, pageLimit, Sort.by("updated_at").ascending());
                List<SaleOrder> packingSlipWithFailure = saleOrderWriteRepository.findFailureEntriesByErrorTypesPackingSlip(
                        pageable,
                        eligibleEventErrorTypesForSaleOrders,
                        packingSlipRetryCount,
                        localDateTime);
                log.info(
                        "SaleOrderService : packing slip eligible Items filtered by status size: {} | page:{} | maxCount:{}",
                        packingSlipWithFailure.size(),
                        page,
                        maxCount);
                if (CollectionUtils.isEmpty(packingSlipWithFailure)) {
                    break;
                }
                page = page + 1;
                count = count + packingSlipWithFailure.size();
                publishSaleOrderItemsForFailureStrategy(packingSlipWithFailure);
            }
        } catch (Exception ex) {
            log.error("SaleOrderService :error occurred for packing slip failure strategy : {}",
                      ex.getMessage(),
                      ex);
        }
    }

    @Override
    public void processFailureStrategyForSaleOrder(SaleOrderSyncConsumerDto saleOrderSyncConsumerDto) {
        SaleOrderStrategy saleOrderStrategy = saleOrderFailureStrategyService.findFailureStrategy(
                saleOrderSyncConsumerDto.getEventErrorType());
        try {
            if (Objects.nonNull(saleOrderStrategy)) {
                saleOrderStrategy.execute(saleOrderSyncConsumerDto);

            } else {
                log.info("failureEventStrategy not found: {}", saleOrderSyncConsumerDto);
            }
        } catch (Exception e) {
            log.error("Error occurred while executing strategy for saleOrderSyncConsumerDto: {} with exception {}",
                      saleOrderSyncConsumerDto, e);
        }
    }

    private void publishSaleOrderItemsForFailureStrategy(List<SaleOrder> saleOrderEligibleItemList) {
        saleOrderEligibleItemList.stream().forEach(saleOrder -> {
            SaleOrderSyncConsumerDto saleOrderSyncConsumerDto = SaleOrderSyncConsumerDto.builder()
                    .entityId(saleOrder.getEntityId())
                    .id(saleOrder.getId())
                    .uwItemIds(saleOrder.getUwItemIds())
                    .eventErrorType(saleOrder.getErrorType())
                    .build();
            try {
                kafkaProducerTemplate.send(saleOrderFailureStrategyTopic,
                                           ObjectHelper.convertToString(saleOrderSyncConsumerDto));
            } catch (Exception e) {
                log.error("SaleOrderService :error occurred while publishing packet to sale order topic:{}",
                          e);
            }
        });
    }

    private void publishSaleOrderItems(List<SaleOrder> saleOrderEligibleItemList, SyncRequestType syncRequestType) {
        saleOrderEligibleItemList.forEach(saleOrder -> {
            SaleOrderSyncConsumerDto saleOrderSyncConsumerDto = SaleOrderSyncConsumerDto.builder()
                    .syncRequestType(syncRequestType)
                    .entityId(saleOrder.getEntityId())
                    .id(saleOrder.getId())
                    .uwItemIds(saleOrder.getUwItemIds())
                    .build();
            try {
                kafkaProducerTemplate.send(saleOrderRetryTopicForNewInstance,
                                           ObjectHelper.convertToString(saleOrderSyncConsumerDto));
            } catch (Exception e) {
                log.error("SaleOrderService :error occurred while publishing packet to sale order topic",
                          e);
            }
        });
    }


    private boolean isEligibleForSO(String country){
        if(!CollectionUtils.isEmpty(saleOrderEligibleCountry) && saleOrderEligibleCountry.contains(country)){
            return true;
        }
        return false;
    }

    private boolean isEligibleForPS(String country){
        if(!CollectionUtils.isEmpty(packingSlipEligibleCountry) && packingSlipEligibleCountry.contains(country)){
            return true;
        }
        return false;
    }

    @Override
    public SaleOrderPackingSlipResponseDTO createSalesOrder(String shipmentId, List<Integer> uwItemIds, String saleOrderUrl, SaleOrderSync saleOrderSync){
        return createSalesOrder(shipmentId,uwItemIds,saleOrderUrl,saleOrderSync, false, NewInstanceDto.builder().isNewInstance(false).syncRequestType(
                SyncRequestType.SALE_ORDER_AND_PACKING_SLIP).build());
    }
    public SaleOrderPackingSlipResponseDTO createSalesOrderForNewInstance(String shipmentId,String warehouseFacilityCode , List<Integer> uwItemIds, String saleOrderUrl, SaleOrderSync saleOrderSync, NewInstanceDto newInstanceDto){
        log.info("[SalesOrderServiceImpl] : create sale order for new instance newInstanceDto :{} ", newInstanceDto);
        return createSalesOrder(shipmentId,warehouseFacilityCode,uwItemIds,saleOrderUrl,saleOrderSync, false, newInstanceDto);
    }

    @SneakyThrows
    @Override
    public SaleOrderPackingSlipResponseDTO createSalesOrder(String shipmentId, List<Integer> uwItemIds, String saleOrderUrl, SaleOrderSync saleOrderSync, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto) {
    return createSalesOrder(shipmentId,null,uwItemIds,saleOrderUrl, saleOrderSync,isForceSyncForShippingChargeEnabled,newInstanceDto);
    }


    @SneakyThrows
    public SaleOrderPackingSlipResponseDTO createSalesOrder(String shipmentId,String warehouseFacilityCode ,List<Integer> uwItemIds, String saleOrderUrl, SaleOrderSync saleOrderSync, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto){
        //for DTC orders we receive all the uw item ids
        //for B2B orders we receive only uw item ids of DK to customer orders
        SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO = new SaleOrderPackingSlipResponseDTO();
        UwOrder uwOrder = uwOrdersReadRepository.findByUwItemId(uwItemIds.get(0));
        OrdersHeader ordersHeader = orderHeaderService.getOrderHeader(uwOrder.getIncrementId());
        String shipment;
        String facilityCode = null;
        if (("B2B").equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            UwOrder firstUwOrderLK = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
            HubMaster hubMaster = hubMasterReadRepository.findByFacilityCode(firstUwOrderLK.getFacilityCode());
            if (Objects.nonNull(hubMaster)) {
                shipment = firstUwOrderLK.getShippingPackageId();
                facilityCode = firstUwOrderLK.getFacilityCode();
            } else {
                shipment = uwOrder.getShippingPackageId();
                facilityCode = uwOrder.getFacilityCode();
            }
        } else {
            shipment = uwOrder.getShippingPackageId();
            facilityCode = uwOrder.getFacilityCode();
        }
        FinanceSourceSystemSync financeSourceSystemSync = saleOrderUtil.fetchFinanceSourceSystemRecord(shipmentId, facilityCode);
        long financeSourceSystemId = financeSourceSystemSync.getId();

        if(!isEligibleForSO(ordersHeader.getLkCountry())){
            if(!newInstanceDto.getIsNewInstance()){
                payloadBuildUtils.acknowdegeFailureToSourceSystem(shipment, Constants.SalesOrder.OrderType,new Exception("Skipping this Sale order as it belongs to "+ordersHeader.getLkCountry()), facilityCode);
            }
            else {
                acknowledgeD365Response(D365ResponseDto.builder()
                                                .event(FinanceSourceSystemSyncEvent.SALE_ORDER)
                                                .id(newInstanceDto.getId())
                                                .d365SyncStatus("FAILED")
                                                .errorMessage(genericClientService.getErrorMessage(new Exception("Skipping this Sale order as it belongs to "+ordersHeader.getLkCountry())))
                                                .build(), "finance-consumer");
                if (SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(newInstanceDto.getSyncRequestType())) {
                    acknowledgeD365Response(D365ResponseDto.builder()
                                                    .event(FinanceSourceSystemSyncEvent.PACKING_SLIP)
                                                    .id(newInstanceDto.getId())
                                                    .d365SyncStatus("FAILED")
                                                    .errorMessage(genericClientService.getErrorMessage(new Exception("Skipping this Sale order as it belongs to "+ordersHeader.getLkCountry())))
                                                    .build(), "finance-consumer");
                }
            }
            return saleOrderPackingSlipResponseDTO;
        }

        if (forwardTransferOrderService.isValidTransferOrder(financeSourceSystemSync)) {
            forwardTransferOrderService.createTransferOrderForShippingIdAndFacilityCode(shipmentId, facilityCode, financeSourceSystemSync);
            return null;    // terminating flow as transfer order will be triggered for this shipment
        }

        if (forwardTransferJournalService.isValidTransferJournal(financeSourceSystemSync)) {
            forwardTransferJournalService.createTransferJournalForShippingIdAndFacilityCode(shipmentId, facilityCode, financeSourceSystemSync);
            return null;
        }


        String message = null;
        String status = null;
        Object payload = null;
        try {
            String baseUrl;
            String packingSlipBaseUrl;
            if (saleOrderUrl.equals(Constants.SalesOrder.CREATE_PROD_URL) || saleOrderUrl.equals(Constants.SalesOrder.CREATE_PROD_SO_URL)) {
                baseUrl = D365ProdUrl;
            } else {
                baseUrl = D365BaseUrl;
            }
            if (packingSlipLive.equalsIgnoreCase("true")) {
                packingSlipBaseUrl = D365ProdUrl;
            } else {
                packingSlipBaseUrl = D365BaseUrl;
            }
            payload = generatePayload(uwItemIds, baseUrl, saleOrderSync,isForceSyncForShippingChargeEnabled,newInstanceDto);
            ResponseEntity responseEntity;
            try {
                if (newInstanceDto.getIsNewInstance()) {
                    D365RequestDto d365RequestDto = D365RequestDto.builder()
                            .requestPayload(payload)
                            .id(newInstanceDto.getId())
                            .event(FinanceSourceSystemSyncEvent.SALE_ORDER.toString())
                            .warehouseFacilityCode(facilityCode)
                            .build();
                    log.info("[SalesOrderServiceImpl][createSalesOrder] Request for entity id {} | payload {}", shipment, d365RequestDto);
                    responseEntity = genericClientService.forwardRequest(financeAdapterUrl + saleOrderUrl, new HttpHeaders(), HttpMethod.POST, d365RequestDto);
                } else {
                    D365RequestDto d365RequestDtoSaleOrderOld = D365RequestDto.builder()
                            .requestPayload(payload)
                            .event(FinanceSourceSystemSyncEvent.SALE_ORDER.toString())
                            .id(financeSourceSystemId)
                            .warehouseFacilityCode(facilityCode)
                            .build();
                    log.info("[SalesOrderServiceImpl][createSalesOrder] Request for entity id {} | payload {}", shipment, d365RequestDtoSaleOrderOld);
                    responseEntity = genericClientService.forwardRequest(financeAdapterUrl + saleOrderUrl, new HttpHeaders(), HttpMethod.POST, d365RequestDtoSaleOrderOld);

                }
            } catch (Exception e) {
                if(!newInstanceDto.getIsNewInstance()){
                    genericClientService.saveLog(shipmentId, genericClientService.getErrorMessage(e), payload,
                                                 baseUrl + Constants.SalesOrder.URL, Constants.SalesOrder.OrderType, Constants.Common.FAILURE);
                }
                throw e;
            }
            if (!responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                HashMap responseBody = (HashMap) responseEntity.getBody();
                message = (String) responseBody.get("Message");
                if (newInstanceDto.getIsNewInstance()) {
                    acknowledgeD365Response(D365ResponseDto.builder()
                                                    .event(FinanceSourceSystemSyncEvent.SALE_ORDER)
                                                    .id(newInstanceDto.getId())
                                                    .d365SyncStatus("FAILED")
                                                    .errorMessage(message)
                                                    .build(), "finance-consumer");
                    if (SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(newInstanceDto.getSyncRequestType())) {
                        acknowledgeD365Response(D365ResponseDto.builder()
                                                        .event(FinanceSourceSystemSyncEvent.PACKING_SLIP)
                                                        .id(newInstanceDto.getId())
                                                        .d365SyncStatus("FAILED")
                                                        .errorMessage(message)
                                                        .build(), "finance-consumer");
                    }
                }
                else{
                    genericClientService.saveLog(shipmentId, message, payload, baseUrl + Constants.SalesOrder.URL,
                                                 Constants.SalesOrder.OrderType, Constants.Common.FAILURE);
                    payloadBuildUtils.acknowdegeFailureToSourceSystem(shipment, Constants.SalesOrder.OrderType,new Exception(message), facilityCode);
                    payloadBuildUtils.acknowdegeFailureToSourceSystem(shipment, Constants.PackingSlip.OrderType,new Exception(message), facilityCode);
                }
                saleOrderPackingSlipResponseDTO.setSaleOrderResponse(responseEntity);
                return saleOrderPackingSlipResponseDTO;
            }
            saleOrderPackingSlipResponseDTO.setSaleOrderResponse(responseEntity);
            HashMap responseBody = (HashMap) responseEntity.getBody();

            message = (String) responseBody.get("Message");
            Boolean responseStatus = Boolean.valueOf(String.valueOf(responseBody.get("Success")));
            if (responseStatus) {
                status = Constants.Common.SUCCESS;
                Order order= getOrderDetailsByItemId(uwOrder.getItemId());
                if(Objects.nonNull(order) && isOrderExchangeType(order)){
                    publishExchangeTypeOrderToD365Exchange(order);
                }
            } else {
                status = Constants.Common.FAILURE;
            }

            if (SaleOrderUtil.isSaleOrderSynced(message)) {
                log.info("already exist check ");
                status = Constants.Common.SUCCESS;
            }
            if (packingSlipLive.equalsIgnoreCase("true") && status != null && status.equals("Success") && SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(
                    newInstanceDto.getSyncRequestType())) {
                SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO1 = generatePackingSlip(shipmentId,
                                                                                                       uwItemIds,
                                                                                                       saleOrderPackingSlipResponseDTO,
                                                                                                       packingSlipBaseUrl,
                                                                                                       responseEntity,
                                                                                                       status,
                                                                                                       isForceSyncForShippingChargeEnabled,newInstanceDto,
                                                                                                       facilityCode);
                if (saleOrderPackingSlipResponseDTO1 != null) {
                    return saleOrderPackingSlipResponseDTO1;
                }
            }
            return saleOrderPackingSlipResponseDTO;
        } catch (Exception e) {
            log.error("SaleOrderServiceImpl Exception:{}",e.getMessage(),e);
            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> body = new HashMap<String, Object>();
            body.put("Message", genericClientService.getErrorMessage(e));
            body.put("Success", false);
            ResponseEntity response = new ResponseEntity(body, header, HttpStatus.INTERNAL_SERVER_ERROR);
            saleOrderPackingSlipResponseDTO.setSaleOrderResponse(response);
            log.error(e.getMessage());
            //genericClientService.saveLog(String.valueOf(uwOrdersService.getUwOrderByUwItemId(uwItemIds.get(0))
            // .getShippingPackageId()),e.getMessage(),null,D365BaseUrl +Constants.SalesOrder.URL,Constants
            // .SalesOrder.OrderType,Constants.Common.FAILURE);
            if (newInstanceDto.getIsNewInstance()) {
                acknowledgeD365Response(D365ResponseDto.builder()
                                                .event(FinanceSourceSystemSyncEvent.SALE_ORDER)
                                                .id(newInstanceDto.getId())
                                                .d365SyncStatus("FAILED")
                                                .errorMessage(genericClientService.getErrorMessage(e))
                                                .build(), "finance-consumer");
                if (SyncRequestType.SALE_ORDER_AND_PACKING_SLIP.equals(newInstanceDto.getSyncRequestType())) {
                    acknowledgeD365Response(D365ResponseDto.builder()
                                                    .event(FinanceSourceSystemSyncEvent.PACKING_SLIP)
                                                    .id(newInstanceDto.getId())
                                                    .d365SyncStatus("FAILED")
                                                    .errorMessage(genericClientService.getErrorMessage(e))
                                                    .build(), "finance-consumer");
                }
            }
            else {
                payloadBuildUtils.acknowdegeFailureToSourceSystem(shipmentId, Constants.SalesOrder.OrderType, e, facilityCode);
            }
            return saleOrderPackingSlipResponseDTO;
        } finally {
            if(!newInstanceDto.getIsNewInstance()){
                genericClientService.saveLog(shipmentId, message, payload, D365BaseUrl + Constants.SalesOrder.URL,
                                             Constants.SalesOrder.OrderType, Constants.Common.SUCCESS.equals(status) ? Constants.Common.SUCCESS :
                                                     Constants.Common.FAILURE);
            }
        }
    }

    private Order getOrderDetailsByItemId(Integer itemId) {
        Order order = orderRepository.getByItemId(itemId);
        log.info("Order fetched from db for uwItemId : {} is :{}", itemId, order);
        return order;
    }

    private void publishExchangeTypeOrderToD365Exchange(Order order) throws JsonProcessingException {
        Map<String, Integer> map = preparePayloadForExchangeSync(order);
        log.info("Pushing data to kafka topic for exchange order types :{}", map);
        kafkaProducerTemplate.send(D365ExchangeTopic, objectMapper.writeValueAsString(map));
    }

    private boolean isOrderExchangeType(Order order) {
        Boolean flag = false;
        if (StringUtils.isNotEmpty(order.getMethod()) && EXCHANGE.equals(order.getMethod())) {
            flag = true;
        }
        return flag;
    }

    private Map<String, Integer> preparePayloadForExchangeSync(Order order) {
        Map<String, Integer> map = new HashMap<>();
        map.put(ORDER_ID, order.getIncrementId());
        return map;
    }
    protected  SaleOrderPackingSlipResponseDTO generatePackingSlip(String shipmentId, List<Integer> uwItemIds, SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO, String packingSlipBaseUrl,
                                                                   ResponseEntity responseEntity, String status, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto, String facilityCode) throws Exception {
        return generatePackingSlip(shipmentId,facilityCode ,uwItemIds, saleOrderPackingSlipResponseDTO, packingSlipBaseUrl,
                responseEntity, status, isForceSyncForShippingChargeEnabled, newInstanceDto);
    }
    protected SaleOrderPackingSlipResponseDTO generatePackingSlip(String shipmentId,String warehouseFacilityCode ,List<Integer> uwItemIds, SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO, String packingSlipBaseUrl,
                                                                  ResponseEntity responseEntity, String status, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto) throws Exception {
        PackingSlip packingSlip = generatePackingSlipPayload(uwItemIds ,isForceSyncForShippingChargeEnabled, newInstanceDto);
        ResponseEntity responseEntityPacking;
        D365RequestDto d365RequestDto = D365RequestDto.builder()
                .requestPayload(packingSlip)
                .event(FinanceSourceSystemSyncEvent.PACKING_SLIP.toString())
                .id(newInstanceDto.getId())
                .warehouseFacilityCode(warehouseFacilityCode)
                .build();
        log.info("[SalesOrderServiceImpl][generatePackingSlip] Request for entity id {} | payload {}", shipmentId, d365RequestDto);
        FinanceSourceSystemSync financeSourceSystemSync=financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.PACKING_SLIP,
                                                                                                                                       FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                                                                                                                                       shipmentId,
                                                                                                                                       warehouseFacilityCode);
        if(Objects.isNull(financeSourceSystemSync)){
            throw new RecordNotFoundException("shipment record not available in finance_source_system_sync table , shipmentId : {}"+ shipmentId, HttpStatus.BAD_REQUEST);
        }
        try {
            if(newInstanceDto.getIsNewInstance()){
                responseEntityPacking = genericClientService.forwardRequest(financeAdapterUrl +Constants.PackingSlip.CREATE_URL_NEW_INSTANCE , new HttpHeaders(), HttpMethod.POST, d365RequestDto);
            }
            else {
                D365RequestDto d365RequestDtoPackingSlipOld = D365RequestDto.builder()
                        .requestPayload(packingSlip)
                        .event(FinanceSourceSystemSyncEvent.PACKING_SLIP.toString())
                        .id(financeSourceSystemSync.getId())
                        .warehouseFacilityCode(warehouseFacilityCode)
                        .build();
                log.info("[SalesOrderServiceImpl][generatePackingSlip] Request for entity id {} | payload {}", shipmentId, d365RequestDtoPackingSlipOld);
                responseEntityPacking = genericClientService.forwardRequest(financeAdapterUrl +Constants.PackingSlip.CREATE_URL , new HttpHeaders(), HttpMethod.POST, d365RequestDtoPackingSlipOld);
            }
            log.info("[SaleOrderServiceImpl][PackingSlip] response " + responseEntity.getBody());
        } catch (Exception e) {
            if (newInstanceDto.getIsNewInstance()) {
                acknowledgeD365Response(D365ResponseDto.builder()
                                                .event(FinanceSourceSystemSyncEvent.PACKING_SLIP)
                                                .id(newInstanceDto.getId())
                                                .d365SyncStatus("FAILED")
                                                .errorMessage(genericClientService.getErrorMessage(e))
                                                .build(), "finance-consumer");
            } else {
                genericClientService.saveLog(shipmentId, genericClientService.getErrorMessage(e), packingSlip,
                                             packingSlipBaseUrl + Constants.SalesOrder.URL, Constants.SalesOrder.OrderType, Constants.Common.FAILURE);
            }
            throw e;
        }
        if (!responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            HashMap responseBodyP = (HashMap) responseEntity.getBody();
            String messageP = (String) responseBodyP.get("Message");
            if(!newInstanceDto.getIsNewInstance()){
                genericClientService.saveLog(shipmentId, String.valueOf(messageP), packingSlip,
                                             packingSlipBaseUrl + Constants.SalesOrder.URL, Constants.SalesOrder.OrderType, Constants.Common.FAILURE);
                saleOrderPackingSlipResponseDTO.setPackingSlipResponse(responseEntityPacking);
            }
            return saleOrderPackingSlipResponseDTO;
        }
        saleOrderPackingSlipResponseDTO.setPackingSlipResponse(responseEntityPacking);
        Map<String, Object> responseBodyPacking = (Map<String, Object>) responseEntityPacking.getBody();
        String messagePacking = (String) responseBodyPacking.get("Message");
        Boolean responseStatusPacking = Boolean.valueOf(String.valueOf(responseBodyPacking.get("Success")));
        String statusPacking;

        if (responseStatusPacking) {
            statusPacking = Constants.Common.SUCCESS;
            if(!newInstanceDto.getIsNewInstance()){
                genericClientService.saveLog(String.valueOf(packingSlip.getPackingSlipId()), messagePacking, packingSlip, packingSlipBaseUrl + Constants.SalesOrder.URL, Constants.SalesOrder.OrderType, statusPacking);
            }
        } else {
            statusPacking = Constants.Common.FAILURE;
        }
        return null;
    }

    @Override
    public ResponseEntity createPackingSlip(List<Integer> uwItemIds) throws Exception {
        return createPackingSlip(uwItemIds,NewInstanceDto.builder().isNewInstance(false).build());
    }
    @Override
    public ResponseEntity createPackingSlipForNewInstance(List<Integer> uwItemIds, NewInstanceDto newInstanceDto,String warehouseFacilityCode) throws Exception {
        return createPackingSlip(uwItemIds,newInstanceDto,warehouseFacilityCode);
    }

    public ResponseEntity createPackingSlip(List<Integer> uwItemIds, NewInstanceDto newInstanceDto) throws Exception {
        return createPackingSlip(uwItemIds,newInstanceDto,null);
    }
        public ResponseEntity createPackingSlip(List<Integer> uwItemIds, NewInstanceDto newInstanceDto,String warehouseFacilityCode) throws Exception {

        UwOrder uwOrder = uwOrdersRepository.findByUwItemId(uwItemIds.get(0));
        OrdersHeader ordersHeader = orderHeaderService.getOrderHeader(uwOrder.getIncrementId());
        String shipment = null;
        String facilityCode = null;
        FinanceSourceSystemSync financeSourceSystemSync =null;
        FinanceSourceSystemSync financeSourceSystemSyncSaleOrder = null;
            if (("B2B").equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
                UwOrder firstUwOrderLK = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
                HubMaster hubMaster = hubMasterRepository.findByFacilityCode(firstUwOrderLK.getFacilityCode());
                if (Objects.nonNull(hubMaster)) {
                    shipment = firstUwOrderLK.getShippingPackageId();
                    facilityCode = firstUwOrderLK.getFacilityCode();
                } else {
                    shipment = uwOrder.getShippingPackageId();
                    facilityCode = uwOrder.getFacilityCode();
                }
            } else {
                shipment = uwOrder.getShippingPackageId();
                facilityCode = uwOrder.getFacilityCode();
            }
        financeSourceSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.PACKING_SLIP,
                                                                                                                 FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                                                                                                                 shipment,
                                                                                                                 facilityCode);

            financeSourceSystemSyncSaleOrder = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.SALE_ORDER,
                                                                                                                     FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                                                                                                                     shipment,
                                                                                                                     facilityCode);
            if(Objects.nonNull(financeSourceSystemSyncSaleOrder) && !D365ResponseType.SUCCESS.name().equalsIgnoreCase(financeSourceSystemSyncSaleOrder.getD365SyncStatus())) {
                String message = "Can not sync packing slip since sale order is not synced fr shipment " + shipment + " and facility code " + facilityCode;
                log.info("[SalesOrderServiceImpl][createPackingSlip] {}", message);
                return new ResponseEntity(message, HttpStatus.OK);
            }
        if (Objects.isNull(financeSourceSystemSync)) {
                throw new RecordNotFoundException("shipment record not available in finance_source_system_sync table , shipmentId : {}"+ shipment, HttpStatus.BAD_REQUEST);
        }
            if(!isEligibleForPS(ordersHeader.getLkCountry())){
            if(!newInstanceDto.getIsNewInstance()){
                payloadBuildUtils.acknowdegeFailureToSourceSystem(shipment, Constants.PackingSlip.OrderType,new Exception("Skipping this Packing slip as it belongs to "+ordersHeader.getLkCountry()), facilityCode);
            } else {
                acknowledgeD365Response(D365ResponseDto.builder()
                                                .event(FinanceSourceSystemSyncEvent.SALE_ORDER)
                                                .id(newInstanceDto.getId())
                                                .d365SyncStatus("FAILED")
                                                .errorMessage(genericClientService.getErrorMessage(new Exception("Skipping this Packing slip as it belongs to " + ordersHeader.getLkCountry())))
                                                .build(), "finance-consumer");
            }
            return new ResponseEntity(HttpStatus.OK);
        }

        String packingSlipBaseUrl;
        if(packingSlipLive.equalsIgnoreCase("true")){
            packingSlipBaseUrl = D365ProdUrl;
        }
        else{
            packingSlipBaseUrl = D365BaseUrl;
        }
        String packingSlipUrl = newInstanceDto.getIsNewInstance() ? Constants.PackingSlip.CREATE_URL_NEW_INSTANCE : Constants.PackingSlip.CREATE_URL;
        try {
            PackingSlip packingSlip = generatePackingSlipPayload(uwItemIds,false, newInstanceDto);
            D365RequestDto d365RequestDto = D365RequestDto.builder()
                    .requestPayload(packingSlip)
                    .id(newInstanceDto.getId())
                    .event(FinanceSourceSystemSyncEvent.PACKING_SLIP.toString())
                    .warehouseFacilityCode(facilityCode)
                    .build();
            log.info("[SalesOrderServiceImpl][createPackingSlip] Request for entity id {} | payload {}", shipment, d365RequestDto);
            ResponseEntity responseEntity;
            if(newInstanceDto.getIsNewInstance()){
                responseEntity = genericClientService.forwardRequest(financeAdapterUrl +packingSlipUrl , new HttpHeaders(), HttpMethod.POST, d365RequestDto);
            }
            else {
                D365RequestDto d365RequestDtoPackingSlipOld = D365RequestDto.builder()
                        .requestPayload(packingSlip)
                        .id(financeSourceSystemSync.getId())
                        .event(FinanceSourceSystemSyncEvent.PACKING_SLIP.toString())
                        .warehouseFacilityCode(facilityCode)
                        .build();
                log.info("[SalesOrderServiceImpl][createPackingSlip] Request for entity id {} | payload {}", shipment, d365RequestDtoPackingSlipOld);
                responseEntity = genericClientService.forwardRequest(financeAdapterUrl +packingSlipUrl , new HttpHeaders(), HttpMethod.POST, d365RequestDtoPackingSlipOld);
            }
            log.info("[SalesOrderConsumer][createSalesOrder] response " + responseEntity.getBody());
            if (!responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                HashMap responseBody = (HashMap) responseEntity.getBody();
                String message = (String) responseBody.get("Message");
                throw new Exception(message);
            }
            HashMap responseBody = (HashMap) responseEntity.getBody();
            //Map<String, Object> responseBody = (Map<String, Object>) responseEntity.getBody();
            String message = (String) responseBody.get("Message");
            Boolean responseStatus = Boolean.valueOf(String.valueOf(responseBody.get("Success")));
            String status;
            if (responseStatus) {
                status = Constants.Common.SUCCESS;
                if(!newInstanceDto.getIsNewInstance())
                {
                    genericClientService.saveLog(String.valueOf(packingSlip.getPackingSlipId()), message, packingSlip, packingSlipBaseUrl + Constants.PackingSlip.URL, Constants.PackingSlip.OrderType, status);
                }
            } else {
                status = Constants.Common.FAILURE;
            }
            return responseEntity;
        } catch (Exception e) {
            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            Map<String,Object> body = new HashMap<String,Object>();
            body.put("Message",genericClientService.getErrorMessage(e));
            body.put("Success",false);
            ResponseEntity response = new ResponseEntity(body, header, HttpStatus.INTERNAL_SERVER_ERROR);
            if (newInstanceDto.getIsNewInstance()) {
                acknowledgeD365Response(D365ResponseDto.builder()
                                                .event(FinanceSourceSystemSyncEvent.PACKING_SLIP)
                                                .id(newInstanceDto.getId())
                                                .d365SyncStatus("FAILED")
                                                .errorMessage(genericClientService.getErrorMessage(e))
                                                .build(), "finance-consumer");
            } else {
                genericClientService.saveLog(String.valueOf(uwOrdersService.getUwOrderByUwItemId(uwItemIds.get(0)).getShippingPackageId()), genericClientService.getErrorMessage(e), null, packingSlipBaseUrl + Constants.PackingSlip.URL, Constants.PackingSlip.OrderType, Constants.Common.FAILURE);
                payloadBuildUtils.acknowdegeFailureToSourceSystem(shipment, Constants.PackingSlip.OrderType,e, facilityCode );
            }
            log.error("[createPackingSlip] " + e.getMessage() + " response " + response);
            return response;
        }
    }

    private void validate(SalesOrderHeader salesOrderHeader) throws Exception {
        boolean flag = true;
        String errMessage = "";
        if (StringUtils.isBlank(salesOrderHeader.getIsWithTax()) ||
                StringUtils.isBlank(salesOrderHeader.getExportReason()) ||
                StringUtils.isBlank(salesOrderHeader.getSalesOrderNumber()) || StringUtils.isBlank(salesOrderHeader.getCurrencyCode()) ||
                StringUtils.isBlank(salesOrderHeader.getInventLocationId())) {
            errMessage = "Please provide all mandatory fields in Sale Order Header: IsWithTax, ExportReason, SalesOrderNumber, CurrencyCode, InventLocationId";
            log.error("[validate] Error occurred " + errMessage);
            flag = false;
        }

        if (StringUtils.isBlank(salesOrderHeader.getCustomerAccount()) || StringUtils.isBlank(salesOrderHeader.getDeliveryName()) || StringUtils.isBlank(salesOrderHeader.getAddressCountryRegionId())) {
            errMessage += "Please provide all mandatory fields in Sale Order Header: CustomerAccount, DeliveryName, AddressCountryRegionId";
            log.error("[validate] Error occurred " + errMessage);
            flag = false;
        }

        List<SoLine> soLines = salesOrderHeader.getSoLines();
        for (SoLine soLine : soLines) {
            if (StringUtils.isBlank(soLine.getItemNumber())) {
                errMessage += "Please provide all mandatory fields in Sale Order : ItemNumber for SO Line" + soLine.getLineNumber();
                log.error("[validate] Error occurred " + errMessage);
                flag = false;
            }
        }
        if (!flag) {
            log.info("[validate] Sale order header payload " + salesOrderHeader);
            throw new Exception(errMessage);
        }
    }

    protected Object generatePayload(List<Integer> uwItemIds, String baseUrl, SaleOrderSync saleOrderSync, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto) throws Exception {
        return generatePayload(uwItemIds, baseUrl,isForceSyncForShippingChargeEnabled,newInstanceDto );
    }

    /***
     * @param uwItemIds these are virtual uw_item ids in cases of B2B orders
     * @param baseUrl
     * @param newInstanceDto
     * @return SalesOrder payload generated
     * @throws Exception
     */
    public SalesOrderHeader generatePayload(List<Integer> uwItemIds, String baseUrl, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto) throws Exception {
        String shipment = "";
        SalesOrderHeader salesOrderHeader = new SalesOrderHeader();
        List<UwOrder> uwOrders = uwOrdersService.getUwOrders(uwItemIds);
        UwOrder firstUwOrder = uwOrders.get(0);
        String facilityCode=null;
        UwOrder firstUwOrderLK = null;
        try {
            log.info("[generatePayload] UWItem Ids :" + uwItemIds);
            if (firstUwOrder.getProductDeliveryType().equals("B2B") && !firstUwOrder.getNavChannel().equalsIgnoreCase(Constants.Common.FOFOB2B)) {
                firstUwOrderLK = uwOrdersService.getUwOrderByUwItemId(firstUwOrder.getB2bRefrenceItemId());
                shipment = firstUwOrderLK.getShippingPackageId();
                // for B2B orders firstUwOrderLK is real order hence its facility code is picked
                facilityCode=firstUwOrderLK.getFacilityCode(); //source warehouse from where order was shipped first
                salesOrderHeader.setFulfillmentWH(facilityCode);
            } else {
                shipment = firstUwOrder.getShippingPackageId();
                facilityCode = firstUwOrder.getFacilityCode();
            }
            if (StringUtils.isBlank(shipment)) {
                throw new Exception(NOT_FOUND_SHIPPING_PACKAGE_ID + firstUwOrder.getUwItemId());
            }
            checkMultipleShipments(uwOrders, shipment);
            int incrementId = firstUwOrder.getIncrementId();
            List<Order> orders = orderService.getOrders(incrementId);
            Order order = orders.get(0);
            DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);
            OrdersHeader ordersHeader = orderHeaderService.getOrderHeader(incrementId);
            OrderAddressUpdate orderAddressUpdateShipping = orderAddressUpdateService.getOrderAddressUpdateShipping(order.getOrderId());
            OrderAddressUpdate orderAddressUpdateBilling = orderAddressUpdateService.getOrderAddressUpdateBilling(order.getOrderId());
            String site = saleOrderUtil.getInventLocationId(firstUwOrder, ordersHeader);
            String subChannel = saleOrderUtil.getSubChannel(firstUwOrder, order);
            salesOrderHeader.setIsUpdateSalesOrder(0);
            salesOrderHeader.setOrderType(Constants.SalesOrder.PayloadOrderType);
            salesOrderHeader.setWebOrderNo(String.valueOf(incrementId));
            InvoiceDetails invoiceDetails = invoiceDetailsService.getInvoiceDetails(firstUwOrder.getShippingPackageId());
            if (firstUwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                if (invoiceDetails != null) {
                    salesOrderHeader.setOrgInvoiceNo(invoiceDetails.getInvoiceCode());
                } else {
                    salesOrderHeader.setOrgInvoiceNo(shipment);
                }
            }
            String suffixValue = "";
            if(!newInstanceDto.getIsNewInstance()) {
                FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.SALE_ORDER,
                                                                                                                                                 FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                                                                                                                                                 shipment,
                                                                                                                                                 facilityCode);
                if (financeSourceSystemSync != null && financeSourceSystemSync.getSuffix() != null) {
                    suffixValue = financeSourceSystemSync.getSuffix();
                }
            } else {
                SaleOrder saleOrder = saleOrderWriteRepository.findById(newInstanceDto.getId()).orElseThrow(() -> new RecordNotFoundException(
                        "[SaleOrderServiceImpl][generatePayload]Sale order record not found"));
                if (saleOrder.getSuffix() != null) {
                    suffixValue = saleOrder.getSuffix();
                }
            }
            salesOrderHeader.setSalesOrderNumber(shipment + "_" + site + suffixValue);
            boolean isExchangeOrder = false;
            if(ordersHeader.getIsExchangeOrder()!= null){
                isExchangeOrder = ordersHeader.getIsExchangeOrder();
            }
            if (isExchangeOrder) {
                ExchangeOrder exchangeOrder = exchangeOrdersReadRepository.findTopByExchangeIncrementId(incrementId);
                if (exchangeOrder != null) {
                    UwOrder uwOrder = uwOrdersReadRepository.findByUwItemId(exchangeOrder.getUwItemId());
                    if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                        UwOrder temp = uwOrdersReadRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());
                        salesOrderHeader.setRefInvoiceNo(temp.getShippingPackageId());
                    } else {
                        salesOrderHeader.setRefInvoiceNo(uwOrder.getShippingPackageId());
                    }
                }
            }
            salesOrderHeader.setCustomerGroup(getCustomerGroup(firstUwOrder, ordersHeader.getLkCountry()));
            salesOrderHeader.setInventLocationId(site);
            salesOrderHeader.setSalesChannel(site);
            salesOrderHeader.setSubChannel(subChannel);
            salesOrderHeader.setStores(saleOrderUtil.getStoreCode(ordersHeader));
            salesOrderHeader.setTCSGroup("");
            salesOrderHeader.setTDSGroup("");
            String currencyCode = ProductUtil.CountryCurrencyMapping.get(ordersHeader.getLkCountry());
            if (currencyCode != null) {
                salesOrderHeader.setCurrencyCode(currencyCode);
            }
            String tax = saleOrderUtil.getTaxCheck(orderAddressUpdateShipping.getCountryId(), ordersHeader, firstUwOrder.getNavChannel());
            salesOrderHeader.setExportReason(tax);
            salesOrderHeader.setIsWithTax(tax);
            salesOrderHeader.setModeOfDelivery(Constants.SalesOrder.ModeOfDelivery);
            salesOrderHeader.setDeliveryName(saleOrderUtil.getDeliveryName(order, orderAddressUpdateShipping));
            saleOrderUtil.setAddress(salesOrderHeader, order, orderAddressUpdateShipping);
            salesOrderHeader.setOrderCreationDate(dateFormat.format(order.getCreatedAt()));
            ShipmentDetails shipmentDetails;
            MpOrderDetails mpOrderDetails = null;

            if (incrementId != 0)
                mpOrderDetails = mpOrderDetailsReadRepository.findOneByIncrementIdNew(incrementId);
            if (mpOrderDetails != null && StringUtils.isNotBlank(mpOrderDetails.getCarrierCode())) {
                salesOrderHeader.setCourierCode(mpOrderDetails.getCarrierCode());
            } else {
                if (!StringUtils.isBlank(shipment) && shipment != null && !uwOrders.get(0).getIsLocalFittingRequired() && !uwOrders.get(0).getProductDeliveryType().equals(Constants.Common.OTC)) {
                    shipmentDetails = courierInfoService.getCourierInfo(shipment);
                    if (shipmentDetails == null) {
                        com.lenskart.wm.model.ShipmentDetails shipmentDetailsMongo = courierInfoService.getCourierInfoFromMongo(shipment, facilityCode);
                        if(StringUtils.isBlank(shipmentDetailsMongo.getTrackingNumber())){
                            ShippingStatus shippingStatus = shippingStatusService.getShippingStatus(shipment);
                            if(shippingStatus!= null && !StringUtils.isBlank(shippingStatus.getCarrierCode())){
                                salesOrderHeader.setCourierCode(shippingStatus.getCarrierCode());
                            }
                            else{
                                salesOrderHeader.setCourierCode("NA");
                            }
                        }
                        else {
                            salesOrderHeader.setCourierCode(shipmentDetailsMongo.getShippingProviderCode());
                        }
                    }
                } else {
                    shipmentDetails = null;
                }
                log.info("[generatePayload] shipmentDetails " + shipmentDetails);
                if (shipmentDetails != null) {
                    salesOrderHeader.setCourierCode(StringUtils.defaultString(shipmentDetails.getCourierCode()).trim());
                } else {
                    ShippingStatus shippingStatus = shippingStatusService.getShippingStatus(shipment);
                    if(shippingStatus!= null && !StringUtils.isBlank(shippingStatus.getCarrierCode())){
                        salesOrderHeader.setCourierCode(shippingStatus.getCarrierCode());
                    }
                    else {
                        salesOrderHeader.setCourierCode("NA");
                    }
                }
            }

            Order Paymentorder = orderService.findOrderByItemId(firstUwOrder.getItemId());
            if (Paymentorder.getMethod().equalsIgnoreCase("cashondelivery")) {
                salesOrderHeader.setModeOfPayment(Constants.SalesOrder.COD);
                salesOrderHeader.setTermsOfPayment(Constants.SalesOrder.COD);
            } else {
                salesOrderHeader.setModeOfPayment(StringUtils.defaultString(Paymentorder.getMethod()).trim());
                salesOrderHeader.setTermsOfPayment("");
            }
            String deliveryName = saleOrderUtil.getDeliveryName(order, orderAddressUpdateShipping);
            salesOrderHeader.setDeliveryName(StringUtils.defaultString(deliveryName).trim());
            setPaymentMethod(salesOrderHeader, firstUwOrder, order, ordersHeader, mpOrderDetails);
            if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
                salesOrderHeader.setLegalEntity("LKSG");
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("AE")) {
                salesOrderHeader.setLegalEntity("LKAE");
            } else if (ordersHeader.getLkCountry().equalsIgnoreCase("US")) {
                salesOrderHeader.setLegalEntity("LKUS");
            } else if ((firstUwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B) && !firstUwOrder.getNavChannel().equalsIgnoreCase(Constants.Common.FOFOB2B)) || firstUwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
                if(Constants.Common.OTC.equalsIgnoreCase(firstUwOrder.getProductDeliveryType()) && junoClient.isSBRTAtOrderLevel(firstUwOrder.getIncrementId())){
                    log.info("Increment Id  : {} is received as an SBRT order for OTC shipment : {} ",
                            firstUwOrder.getIncrementId() , firstUwOrder.getShippingPackageId() );
                    salesOrderHeader.setLegalEntity(Constants.Common.LegalEntityLenskart);
                }else{
                    salesOrderHeader.setLegalEntity(Constants.Common.LegalEntityDealskart);
                }
                /*HubMaster hubMaster = hubMasterService.getHubMaster(firstUwOrder.getFacilityCode());
                salesOrderHeader.setLegalEntity(hubMaster.getLegalEntity());*/
            } else if (firstUwOrder.getNavChannel().equalsIgnoreCase("COCOBulk") && !ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
                HubMaster hubMaster = hubMasterService.getHubMaster(firstUwOrder.getFacilityCode());
                salesOrderHeader.setLegalEntity("LK" + hubMaster.getCountry());
            } else {
                // Local fitting,DTC
                salesOrderHeader.setLegalEntity(Constants.Common.LegalEntityLenskart);
                //   salesOrderHeader.setLegalEntity(ordersHeader.getLegalEntity());
            }
            setCustomerDetails(salesOrderHeader, orderAddressUpdateShipping, ordersHeader, order);

            boolean isExportOrder = isExportOrder(orderAddressUpdateShipping, orderAddressUpdateBilling, ordersHeader);

            ArrayList<SoLine> soLines = getSoLines(uwOrders, order, ordersHeader, site, subChannel, isForceSyncForShippingChargeEnabled, newInstanceDto, isExportOrder);
            List<Integer> uwItemIdsToCheck =soLines.stream().map(soLine ->  soLine.getLineNumber().intValue()).collect(Collectors.toList());
            processGiftCardIfApplicableForUwItems(uwItemIdsToCheck,salesOrderHeader.getLegalEntity(),"SCM");
            ArrayList<MiscChargesHeader> miscChargesHeader = new ArrayList<MiscChargesHeader>();
            salesOrderHeader.setSoLines(soLines);
            ArrayList<MiscChargesLine> miscChargesLines = new ArrayList<MiscChargesLine>();
            salesOrderHeader.setMiscChargesLines(miscChargesLines);
            salesOrderHeader.setMiscChargesHeader(miscChargesHeader);
            //saleOrderUtil.saveValueInRedis(shipment, salesOrderHeader);
            salesOrderHeader.setIsIntegrated(1);
            validate(salesOrderHeader);
            log.info("Sale order payload is : {}",salesOrderHeader);
            //publishIntoMysqlTpoic
            publishEventForMySQLInsert(salesOrderHeader,FinanceSourceSystemSyncEvent.SALE_ORDER.name(),shipment,facilityCode,firstUwOrder.getIncrementId());
            return salesOrderHeader;
        } catch (Exception e) {
            log.error("[SalesOrderServiceImpl][generatePayload] error", e);
            throw e;
        }
    }

    private boolean isExportOrder(OrderAddressUpdate shippingAddress, OrderAddressUpdate billingAddress, OrdersHeader ordersHeader) {
        // Return true in case of international shipping to avoid tax config setup
        if (Objects.isNull(shippingAddress) || Objects.isNull(billingAddress) || Objects.isNull(ordersHeader)) {
            return false;
        }
        String shippingCountry = shippingAddress.getCountryId();
        String billingCountry = billingAddress.getCountryId();
        String orderCountry = ordersHeader.getLkCountry();

        if (Objects.isNull(shippingCountry) || Objects.isNull(billingCountry) || Objects.isNull(orderCountry)) {
            return false;
        }

        return !"IN".equalsIgnoreCase(shippingCountry) && ("IN".equalsIgnoreCase(billingCountry) || "IN".equalsIgnoreCase(orderCountry));
    }


    @Override
    public FinanceConsumerResponseDto checkIfGiftCardApplicableForUwItems(GiftCardEligibilityRequestDto giftCardEligibilityRequestDto){
        List<Integer> uwItemIds =giftCardEligibilityRequestDto.getUwItems();
        String source = giftCardEligibilityRequestDto.getSource();
        return processGiftCardIfApplicableForUwItems(uwItemIds,null, source);
    }

    public FinanceConsumerResponseDto checkIfGiftCardApplicableForUwItemsList(List<List<Integer>> uwItemIdsList) {
        GiftCardEligibilityRequestDto giftCardEligibilityRequestDto = GiftCardEligibilityRequestDto.builder().source("SCM").build();
        for(List<Integer> uwItemsIds : uwItemIdsList) {
            giftCardEligibilityRequestDto.setUwItems(uwItemsIds);
            checkIfGiftCardApplicableForUwItems(giftCardEligibilityRequestDto);
        }
        return buildFinanceConsumerResponse(HttpStatus.OK,
                                            "Successfully validated shipments based on uwItemIds.",
                                            null);

    }

    private Boolean isGiftCardEligibleForOrder(List<UwOrder> uwOrderList) throws InvalidRequestException {


        List<Integer> itemIds = uwOrderList.stream().map(UwOrder::getItemId).collect(Collectors.toList());

        List<ItemWisePriceDetails> itemWisePriceDetailsList = itemWisePricesReadRepository.
                findByItemIdIn(itemIds);

        if(!CollectionUtils.isEmpty(itemWisePriceDetailsList)){
            itemWisePriceDetailsList = itemWisePriceDetailsList.stream().
                    filter(item -> item.getGiftCardDiscount()>0).collect(Collectors.toList());
        }

       return  !CollectionUtils.isEmpty(itemWisePriceDetailsList) ? true : false;
    }
    /***
     *
     * @param uwItemIds
     * @param legalEntity
     * @param source here can be scm or return
     */
    @SneakyThrows
    public FinanceConsumerResponseDto processGiftCardIfApplicableForUwItems(List<Integer> uwItemIds, String legalEntity, String source){

            try{

                if(!isGiftCardEnabled){
                    log.info(" gift card flow is not enabled currently");
                    return buildFinanceConsumerResponse(HttpStatus.OK, "Gift card flow is blocked currently!", null);
                }
                List<UwOrder> uwOrderList  = uwOrdersReadRepository.findByUwItemIdIn(uwItemIds);

                if(CollectionUtils.isEmpty(uwOrderList)){
                    throw new InvalidRequestException("UwItem Ids not present in UwOrders table");
                }

                Boolean isGiftCardEligible = isGiftCardEligibleForOrder(uwOrderList);

                if ("RETURN".equalsIgnoreCase(source)) {
                    if (!isGiftCardEligible) {
                        return buildFinanceConsumerResponse(HttpStatus.OK, "Items not elgible for gift card Sync", GiftCardEligibilityCheckDto.builder().isEligible(false).build());
                    } else {
                        return buildFinanceConsumerResponse(HttpStatus.OK, "Items eligible for gift card Sync", GiftCardEligibilityCheckDto.builder().isEligible(true).build());
                    }
                }


                if (isGiftCardEligible) {
                    UwOrder uwOrder = uwOrderList.get(0);
                    if("B2B".equalsIgnoreCase(uwOrderList.get(0).getProductDeliveryType()) && !Constants.Common.FOFOB2B.equalsIgnoreCase(uwOrderList.get(0).getNavChannel())){
                        uwOrder = uwOrdersReadRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());   //warehouse order
                    }
                    GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                            shipmentId(uwOrder.getShippingPackageId()).
                            facilityCode(uwOrder.getFacilityCode()).
                            incrementId(uwOrder.getIncrementId()).
                            legalEntity(legalEntity).
                            source(source).
                            uwItemIds(uwItemIds).
                            build();

                    kafkaProducerTemplate.send(giftCardTopic,uwOrder.getShippingPackageId(),ObjectHelper.writeValue(giftCardSyncDto));
                }


            }catch (Exception e){
                log.error("Failed to send kafka message for gift_card for uwItems : {} ",objectMapper.writeValueAsString(uwItemIds),e);
                if ("RETURN".equalsIgnoreCase(source)) {
                return buildFinanceConsumerResponse(HttpStatus.INTERNAL_SERVER_ERROR,"Some server error happened at finance-consumer's end : "+ e.getMessage(),null);
                }
            }
        return null;
    }

    private FinanceConsumerResponseDto buildFinanceConsumerResponse(HttpStatus httpStatus,String message ,
                                                                    GiftCardEligibilityCheckDto giftCardEligibilityCheckDto){
       return FinanceConsumerResponseDto.builder().
                httpStatusCode(httpStatus.value()).
                message(message).
                data(giftCardEligibilityCheckDto).
                build();
    }

    public void setCustomerDetails(SalesOrderHeader salesOrderHeader, OrderAddressUpdate orderAddressUpdate, OrdersHeader ordersHeader, Order order) {
        String customerId = String.valueOf(order.getCustomerId().longValue());
        String storeCode = saleOrderUtil.getStoreCode(ordersHeader);
        if (ordersHeader.getIsBulkOrder() && !salesOrderHeader.getSubChannel().equalsIgnoreCase("COCOBulk")) {
            String saleSource = order.getSaleSource();
            if (saleSource.contains(Constants.Channel.AQUALENS)) {
                salesOrderHeader.setCustomerAccount(customerId);
                salesOrderHeader.setInvoiceAccount(customerId);
            } else {
                salesOrderHeader.setCustomerAccount(storeCode);
                salesOrderHeader.setInvoiceAccount(storeCode);
            }
        } else if (salesOrderHeader.getSubChannel().equalsIgnoreCase("COCOBulk") && ordersHeader.getLkCountry().equalsIgnoreCase("IN")) {
            salesOrderHeader.setCustomerAccount("DKIN");
            salesOrderHeader.setInvoiceAccount("DKIN");
        } else if (salesOrderHeader.getSubChannel().equalsIgnoreCase("COCOBulk") && ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
            salesOrderHeader.setCustomerAccount("LKSG");
            salesOrderHeader.setInvoiceAccount("LKSG");
        } else if (salesOrderHeader.getSubChannel().equalsIgnoreCase("COCOBulk") && ordersHeader.getLkCountry().equalsIgnoreCase("AE")) {
            salesOrderHeader.setCustomerAccount("LKAE");
            salesOrderHeader.setInvoiceAccount("LKAE");
        } else if (salesOrderHeader.getSubChannel().equalsIgnoreCase("COCOBulk") && ordersHeader.getLkCountry().equalsIgnoreCase("US")) {
            salesOrderHeader.setCustomerAccount("LKUS");
            salesOrderHeader.setInvoiceAccount("LKUS");
        }else if(Constants.Common.FOFOB2B.equalsIgnoreCase(salesOrderHeader.getSubChannel())){
            salesOrderHeader.setCustomerAccount(storeCode);
            salesOrderHeader.setInvoiceAccount(storeCode);
        }
        else {
            salesOrderHeader.setCustomerAccount(customerId);
            salesOrderHeader.setInvoiceAccount(customerId);
        }
        salesOrderHeader.setFirstName(StringUtils.defaultString(orderAddressUpdate.getFirstName()));
        salesOrderHeader.setLastName(StringUtils.defaultString(orderAddressUpdate.getLastName()));
        salesOrderHeader.setGender("Non-Specific");
        salesOrderHeader.setContactPersonId(StringUtils.defaultString(orderAddressUpdate.getEmail()));
        salesOrderHeader.setEmail(StringUtils.defaultString(StringUtils.defaultString(orderAddressUpdate.getEmail())));
        salesOrderHeader.setPhone(StringUtils.defaultString(orderAddressUpdate.getTelephone()));
    }

    public String getCustomerGroup(UwOrder uwOrder, String country) {
        String customerGroup = "", type;
        MpOrderDetails mpOrderDetails = mpOrderDetailsRepository.findOneByIncrementIdNew(uwOrder.getIncrementId());

        if (country.equalsIgnoreCase("IN")) {
            type = "Domestic";
        } else {
            type = "International";
        }

        if (country.equalsIgnoreCase("SG") && uwOrder.getShipToStoreRequired()) {
            customerGroup = "Intercompany";
        } else if (mpOrderDetails != null) {
            customerGroup = "B2M" + type;
        } else if (uwOrder.getProductDeliveryType().equalsIgnoreCase("DTC") || uwOrder.getProductDeliveryType().equalsIgnoreCase("OTC")) {
            customerGroup = "B2C" + type;
        } else if (uwOrder.getShipToStoreRequired()) {
            customerGroup = "B2B" + type;
        } else if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
            customerGroup = "DK trade";
        }
        return customerGroup;
    }

    public void setPaymentMethod(SalesOrderHeader salesOrderHeader, UwOrder firstUwOrder, Order order, OrdersHeader ordersHeader, MpOrderDetails mpOrderDetails) {
        //MethodOfPayment
        if (mpOrderDetails != null) {
            salesOrderHeader.setMethodOfPayment(order.getSaleSource());
        } else if (ordersHeader.getLkCountry().equalsIgnoreCase("SG")) {
            //LKSG
            if (firstUwOrder.getNavChannel().contains("COCO") && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                salesOrderHeader.setMethodOfPayment("offlinecash");
            } else {
                salesOrderHeader.setMethodOfPayment("LKSG");
            }
        } else if (ordersHeader.getLkCountry().equalsIgnoreCase("AE")) {
            //LKAE
            if (firstUwOrder.getNavChannel().contains("COCO") && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                salesOrderHeader.setMethodOfPayment("offlinecash");
            } else {
                salesOrderHeader.setMethodOfPayment("LKAE");
            }
        } else if (ordersHeader.getLkCountry().equalsIgnoreCase("US")) {
            salesOrderHeader.setMethodOfPayment(ordersHeader.getPaymentMode());
        } else if ((firstUwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B) && !firstUwOrder.getNavChannel().equalsIgnoreCase(Constants.Common.FOFOB2B)) || firstUwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.OTC)) {
            //DKIN
            if ((firstUwOrder.getNavChannel().equalsIgnoreCase("COCOOTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("JJOTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("COCOB2B") || firstUwOrder.getNavChannel().equalsIgnoreCase("JJB2B")) && (!salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                salesOrderHeader.setMethodOfPayment(salesOrderHeader.getModeOfPayment());
            } else if (firstUwOrder.getNavChannel().equalsIgnoreCase("OJOSDTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("JJDTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("WEBDTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("JJOnlineDTC")) {
                salesOrderHeader.setMethodOfPayment("DKIN");
            }
        } else {
            //LKIN
            if ((firstUwOrder.getNavChannel().equalsIgnoreCase("COCODTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("FOFODTC")) && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                salesOrderHeader.setMethodOfPayment("offlinecash");
            } else if (firstUwOrder.getNavChannel().equalsIgnoreCase("OJOSDTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("WEBDTC") || firstUwOrder.getNavChannel().equalsIgnoreCase("JJOnlineDTC")) {
                salesOrderHeader.setMethodOfPayment("DKIN");
            } else if (firstUwOrder.getNavChannel().equalsIgnoreCase("JJDTC") && (salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                salesOrderHeader.setMethodOfPayment("offlinecash");
            } else if (firstUwOrder.getNavChannel().equalsIgnoreCase("JJDTC") && (!salesOrderHeader.getModeOfPayment().equalsIgnoreCase("offlinecash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("cash") || !salesOrderHeader.getModeOfPayment().equalsIgnoreCase("storecash"))) {
                salesOrderHeader.setMethodOfPayment("DKIN");
            }
        }

        if (StringUtils.isBlank(salesOrderHeader.getMethodOfPayment())) {
            Order Paymentorder = orderService.findOrderByItemId(firstUwOrder.getItemId());
            if (Paymentorder.getMethod().equalsIgnoreCase("cashondelivery")) {
                salesOrderHeader.setMethodOfPayment("COD");
            } else {
                salesOrderHeader.setMethodOfPayment("Prepaid");
            }
        }
    }

    public ArrayList<SoLine> getSoLines(List<UwOrder> uwOrders, Order order, OrdersHeader ordersHeader,
                                        String site, String subChannel, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto, boolean isExportOrder) throws Exception {
        try {
            ArrayList<SoLine> soLines = new ArrayList<SoLine>();
            DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);
            List<Integer> SBRTUwItemIds =null;
            List<Integer> uwItemIds = uwOrders.stream().map(UwOrder::getUwItemId).collect(Collectors.toList());

            if(isSBRTFlowEnabledForSalesOrder){
                SBRTUwItemIds = sbrtOrderItemService.fetchUwItemIdsFromSBRTOrderItem(uwItemIds);
            }

            for (UwOrder uwOrder : uwOrders) {
                SoLine soLine = new SoLine();
                soLine.setPartnerType("");
                setSBRTFlagForLineItem(uwOrder, SBRTUwItemIds, soLine);
                Product product = productService.getProduct(uwOrder.getProductId());
                Integer uwItemId = uwOrder.getUwItemId();
                Integer gstuwItemIdVirtual, gstuwItemIdNonVirtual;
                ShippingStatus shippingStatus;
                UwItemWisePriceInfo uwItemWisePriceInfo;
                ItemWisePriceDetails itemWisePriceDetails = itemWisePricesReadRepository.findByItemId(uwOrder.getItemId());
                Double discount = 0.0D;
                if (itemWisePriceDetails != null) {
                    discount = itemWisePriceDetails.getGiftVoucherDiscount() + itemWisePriceDetails.getPrepaidDiscount() + itemWisePriceDetails.getImplicitDiscount() + itemWisePriceDetails.getLenskartDiscount() + itemWisePriceDetails.getLenskartPlusDiscount() + itemWisePriceDetails.getCouponDiscount() + itemWisePriceDetails.getFcDiscount() + itemWisePriceDetails.getExchangeDiscount();
                    soLine.setLineDiscountAmount(String.format("%.2f", discount));
                }
                if (uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B)) {
                    UwOrder uwOrderLK = uwOrder;
                    if(!uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) {
                        uwOrderLK = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
                    }
                    gstuwItemIdVirtual = uwOrder.getUwItemId();
                    gstuwItemIdNonVirtual = uwOrderLK.getUwItemId();
                    log.info("gstuwItemIdVirtual " + gstuwItemIdVirtual + " gstuwItemIdNonVirtual " + gstuwItemIdNonVirtual);
                    soLine.setDeliveryType(Constants.SalesOrder.DeliveryTypeB2B);
                    shippingStatus = shippingStatusService.getShippingStatus(uwOrderLK.getShippingPackageId());
                    OrderItemGSTDetail orderItemGSTDetailNonVirtual = orderItemGstDetailsService.getOrderItemGSTDetail(gstuwItemIdNonVirtual);
                    OrderItemGSTDetail orderItemGSTDetailVirtual = orderItemGstDetailsService.getOrderItemGSTDetail(gstuwItemIdVirtual);
                    soLine.setPurchPrice(String.format("%.2f", orderItemGSTDetailNonVirtual.getCostPerItem()));
                    List<String> insurancePids = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365, Constants.SYSTEM_PREFERENCE_KEYS.D365_insurance_pids);
                    if (insurancePids.contains(String.valueOf(product.getProductId()))) {
                        soLine.setSalesPrice(String.format("%.2f", orderItemGSTDetailNonVirtual.getTotalAmount()+discount));
                    } else {
                        soLine.setSalesPrice(String.format("%.2f", orderItemGSTDetailVirtual.getCostPerItem() + discount));
                    }
                    soLine.setConfirmedShipDate(dateFormat.format(saleOrderUtil.getDispatchDate(uwOrderLK)));
                    if (newInstanceDto.getIsNewInstance()) {
                        soLine.setBarcode(uwOrderLK.getBarcode());
                        if (serviceClassificationIds.contains(product.getClassification())) {
                            soLine.setLkPurchasePrice(0.0D);
                        } else {
                            List<CostPriceResponseBody> costPriceResponseBodyList = nexsClient.getCostPrice(Collections.singletonList(
                                    uwOrderLK.getBarcode()), uwOrderLK.getFacilityCode());
                            CostPriceResponseBody costPriceResponseBody = costPriceResponseBodyList.get(0);
                            if (Objects.nonNull(costPriceResponseBody)) {
                                soLine.setLkPurchasePrice(Double.valueOf(costPriceResponseBody.getPrice()));
                            }
                        }
                    }
                } else {
                    //DTC, OTC, Local fitting, FOFOB2B
                    gstuwItemIdVirtual = uwOrder.getUwItemId();
                    log.info("gstuwItemIdVirtual " + gstuwItemIdVirtual + " gstuwItemIdNonVirtual ");
                    shippingStatus = shippingStatusService.getShippingStatus(uwOrder.getShippingPackageId());
                    soLine.setDeliveryType(Constants.SalesOrder.DeliveryType);
                    if (StringUtils.isBlank(uwOrder.getBarcode())) {
                        log.error("[SalesOrderServiceImpl][getSoLines] barcode is mandatory for uw_item_id " + gstuwItemIdVirtual);
                    }
                    soLine.setPurchPrice("0.00");
                    OrderItemGSTDetail orderItemGSTDetailVirtual = orderItemGstDetailsService.getOrderItemGSTDetail(gstuwItemIdVirtual);
                    List<String> insurancePids = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365, Constants.SYSTEM_PREFERENCE_KEYS.D365_insurance_pids);
                    if (insurancePids.contains(String.valueOf(product.getProductId()))) {
                        soLine.setSalesPrice(String.format("%.2f", orderItemGSTDetailVirtual.getTotalAmount()+discount));
                    } else {
                        soLine.setSalesPrice(String.format("%.2f", orderItemGSTDetailVirtual.getCostPerItem() + discount));
                    }
                    soLine.setConfirmedShipDate(dateFormat.format(saleOrderUtil.getDispatchDate(uwOrder)));
                    if (newInstanceDto.getIsNewInstance()) {
                        soLine.setBarcode(uwOrder.getBarcode());
                        if (serviceClassificationIds.contains(product.getClassification())) {
                            soLine.setLkPurchasePrice(0.0D);
                        } else {
                            List<CostPriceResponseBody> costPriceResponseBodyList = nexsClient.getCostPrice(Collections.singletonList(
                                    uwOrder.getBarcode()), uwOrder.getFacilityCode());
                            CostPriceResponseBody costPriceResponseBody = costPriceResponseBodyList.get(0);
                            if (Objects.nonNull(costPriceResponseBody)) {
                                soLine.setLkPurchasePrice(Double.valueOf(costPriceResponseBody.getPrice()));
                            }
                        }
                    }
                }
                if(uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)){
                    soLine.setDeliveryType(Constants.SalesOrder.DeliveryType);
                }
                if (shippingStatus != null) {
                    soLine.setConfirmedReceiptDate(StringUtils.defaultString(shippingStatus.getShipping_time()));
                }
                OrderItemGSTDetail orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwItemId);
                //setSalesPrice(soLine,uwOrder,discount);
                soLine.setLineNumber(Long.valueOf(uwItemId));
                soLine.setItemNumber(String.valueOf(uwOrder.getProductId()));
                soLine.setQtyOrdered(Integer.parseInt(Constants.Common.ITEM_QTY));
                soLine.setSourcingVendor(Constants.Common.LegalEntityLenskart);
                soLine.setSalesOrderItemCode(String.valueOf(uwItemId));
                Double taxRate = 0.00D;
                taxRate = orderItemGSTDetail.getCgstPc() + orderItemGSTDetail.getIgstPc() + orderItemGSTDetail.getSgstPc() + orderItemGSTDetail.getUgstPc();
                soLine.setTaxRateType(String.format("%.2f", taxRate));
                soLine.setItemSalesTaxGrp(String.format("%.2f", taxRate));
                soLine.setSalesTaxGrp(String.format("%.2f", taxRate));
                soLine.setItemTemplateName("");
                soLine.setInventLocationId(site);
                soLine.setStores(saleOrderUtil.getStoreCode(ordersHeader));
                soLine.setInventSiteId("");
                soLine.setBrand(product.getBrand().trim());
                soLine.setSubChannel(subChannel);
                soLine.setSalesChannel(site);
                soLine.setMagentoItemId(order.getMagentoItemId());
                int classificationId = product.getClassification();
                String classificationDisplayName = classificationService.getClassification(classificationId).getDisplayName();
                classificationDisplayName = ProductUtil.getClassificationDisplayName(classificationId, classificationDisplayName);
                soLine.setItemClassification(classificationDisplayName);
                String hsncode = orderItemGSTDetail.getHsn();
                if(StringUtils.isBlank(hsncode) && blankHsnCodeClassifications.contains(String.valueOf(classificationId))){
                    hsncode = ProductUtil.getHSNCode(product.getHsnCode());
                }
                log.info("hsn code for uw item id {} {}",uwItemId,hsncode);
                if(!CollectionUtils.isEmpty(hsnEligibleProductsList) && hsnEligibleProductsList.contains(uwOrder.getProductId())) {
                    soLine.setHsnCode(hsncode);
                    soLine.setSacCode("");
                } else if (21566 == classificationId || (hsncode.length() > 2 && hsncode.substring(0, 2).equals("99"))) {
                    soLine.setSacCode(hsncode.length() > 6 ? hsncode.substring(0, 6) : hsncode);
                    soLine.setHsnCode("");
                } else {
                    soLine.setHsnCode(hsncode);
                    soLine.setSacCode("");
                }
                if (Double.parseDouble(soLine.getLineDiscountAmount()) > Double.parseDouble(soLine.getSalesPrice())){
                    log.info("line Discount Item greater than sales price for shipping package id: {}", uwOrder.getShippingPackageId());
                    soLine.setLineDiscountAmount(soLine.getSalesPrice());
                }
                soLine.setExempt(getExemptFlagValue(isExportOrder, soLine.getItemNumber()));
                soLines.add(soLine);
            }
            if (isShippingChargesPayloadRequired(isForceSyncForShippingChargeEnabled)) {
                double shippingCharges = getShippingChargesForShipment(uwOrders);
                if(shippingCharges > 0.00 && "IN".equals(ordersHeader.getLkCountry())) {
                	UwOrder freeGoldMembershipItem = getFreeGoldMembershipItem(uwOrders);
            		if (Objects.isNull(freeGoldMembershipItem)) {
                        SoLine soLineForShippingCharges = getSoLineForShippingCharges(uwOrders.get(0), shippingCharges, isExportOrder);
                        soLines.add(soLineForShippingCharges);
                    } else {
                        double taxRateMultiplier = 1.00D + (taxRateForShippingCharges / 100.00D);
                        double shippingChargeWithoutTax = shippingCharges / taxRateMultiplier;
                        SoLine soLineForFreeGoldMembership = soLines.stream().filter(p->Long.valueOf(freeGoldMembershipItem.getUwItemId()).equals(p.getLineNumber())).findFirst().get();
                        double salePriceForFreeGoldMembership = Double.parseDouble(soLineForFreeGoldMembership.getSalesPrice());
                        soLineForFreeGoldMembership.setSalesPrice(String.format("%.2f", salePriceForFreeGoldMembership+shippingChargeWithoutTax));
                    }
                }
            }
            return soLines;
        } catch (Exception e) {
            log.error("[SalesOrderServiceImpl][getSoLines] error occurred while fetching soLines for shipment :{}",uwOrders.get(0).getShippingPackageId(),e);
            throw e;
        }

    }

    private String getExemptFlagValue(boolean isExportOrder, String productId) {
        if (isExportOrder || (!CollectionUtils.isEmpty(exemptTaxProductIds) && exemptTaxProductIds.contains(productId))) {
            return "Yes";
        }
        return "No";
    }
    private void setSBRTFlagForLineItem(UwOrder uwOrder, List<Integer> sbrtUwItemIds, SoLine soLine) {
        log.info("isSBRTFlowEnabledForSalesOrder : {}",isSBRTFlowEnabledForSalesOrder);
        if(!isSBRTFlowEnabledForSalesOrder){
            return;
        }

        if(!CollectionUtils.isEmpty(sbrtUwItemIds) && sbrtUwItemIds.contains(uwOrder.getUwItemId())){
            soLine.setUnits(SBRTItemFlag); // SBRT code
        }else{
            soLine.setUnits(nonSBRTItemFlag); // non-SBRT code
        }
    }



    private void setSalesPrice(SoLine soLine, UwOrder uwOrder, Double discount) {
        Double salesPrice = fetchCostPriceForUWOrder(uwOrder) + discount;
        soLine.setSalesPrice(String.format("%.2f", salesPrice));
    }

    boolean isShippingChargesPayloadRequired(boolean forceSyncForShippingCharges){
        return (forceSyncForShippingCharges || isShippingChargesPayloadEnabled);
    }

    @SneakyThrows
    private SoLine getSoLineForShippingCharges(UwOrder uwOrder, double shippingCharges, boolean isExportOrder) {
        SoLine soLine = new SoLine();
        Product product = productService.getProduct(uwOrder.getProductId());
        DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);
        Integer uwItemId = uwOrder.getUwItemId();
        int incrementId = uwOrder.getIncrementId();
        List<Order> orders = orderService.getOrders(incrementId);
        Order order = orders.get(0);
        OrdersHeader ordersHeader = orderHeaderService.getOrderHeader(incrementId);
        String site = saleOrderUtil.getInventLocationId(uwOrder, ordersHeader);
        String subChannel = saleOrderUtil.getSubChannel(uwOrder, order);
        ShippingStatus shippingStatus;
        double taxRateMultiplier = 1.00D + (taxRateForShippingCharges / 100.00D);
        double shippingChargeWithoutTax = shippingCharges / taxRateMultiplier;

        soLine.setPartnerType("");
        soLine.setLineDiscountAmount(String.format("%.2f", 0.0D));
        if (uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B)) {
            UwOrder uwOrderLK = uwOrder;
            if (!uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) {
                uwOrderLK = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
                soLine.setSourcingVendor(Constants.Common.LegalEntityLenskart);
                soLine.setPurchPrice("1.00");
            } else { // FOFOB2B
                soLine.setPurchPrice("0.00");
                soLine.setSourcingVendor("");
            }
            soLine.setDeliveryType(Constants.SalesOrder.DeliveryTypeB2B);
            shippingStatus = shippingStatusService.getShippingStatus(uwOrderLK.getShippingPackageId());
            soLine.setConfirmedShipDate(dateFormat.format(saleOrderUtil.getDispatchDate(uwOrderLK)));
        } else {
            //DTC, OTC, Local fitting, FOFOB2B
            shippingStatus = shippingStatusService.getShippingStatus(uwOrder.getShippingPackageId());
            soLine.setDeliveryType(Constants.SalesOrder.DeliveryType);
            soLine.setConfirmedShipDate(dateFormat.format(saleOrderUtil.getDispatchDate(uwOrder)));
            soLine.setSourcingVendor("");       // to be sent for B2b orders
            soLine.setPurchPrice("0.00");
        }
        soLine.setSalesPrice(String.format("%.2f", shippingChargeWithoutTax));

        if (uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) {
            soLine.setDeliveryType(Constants.SalesOrder.DeliveryType);
        }
        if (shippingStatus != null) {
            soLine.setConfirmedReceiptDate(StringUtils.defaultString(shippingStatus.getShipping_time()));
        }
        soLine.setLineNumber(Long.valueOf(1));
        soLine.setItemNumber("SHIPPING");
        soLine.setQtyOrdered(Integer.parseInt(Constants.Common.ITEM_QTY));
        soLine.setSalesOrderItemCode("");
        soLine.setTaxRateType(String.format("%.2f", taxRateForShippingCharges));
        soLine.setItemSalesTaxGrp(String.format("%.2f", taxRateForShippingCharges));
        soLine.setSalesTaxGrp(String.format("%.2f", taxRateForShippingCharges));
        soLine.setItemTemplateName("");
        soLine.setInventLocationId(site);
        soLine.setStores(saleOrderUtil.getStoreCode(ordersHeader));
        soLine.setInventSiteId("");
        soLine.setBrand("Free");
        soLine.setSubChannel(subChannel);
        soLine.setSalesChannel(site);
        soLine.setMagentoItemId(null);
        soLine.setItemClassification("Prescription Lens");
        soLine.setHsnCode("");
        soLine.setSacCode("997212");
        soLine.setExempt(getExemptFlagValue(isExportOrder, soLine.getItemNumber()));
        return soLine;
    }
    
	private double getShippingChargesForShipment(List<UwOrder> uwOrders) {
		Double shippingCharges = 0.00D;
		List<Integer> itemIds = uwOrders.stream().map(UwOrder::getItemId).collect(Collectors.toList());
		List<ItemWisePriceDetails> itemWisePriceDetailsList = itemWisePricesRepository.findByItemIdIn(itemIds);
        if(!CollectionUtils.isEmpty(itemWisePriceDetailsList)){
            for (ItemWisePriceDetails itemWisePriceDetails : itemWisePriceDetailsList) {
                shippingCharges += itemWisePriceDetails.getShippingCharges();
            }
        }
		return shippingCharges;
	}

    @SneakyThrows
    double fetchCostPriceForUWOrder(UwOrder uwOrder) {
        ItemWisePriceDetails itemWisePriceDetails = itemWisePricesRepository.findByItemId(uwOrder.getItemId());
        Integer gstuwItemIdVirtual;
        double costPrice = itemWisePriceDetails.getItemTotalAfterDiscount() - itemWisePriceDetails.getShippingCharges();
        UwOrder uwOrderLK = uwOrder;
        if (!((Constants.Common.FOFOB2B).equals(uwOrder.getNavChannel()))) {
            uwOrderLK = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
        }
        gstuwItemIdVirtual = uwOrder.getUwItemId();
        OrderItemGSTDetail orderItemGSTDetailVirtual = orderItemGstDetailsService.getOrderItemGSTDetail(gstuwItemIdVirtual);
        double totaltax = orderItemGSTDetailVirtual.getCgstPc() + orderItemGSTDetailVirtual.getIgstPc() + orderItemGSTDetailVirtual.getSgstPc() + orderItemGSTDetailVirtual.getUgstPc();
        double taxMultiplier = 1.00 + (totaltax / 100.0);
        if ((Constants.Common.B2B).equalsIgnoreCase(uwOrder.getProductDeliveryType())) {
            Product p = productService.getProduct(uwOrder.getProductId());
            List<String> insurancePids = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365, Constants.SYSTEM_PREFERENCE_KEYS.D365_insurance_pids);
            if (insurancePids.contains(String.valueOf(p.getProductId()))) {
                itemWisePriceDetails = itemWisePricesRepository.findByItemId(uwOrderLK.getItemId());  // non virtual uwOrder used for this case
                costPrice = itemWisePriceDetails.getItemTotalAfterDiscount() - itemWisePriceDetails.getShippingCharges();
                return costPrice;            // for insurance pids to send total amount with tax
            } else {
                return costPrice / taxMultiplier; // for normal pids to send total amount without tax
            }
        } else {
            //DTC, OTC, Local fitting, FOFOB2B
            if (StringUtils.isBlank(uwOrder.getBarcode())) {
                log.error("[SalesOrderServiceImpl][getSoLines] barcode is mandatory for uw_item_id " + gstuwItemIdVirtual);
            }
            Product p = productService.getProduct(uwOrder.getProductId());
            List<String> insurancePids = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365, Constants.SYSTEM_PREFERENCE_KEYS.D365_insurance_pids);
            if (insurancePids.contains(String.valueOf(p.getProductId()))) {
                return costPrice;
            } else {
                return costPrice / taxMultiplier; // for normal pids to send total amount without tax
            }
        }
    }

    private UwOrder getFreeGoldMembershipItem(List<UwOrder> uwOrders) {
        for (UwOrder uwOrder : uwOrders){
            if(isFreeGoldMembershipItem(uwOrder)){
                return uwOrder;
            }
        }
        return null;
	}

	private boolean isFreeGoldMembershipItem(UwOrder uwOrder) {
		if (!CollectionUtils.isEmpty(goldMembershipProductIds) &&  goldMembershipProductIds.contains(uwOrder.getProductId()) && isZeroValueItem(uwOrder)) {
			return true;
		}
		return false;
	}

    @SneakyThrows
	private boolean isZeroValueItem(UwOrder uwOrder) {
		double salePrice = fetchCostPriceForUWOrder(uwOrder);
        if(0.00D==salePrice){
            return true;
        }
        return false;
    }

    void setInvoiceDetails(PackingSlip packingSlip, UwOrder uwOrder){
        if(invoiceDetailsFetchEnabled){
            try{
                InvoiceDetailsResponse invoiceDetails = inventoryAdaptorConnector.fetchInvoiceDetails
                        (uwOrder.getUnicomOrderCode(), "", uwOrder.getFacilityCode());
                InvoiceDetailsResponse.Invoice invoice = invoiceDetails.invoices.get(0);
                String invoiceCreationTimeInstant = invoice.created;
                Instant invoiceCreationIntant = Instant.ofEpochMilli(Long.parseLong(invoiceCreationTimeInstant));
                LocalDateTime invoiceCreationDate = LocalDateTime.ofInstant(invoiceCreationIntant, ZoneOffset.UTC);
                log.info("packing Slip invoice date : {} , invoice code : {} fetched from inventory Adaptor for unicomOrderCode : {}"
                        ,invoiceCreationDate.format(isoDateFormatter), invoice.code, uwOrder.getUnicomOrderCode());
                packingSlip.setPackingSlipDate(invoiceCreationDate.format(isoDateFormatter));
                packingSlip.setSalesInvoiceNo(invoice.code);
            }catch (Exception e){
                log.error("SalesOrderServiceImpl : Exception occured while fetching invoice details from inventory-adaptor", e);
            }
        }
        /***   last fallback for invoice details to fetch from shipmentInvoiceDetailsRepository  **/
        if(Objects.isNull(packingSlip.getPackingSlipDate())||Objects.isNull(packingSlip.getSalesInvoiceNo())) {
            ShipmentInvoiceDetails shipmentInvoiceDetails = shipmentInvoiceDetailsRepository
                    .findByShippingPackageIdAndFacilityCode(uwOrder.getShippingPackageId(), uwOrder.getFacilityCode());
            if(Objects.nonNull(shipmentInvoiceDetails)){
                if (Objects.isNull(packingSlip.getPackingSlipDate())
                        && Objects.nonNull(shipmentInvoiceDetails.getInvoiceDate())) {
                    packingSlip.setPackingSlipDate(shipmentInvoiceDetails.getInvoiceDate().format(isoDateFormatter));
                }
                if (Objects.isNull(packingSlip.getSalesInvoiceNo())) {
                    packingSlip.setSalesInvoiceNo(shipmentInvoiceDetails.getInvoiceNo());
                }
            }
        }
    }
    @Override
    public PackingSlip generatePackingSlipPayload(List<Integer> uwItemIds, boolean isForceSyncForShippingChargeEnabled, NewInstanceDto newInstanceDto) throws Exception {
        PackingSlip packingSlip = new PackingSlip();
        String packingSlipBaseUrl;
        if(packingSlipLive.equalsIgnoreCase("true")){
            packingSlipBaseUrl = D365ProdUrl;
        }
        else{
            packingSlipBaseUrl = D365BaseUrl;
        }
        log.info("[SalesOrderServiceImpl] [generatePackingSlipPayload] : uwItemIds  :{}", uwItemIds);
        String shipment = "";
            List<UwOrder> uwOrders = uwOrdersService.getUwOrders(uwItemIds);
            UwOrder virtualOrder = uwOrders.get(0);
            UwOrder realOrder=null;
            String facilityCode = null;
            if (virtualOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                realOrder = uwOrdersService.getUwOrderByUwItemId(virtualOrder.getB2bRefrenceItemId()); //
                if (hubMasterReadRepository.findByFacilityCode(realOrder.getFacilityCode()) != null) {
                    shipment = realOrder.getShippingPackageId();
                    facilityCode = realOrder.getFacilityCode();
                } else {
                    shipment = virtualOrder.getShippingPackageId();
                    facilityCode = virtualOrder.getFacilityCode();
                }
            } else {
                shipment = virtualOrder.getShippingPackageId();
                facilityCode = virtualOrder.getFacilityCode();
            }
            if (StringUtils.isBlank(shipment)) {
                throw new Exception(NOT_FOUND_SHIPPING_PACKAGE_ID + virtualOrder.getUwItemId());
            }
        log.info("[SalesOrderServiceImpl] [generatePackingSlipPayload]: value of shipment is : {} and product delivery type is :{}", shipment,
                 virtualOrder.getProductDeliveryType());
        Integer incrementId = virtualOrder.getIncrementId();
            OrdersHeader ordersHeader = orderHeaderService.getOrderHeader(incrementId);
            String site = saleOrderUtil.getInventLocationId(virtualOrder, ordersHeader);

            String suffixValue = "";
            if(!newInstanceDto.getIsNewInstance()) {
                FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(FinanceSourceSystemSyncEvent.PACKING_SLIP,
                                                                                                                                                 FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                                                                                                                                                 shipment,
                                                                                                                                                 facilityCode);
                if (Objects.nonNull(financeSourceSystemSync) && Objects.nonNull(financeSourceSystemSync.getSuffix())) {
                    suffixValue = financeSourceSystemSync.getSuffix();
                }
            } else {
                SaleOrder saleOrder = saleOrderWriteRepository.findById(newInstanceDto.getId()).orElseThrow(() -> new RecordNotFoundException(
                        "[SaleOrderServiceImpl][generatePackingSlipPayload]Sale order record not found"));
                if (saleOrder.getSuffix() != null) {
                    suffixValue = saleOrder.getSuffix();
                }
            }
            packingSlip.setSalesId(shipment + "_" + site + suffixValue);

            ArrayList<SalesLineList> salesLineLists = new ArrayList<SalesLineList>();
            packingSlip.setLegalEntity(packingSlipUtil.getLegalEntity(ordersHeader,virtualOrder));

            DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);

            com.lenskart.wm.model.ShipmentDetails shipmentDetailsMongo = courierInfoService.getCourierInfoFromMongo(shipment, facilityCode);
            Optional<ShippingInvoiceDetails> shippingInvoiceDetailsMongo = shippingInvoiceDetailsRepository.findById(shipment);
            if (shipmentDetailsMongo == null) {
                throw new Exception("Entry not present in shipmentDetailsMongo table " + shipment);
            }
            log.info("shipmentDetailsMongo " + shipmentDetailsMongo.getInvoiceCode());
            packingSlip.setSalesInvoiceNo(shipmentDetailsMongo.getInvoiceCode());
            packingSlip.setPackingSlipId(virtualOrder.getUnicomOrderCode());

            for (UwOrder uwOrder : uwOrders) {
                SalesLineList salesLineList = new SalesLineList();
                salesLineList.setSalesId(shipment + "_" + site + suffixValue);
                salesLineList.setQuantity(Constants.Common.ITEM_QTY);
                salesLineList.setBatchId("");
                salesLineList.setItemNumber(String.valueOf(uwOrder.getProductId()));
                //if (uwOrder.getProductDeliveryType().equals(Constants.Common.B2B)  && !uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) {
                if (uwOrder.getProductDeliveryType().equalsIgnoreCase(Constants.Common.B2B)) {
                    UwOrder uwOrderLK = uwOrder;
                    if(!uwOrder.getNavChannel().equals(Constants.Common.FOFOB2B)) {
                        uwOrderLK = uwOrdersService.getUwOrderByUwItemId(uwOrder.getB2bRefrenceItemId());
                    }
                    if(shippingInvoiceDetailsMongo.isPresent()){
                        packingSlip.setPackingSlipDate(dateFormat.format(shippingInvoiceDetailsMongo.get().getInvoiceTimestamp()));
                    }
                    ShipmentDetails shipmentDetails;
                    String shipmentPackageId = uwOrderLK.getShippingPackageId();
                    MpOrderDetails mpOrderDetails = null;
                    if (incrementId != 0)
                        mpOrderDetails = mpOrderDetailsReadRepository.findOneByIncrementIdNew(incrementId);
                    if (mpOrderDetails != null && StringUtils.isNotBlank(mpOrderDetails.getTrackingNo()))
                        packingSlip.setTrackingNo(mpOrderDetails.getTrackingNo());
                    else {
                        if (!StringUtils.isBlank(shipmentPackageId) && shipmentPackageId != null && !uwOrders.get(0).getIsLocalFittingRequired() && !uwOrders.get(0).getProductDeliveryType().equals(Constants.Common.OTC)) {
                            shipmentDetails = courierInfoService.getCourierInfo(shipmentPackageId);
                            if (shipmentDetails == null) {
                                if(StringUtils.isBlank(shipmentDetailsMongo.getTrackingNumber())){
                                    ShippingStatus shippingStatus = shippingStatusService.getShippingStatus(shipment);
                                    if(shippingStatus!= null && !StringUtils.isBlank(shippingStatus.getTrackingNo())){
                                        packingSlip.setTrackingNo(shippingStatus.getTrackingNo());
                                    }
                                    else {
                                        packingSlip.setTrackingNo("NA");
                                    }
                                }
                                else {
                                    packingSlip.setTrackingNo(shipmentDetailsMongo.getTrackingNumber());
                                }
                            }
                        } else {
                            shipmentDetails = null;
                        }
                        if (shipmentDetails != null) {
                            packingSlip.setTrackingNo(shipmentDetails.getAwbNumber());
                        } else {
                            ShippingStatus shippingStatus = shippingStatusService.getShippingStatus(shipment);
                            if(shippingStatus!=null && !StringUtils.isBlank(shippingStatus.getTrackingNo())){
                                packingSlip.setTrackingNo(shippingStatus.getTrackingNo());
                            }
                            else {
                                packingSlip.setTrackingNo("NA");
                            }
                        }
                    }
                    salesLineList.setSerialId(uwOrderLK.getBarcode());
                    String dummyBarcode=getDummyBarCode(uwOrderLK,packingSlip.getLegalEntity());
                    log.info("[SalesOrderServiceImpl] [generatePackingSlipPayload][getDummyBarCode] value of shipmentId : {} facilityCode : {} actualBarCode {} dummyBarCode {} legalEntity {}", shipment,facilityCode,uwOrderLK.getBarcode(),dummyBarcode,packingSlip.getLegalEntity());
                    if(Objects.nonNull(dummyBarcode)){
                        salesLineList.setSerialId(dummyBarcode);
                    }
                } else {
                    //DTC, OTC, Local fitting
                    packingSlip.setPackingSlipDate(dateFormat.format(saleOrderUtil.getDispatchDate(virtualOrder)));
                    ShipmentDetails shipmentDetails;
                    String shipmentPackageId = uwOrder.getShippingPackageId();
                    MpOrderDetails mpOrderDetails = null;
                    if (incrementId != 0)
                        mpOrderDetails = mpOrderDetailsReadRepository.findOneByIncrementIdNew(incrementId);
                    if (mpOrderDetails != null && StringUtils.isNotBlank(mpOrderDetails.getTrackingNo()))
                        packingSlip.setTrackingNo(mpOrderDetails.getTrackingNo());
                    else {
                        if (!StringUtils.isBlank(shipmentPackageId) && shipmentPackageId != null && !uwOrders.get(0).getIsLocalFittingRequired() && !uwOrders.get(0).getProductDeliveryType().equals(Constants.Common.OTC)) {
                            shipmentDetails = courierInfoService.getCourierInfo(shipmentPackageId);
                            if (shipmentDetails == null) {
                                if(StringUtils.isBlank(shipmentDetailsMongo.getTrackingNumber())){
                                    ShippingStatus shippingStatus = shippingStatusService.getShippingStatus(shipment);
                                    if(shippingStatus!=null && !StringUtils.isBlank(shippingStatus.getTrackingNo())){
                                        packingSlip.setTrackingNo(shippingStatus.getTrackingNo());
                                    }
                                    else {
                                        packingSlip.setTrackingNo("NA");
                                    }
                                }
                                else {
                                    packingSlip.setTrackingNo(shipmentDetailsMongo.getTrackingNumber());
                                }
                            }
                        } else {
                            shipmentDetails = null;
                        }
                        if (shipmentDetails != null) {
                            packingSlip.setTrackingNo(shipmentDetails.getAwbNumber());
                        } else {
                            ShippingStatus shippingStatus = shippingStatusService.getShippingStatus(shipment);
                            if(shippingStatus!=null && !StringUtils.isBlank(shippingStatus.getTrackingNo())){
                                packingSlip.setTrackingNo(shippingStatus.getTrackingNo());
                            }
                            else {
                                packingSlip.setTrackingNo("NA");
                            }
                        }
                    }
                    salesLineList.setSerialId(uwOrder.getBarcode());
                    String dummyBarcode=getDummyBarCode(uwOrder,packingSlip.getLegalEntity());
                    log.info("[SalesOrderServiceImpl] [generatePackingSlipPayload][getDummyBarCode][DTC.]value of shipmentId : {} facilityCode : {} actualBarCode {} dummyBarCode {} legalEntity {}", shipment,facilityCode,uwOrder.getBarcode(),dummyBarcode,packingSlip.getLegalEntity());
                    if(Objects.nonNull(dummyBarcode)){
                        salesLineList.setSerialId(dummyBarcode);
                    }
                }
                salesLineList.setLineNumber(Long.valueOf(uwOrder.getUwItemId()));
                salesLineLists.add(salesLineList);
            }
            if (isShippingChargesPayloadRequired(isForceSyncForShippingChargeEnabled)) {
            	double shippingCharges = getShippingChargesForShipment(uwOrders);
            	if (shippingCharges > 0.00 && ordersHeader.getLkCountry().equals("IN")) {
            		UwOrder freeGoldMembershipItem = getFreeGoldMembershipItem(uwOrders);
            		if(Objects.isNull(freeGoldMembershipItem)) {
                        String saleId= null;
                        saleId = shipment + "_" + site + suffixValue;
		                SalesLineList salesLineListForShippingCharges = getSalesLineListForShippingCharges(saleId);
		                salesLineLists.add(salesLineListForShippingCharges);
            		}
            	}
            }
            packingSlip.setSalesLineList(salesLineLists);
            if(Objects.isNull(packingSlip.getPackingSlipDate())||Objects.isNull(packingSlip.getSalesInvoiceNo())){
                if("B2B".equalsIgnoreCase(virtualOrder.getProductDeliveryType()) &&
                        !Constants.Common.FOFOB2B.equals(virtualOrder.getNavChannel())){
                    setInvoiceDetails(packingSlip,realOrder);
                }else{
                    setInvoiceDetails(packingSlip,virtualOrder);
                }
            }
            log.info("Packing slip payload" + packingSlip);
            //publish event to the sql topic
            publishEventForMySQLInsert(packingSlip,FinanceSourceSystemSyncEvent.PACKING_SLIP.name(),shipment,facilityCode,uwOrders.get(0).getIncrementId());

        return packingSlip;
    }
    @Override
    public SalesOrderHeader generateSaleOrderPayloadOnly(String shippingId, String facility, boolean newInstance)  {
        List<Integer> uwItemIds;
        NewInstanceDto newInstanceDto = NewInstanceDto.builder().isNewInstance(newInstance).build();
        try {
            uwItemIds = getUwItemIds(shippingId, facility, newInstanceDto,FinanceSourceSystemSyncEvent.SALE_ORDER );
            SalesOrderHeader salesOrderHeader;
            if (!uwItemIds.isEmpty()) {

                salesOrderHeader = generatePayload(uwItemIds,
                                                   null,
                                                   false,newInstanceDto
                                                  );
                return salesOrderHeader;
            } else {
                throw new Exception("Cannot find uwOrders for this shipping id " + shippingId + " and facility code " + facility);
            }
        }
        catch (Exception e){
            log.error("SaleOrderServiceImpl: Error occurred while fetching sale-order payload for shipping id : {} and facility code :{} ",shippingId,facility,e);
            throw new RuntimeException(e);
        }

    }
    private List<Integer> getUwItemIds(String shippingId, String facility, NewInstanceDto newInstanceDto, FinanceSourceSystemSyncEvent financeSourceSystemSyncEvent) throws Exception {
        List<Integer> uwItemIds;
        if (newInstanceDto.getIsNewInstance()) {
            SaleOrder saleOrder = saleOrderWriteRepository.findByEntityIdAndFacilityCode(shippingId, facility);
            if(Objects.isNull(saleOrder)){
                throw new Exception("Cannot find sale-order for this shipping id " + shippingId + " and facility code " + facility);
            }
            uwItemIds = Stream.of(saleOrder.getUwItemIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            newInstanceDto.setId(saleOrder.getId());
        } else {
            FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(
                    financeSourceSystemSyncEvent,
                    FinanceSourceSystemSyncEntityType.SHIPMENT_ID,
                    shippingId,
                    facility);
            if(Objects.isNull(financeSourceSystemSync)){
                throw new Exception("Cannot find sale-order for this shipping id" + shippingId + " and facility code " + facility);
            }
            String payload = financeSourceSystemSync.getPayload();
            uwItemIds = objectMapper.readValue(payload, List.class);
        }
        return uwItemIds;
    }

    SalesLineList getSalesLineListForShippingCharges(String salesId){
        SalesLineList salesLineList = new SalesLineList();
        salesLineList.setSalesId(salesId);
        salesLineList.setQuantity(Constants.Common.ITEM_QTY);
        salesLineList.setBatchId("");
        salesLineList.setItemNumber("SHIPPING");
        salesLineList.setLineNumber(Long.valueOf(1));
        salesLineList.setSerialId("");
        return  salesLineList;
    }

    public void checkMultipleShipments(List<UwOrder> uwOrders, String firstShipment) throws Exception {
        UwOrder firstUwOrder = uwOrders.get(0);
        for (UwOrder uwOrder : uwOrders) {
            String shipment;
            if (firstUwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && !Constants.Common.FOFOB2B.equalsIgnoreCase(firstUwOrder.getNavChannel())) {
                shipment = uwOrdersService.getUwOrderByUwItemId(firstUwOrder.getB2bRefrenceItemId()).getShippingPackageId();
            } else {
                shipment = uwOrder.getShippingPackageId();
            }
            if (!StringUtils.isBlank(shipment)) {
                if (!firstShipment.equalsIgnoreCase(shipment)) {
                    throw new Exception("This list of uwOrders contain multiple shipment ids " + uwOrders);
                }
            }
        }
    }


    @Override
    public void pushShippingPackageIdsInSaleOrderKafka(List<String> shippingPackageIds) {
        try {
            for (String shippingPackageId : shippingPackageIds) {
                List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(shippingPackageId);
                apiLogService.pushToD365SaleOrder(unicomOrderIds, d365SaleOrderTopic);
            }
        }
        catch (Exception e){
            log.error("SalesOrderServiceImpl : pushShippingPackageIdsInSaleOrderKafka exception : ",e);
        }
    }

    @Override
    public void pushShippingPackageIdsInSaleOrderKafka(String entityId, String facilityCode) {
        log.info("[SalesOrderServiceImpl] [pushShippingPackageIdsInSaleOrderKafka] : entityId :{} and facilityCode :{}",entityId,facilityCode);
        try {
            String unicomOrderId = uwOrdersService.getUnicomOrderCodeFromShippingPackageIdAndFacilityCode(entityId,
                                                                                                          facilityCode);
            apiLogService.pushToD365SaleOrder(Arrays.asList(unicomOrderId), d365SaleOrderTopic);
        } catch (Exception e) {
            log.info("pushShippingPackageIdsInSaleOrderKafka e" + e);
        }
    }

    @Override
    public void pushShippingPackageIdsInPackingSlipKafka(String entityId, String facilityCode) {
        log.info("[SalesOrderServiceImpl] [pushShippingPackageIdsInPackingSlipKafka] : entityId :{} and facilityCode :{}",entityId,facilityCode);
        try {
            String unicomOrderId = uwOrdersService.getUnicomOrderCodeFromShippingPackageIdAndFacilityCode(entityId,
                                                                                                          facilityCode);
            apiLogService.pushToD365SaleOrder(Arrays.asList(unicomOrderId), PackingSlipTopic);
        } catch (Exception e) {
            log.info("pushShippingPackageIdsInPackingSlipKafka exception" + e);
        }
    }

    //todo need to deprecate this function after removing dependencies in further release pending on returns team for dev as discussed on [14June,2024].
    @Override
    public void pushShippingPackageIdsInSaleOrderKafkaProd(List<String> shippingPackageIds) {
        try {
            for (String shippingPackageId : shippingPackageIds) {
                List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(shippingPackageId);  // shipping id is not unique in uw_order table . thgis needs to be correct if this method has to be used
                apiLogService.pushToD365SaleOrder(unicomOrderIds, d365SaleOrderProdTopic);
            }
        }
        catch (Exception e){
            log.info("pushShippingPackageIdsInSaleOrderKafka e"+e);
        }
    }

    //todo need to deprecate this function after removing dependencies in further release pending on returns team for dev as discussed on [14June,2024].
    @Override
    public void pushShippingPackageIdsInPackingSlipKafka(List<String> shippingPackageIds) {
        try {
            for (String shippingPackageId : shippingPackageIds) {
                List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(shippingPackageId);
                apiLogService.pushToD365SaleOrder(unicomOrderIds, PackingSlipTopic);
            }
        }
        catch (Exception e){
            log.info("pushShippingPackageIdsInPackingSlipKafka exception"+e);
        }
    }

    @Override
    public void pushUwItemIdsInSaleOrderKafka(List<Integer> uwItemIds){
        List<UwOrder> uwOrders = uwOrdersRepository.findByUwItemIdIn(uwItemIds);
        Map<String, List<Integer>> ordersMap = apiLogService.getUwItemIdsForKafka(uwOrders);
        for (Map.Entry<String, List<Integer>> entry : ordersMap.entrySet()) {
            log.info("pushToD365SaleOrder order value pushed to kafka: " + new Gson().toJson(entry.getValue()));
            kafkaProducerTemplate.send( d365SaleOrderProdTopic,new Gson().toJson(entry.getValue()));
        }
    }


    @Override
    public void retrySaleOrder(Date startDate, Date endDate, Integer retryCount) throws Exception {
        try {
            log.info("retrySaleOrder starting time : {}", new Date());
            int page = 0;
            boolean retry = false;
            List<D365ApiLog> d365ApiLogList;
            List<FinanceConsumerLogs> financeConsumerLogsList;
            Pageable pageable = PageRequest.of(page, 10);
            Map<String, Integer> duplicateCheck = new HashMap<String, Integer>();
            boolean duplicate = false;
            do {
                d365ApiLogList = d365LogRepository.findRetryOrders(D365BaseUrl + Constants.SalesOrder.URL, Constants.SalesOrder.OrderType, Constants.Common.FAILURE, startDate, endDate, pageable);
                for (D365ApiLog d365ApiLog : d365ApiLogList) {
                    String facilityCode = getFacilityCodeFromSaleOrderPayload(ObjectHelper.convertToString(d365ApiLog.getRequest()));
                    if (duplicateCheck.containsKey(d365ApiLog.getEntityId())) {
                        duplicate = true;
                    } else {
                        duplicateCheck.put(d365ApiLog.getEntityId(), 1);
                        duplicate = false;
                    }
                    if (!duplicate) {
                        if (retryCount != 0) {
                            if (d365ApiLog.getRetry_count() <= retryCount) {
                                retry = true;
                            }
                        } else {
                            retry = true;
                        }
                        if (retry) {
                            String salesOrderId = d365ApiLog.getEntityId();
                            log.info("retrySaleOrder entity Id: " + salesOrderId);
                            if (Objects.nonNull(facilityCode)) {
                                pushShippingPackageIdsInSaleOrderKafka(salesOrderId, facilityCode);
                            }
                        }
                    }
                }
                page++;
            } while (d365ApiLogList.size() != 0);

            do {
                financeConsumerLogsList = financeConsumerLogsRepository.findRetryOrders(D365BaseUrl + Constants.SalesOrder.URL, Constants.SalesOrder.OrderType, Constants.Common.FAILURE, startDate, endDate, pageable);
                for (FinanceConsumerLogs financeConsumerLog : financeConsumerLogsList) {
                    String facilityCode = getFacilityCodeFromSaleOrderPayload(ObjectHelper.convertToString(financeConsumerLog.getRequest()));
                    if (duplicateCheck.containsKey(financeConsumerLog.getEntityId())) {
                        duplicate = true;
                    } else {
                        duplicateCheck.put(financeConsumerLog.getEntityId(), 1);
                        duplicate = false;
                    }
                    if (!duplicate) {
                        if (retryCount != 0) {
                            if (financeConsumerLog.getRetry_count() <= retryCount) {
                                retry = true;
                            }
                        } else {
                            retry = true;
                        }

                        if (retry) {
                            String salesOrderId= financeConsumerLog.getEntityId();
                            log.info("retrySaleOrder entity Id: " + salesOrderId);
                            if (Objects.nonNull(facilityCode)) {
                                pushShippingPackageIdsInSaleOrderKafka(salesOrderId, facilityCode);
                            }
                        }
                    }
                }
                page++;
            }
            while (financeConsumerLogsList.size() != 0);
            log.info("retrySaleOrder ending time : {}", new Date());

        } catch (Exception e) {
            log.error("retrySaleOrder error " + genericClientService.getErrorMessage(e));
        }
    }

    private String getFacilityCodeFromSaleOrderPayload(String jsonStr) throws JsonProcessingException {
        String facilityCode = null;
        JsonNode jsonObj = new ObjectMapper().readTree(jsonStr);
        if (Objects.nonNull(jsonObj) && Objects.nonNull(jsonObj.get("_salesOrderHeader")) && Objects.nonNull(jsonObj.get(
                "_salesOrderHeader").get("Fulfillment_WH"))) {
            facilityCode = jsonObj.get("_salesOrderHeader").get("Fulfillment_WH").textValue();
        }
        return facilityCode;
    }

    private String getFacilityCodeFromPackingSlipPayload(String jsonStr) throws JsonProcessingException {
        String salesId = null;
        JsonNode jsonObj = new ObjectMapper().readTree(jsonStr);
        if (Objects.nonNull(jsonObj) && Objects.nonNull(jsonObj.get("_contract").get("SalesLineList"))) {
            JsonNode salesLineListArray = jsonObj.get("_contract").get("SalesLineList");
            if (salesLineListArray.isArray()) {
                for (final JsonNode objNode : salesLineListArray) {
                    if (Objects.nonNull(objNode.get("SalesId"))) {
                        salesId = objNode.get("SalesId").textValue().split("_")[1];
                    }
                }
            }
        }
        return salesId;
    }

    @Override
    public void retryPackingSlip(Date startDate, Date endDate, Integer retryCount) throws Exception {
        try {
            log.info("retryPackingSlip starting time : {}", new Date());
            int page = 0;
            boolean retry = false;
            List<D365ApiLog> d365ApiLogList;
            List<FinanceConsumerLogs> financeConsumerLogsList;
            Pageable pageable = PageRequest.of(page, 10);
            Map<String, Integer> duplicateCheck = new HashMap<String, Integer>();
            boolean duplicate = false;
            do {
                d365ApiLogList = d365LogRepository.findRetryOrders(D365BaseUrl + Constants.PackingSlip.URL, Constants.PackingSlip.OrderType, Constants.Common.FAILURE, startDate, endDate, pageable);
                for (D365ApiLog d365ApiLog : d365ApiLogList) {
                    String facilityCode = getFacilityCodeFromPackingSlipPayload(ObjectHelper.convertToString(d365ApiLog.getRequest()));
                    if (duplicateCheck.containsKey(d365ApiLog.getEntityId())) {
                        duplicate = true;
                    } else {
                        duplicateCheck.put(d365ApiLog.getEntityId(), 1);
                        duplicate = false;
                    }
                    if(!duplicate) {
                        if (retryCount != 0) {
                            if (d365ApiLog.getRetry_count() <= retryCount) {
                                retry = true;
                            }
                        } else {
                            retry = true;
                        }
                        String packingSlipId;
                        if (retry) {
                              packingSlipId = d365ApiLog.getEntityId();
                            log.info("retryPackingSlip entity Id: " + packingSlipId);
                            if(Objects.nonNull(facilityCode)){
                                pushShippingPackageIdsInPackingSlipKafka(packingSlipId,facilityCode);
                            }
                        }
                    }
                }
                page++;
            } while (d365ApiLogList.size() != 0);

            do {
                financeConsumerLogsList = financeConsumerLogsRepository.findRetryOrders(D365BaseUrl + Constants.PackingSlip.URL, Constants.PackingSlip.OrderType, Constants.Common.FAILURE, startDate, endDate, pageable);
                for (FinanceConsumerLogs financeConsumerLog : financeConsumerLogsList) {
                    String facilityCode = getFacilityCodeFromPackingSlipPayload(ObjectHelper.convertToString(financeConsumerLog.getRequest()));
                    if (duplicateCheck.containsKey(financeConsumerLog.getEntityId())) {
                        duplicate = true;
                    } else {
                        duplicateCheck.put(financeConsumerLog.getEntityId(), 1);
                        duplicate = false;
                    }
                    if(!duplicate) {
                        if (retryCount != 0) {
                            if (financeConsumerLog.getRetry_count() <= retryCount) {
                                retry = true;
                            }
                        } else {
                            retry = true;
                        }
                        String packingSlipId;
                        if (retry) {
                                packingSlipId = financeConsumerLog.getEntityId();
                            log.info("retryPackingSlip entity Id: " + packingSlipId);
                            if (Objects.nonNull(facilityCode)) {
                                pushShippingPackageIdsInPackingSlipKafka(packingSlipId, facilityCode);
                            }
                        }
                    }
                }
                page++;
            } while (financeConsumerLogsList.size() != 0);
            log.info("retryPackingSlip ending time : {}", new Date());
        } catch (Exception e) {
            log.error("retryPackingSlip error " + genericClientService.getErrorMessage(e));
        }
    }

    @Override
    public void addSuffixToShippingIdOldInstance(List<ShippingIdFacilityCode> shippingIdFacilityCodes, SuffixHandlerRequestType suffixHandlerRequestType, String suffixValue) {
        if (isInvalidSuffix(suffixValue)) {
            log.info("[SaleOrderServiceImpl][addSuffixToShippingIdOldInstance] Skipping adding suffix value {} since it is invalid.", suffixValue);
            return;
        }
        try {
            for (ShippingIdFacilityCode shippingIdFacilityCode : shippingIdFacilityCodes) {
                if (StringUtils.isEmpty(shippingIdFacilityCode.getShipping_id()) || StringUtils.isEmpty(shippingIdFacilityCode.getFacility_code())) {
                    log.info("[SaleOrderServiceImpl][addSuffixToShippingIdOldInstance] Skipping since either shipping ID {} or facility code {} is blank.", shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code());
                    continue;
                }
                switch (suffixHandlerRequestType) {
                    case SALE_ORDER:
                        financeSystemSyncRepository.updateSuffixSaleOrder(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        break;
                    case PACKING_SLIP:
                        financeSystemSyncRepository.updateSuffixPackingSlip(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        break;
                    case BOTH:
                        financeSystemSyncRepository.updateSuffixSaleOrder(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        financeSystemSyncRepository.updateSuffixPackingSlip(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        break;
                    case NONE:
                        break;
                    default:
                        return;
                }
            }
        } catch (Exception ex) {
            log.error("[SaleOrderServiceImpl][addSuffixToShippingIdOldInstance] Error while add suffix." + ex);
        }
    }

    @Override
    public void addSuffixToShippingIdOldInstance(List<Long> ids, String suffixValue) {
        if (isInvalidSuffix(suffixValue)) {
            log.info("[SaleOrderServiceImpl][addSuffixToShippingIdOldInstance] Skipping adding suffix value {} since it is invalid.", suffixValue);
            return;
        }
        try {
            int batchSize = 1000;
            int totalSize = ids.size();
            int batches = (int) Math.ceil((double) totalSize / batchSize);
            log.info("[SaleOrderServiceImpl][addSuffixToShippingIdOldInstance] Updating suffix value {} for total {} shipping ids.", suffixValue, totalSize);
            for (int batch = 0; batch < batches; batch++) {
                int startIdx = batch * batchSize;
                int endIdx = Math.min(totalSize, startIdx + batchSize);
                List<Long> batchIds = ids.subList(startIdx, endIdx);
                processShippingIdsSuffix(batchIds, suffixValue);
            }
        } catch (Exception ex) {
            log.error("[SaleOrderServiceImpl][addSuffixToShippingIdOldInstance] Error while add suffix." + ex);
        }
    }

    @Override
    public PackingSlip getPackingSlipPayloadOnly(SaleOrderPackingSlipPayloadRequestDto saleOrderPackingSlipPayloadRequestDto) {
        List<Integer> uwItemIds;
        NewInstanceDto newInstanceDto = NewInstanceDto.builder().isNewInstance(saleOrderPackingSlipPayloadRequestDto.isNewInstance()).build();
        try {
            uwItemIds = getUwItemIds(saleOrderPackingSlipPayloadRequestDto.getShippingId(),
                                     saleOrderPackingSlipPayloadRequestDto.getFacilityCode(),
                                     newInstanceDto,
                                     FinanceSourceSystemSyncEvent.PACKING_SLIP);
            PackingSlip packingSlip;
            if (!uwItemIds.isEmpty()) {

                packingSlip = generatePackingSlipPayload(uwItemIds,
                                                         false,
                                                         newInstanceDto);
                return packingSlip;
            } else {
                throw new Exception("Cannot find uwOrders for this shipping id " + saleOrderPackingSlipPayloadRequestDto.getShippingId() + " and facility code " + saleOrderPackingSlipPayloadRequestDto.getFacilityCode());
            }
        } catch (Exception e) {
            log.error(
                    "SaleOrderServiceImpl: Error occurred while fetching sale-order payload for shipping id : {} and facility code :{} ",
                    saleOrderPackingSlipPayloadRequestDto.getShippingId(),
                    saleOrderPackingSlipPayloadRequestDto.getFacilityCode(),
                    e);
            throw new RuntimeException(e);
        }
    }
    @Override
    public void processIdsForPushingShipments(List<Long> ids) {
        List<FinanceSourceSystemSync> financeSourceSystemSyncList = financeSystemSyncRepository.findAllById(ids);
        if (!CollectionUtils.isEmpty(financeSourceSystemSyncList)) {
            financeSourceSystemSyncList.forEach(financeSourceSystemSync -> {
                if (ObjectUtils.allNotNull(financeSourceSystemSync.getEntityId(),
                                           financeSourceSystemSync.getFacilityCode())) {
                    if (FinanceSourceSystemSyncEvent.SALE_ORDER.equals(financeSourceSystemSync.getEvent())) {
                        pushShippingPackageIdsInSaleOrderKafka(financeSourceSystemSync.getEntityId(),
                                                               financeSourceSystemSync.getFacilityCode());
                    }
                    if (FinanceSourceSystemSyncEvent.PACKING_SLIP.equals(financeSourceSystemSync.getEvent())) {
                        pushShippingPackageIdsInPackingSlipKafka(financeSourceSystemSync.getEntityId(),
                                                                 financeSourceSystemSync.getFacilityCode());
                    }
                }
            });
        }
    }

    public void processShippingIdsSuffix(List<Long> Ids, String suffixValue) {
        financeSystemSyncRepository.updateSuffixByIdIn(Ids, suffixValue);
    }

    public boolean isInvalidSuffix(String suffixValue) {
        return !SUFFIX_PATTERN.matcher(suffixValue).matches();
    }

    @Override
    public void addSuffixToShippingIdNewInstance(List<ShippingIdFacilityCode> shippingIdFacilityCodes, SuffixHandlerRequestType suffixHandlerRequestType, String suffixValue) {
        if (isInvalidSuffix(suffixValue)) {
            log.info("[SaleOrderServiceImpl][addSuffixToShippingIdNewInstance] Skipping adding suffix value {} since it is invalid.", suffixValue);
            return;
        }
        try {
            for (ShippingIdFacilityCode shippingIdFacilityCode : shippingIdFacilityCodes) {
                if (StringUtils.isEmpty(shippingIdFacilityCode.getShipping_id()) || StringUtils.isEmpty(shippingIdFacilityCode.getFacility_code())) {
                    log.info("[SaleOrderServiceImpl][addSuffixToShippingIdNewInstance] Skipping since either shipping ID {} or facility code {} is blank.", shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code());
                    continue;
                }
                switch (suffixHandlerRequestType) {
                    case SALE_ORDER:
                        saleOrderWriteRepository.updateSuffixSaleOrder(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        break;
                    case PACKING_SLIP:
                        saleOrderWriteRepository.updateSuffixPackingSlip(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        break;
                    case BOTH:
                        saleOrderWriteRepository.updateSuffixSaleOrderPackingSlip(shippingIdFacilityCode.getShipping_id(), shippingIdFacilityCode.getFacility_code(), suffixValue);
                        break;
                    case NONE:
                        break;
                    default:
                        return;
                }
            }
        } catch (Exception ex) {
            log.error("[SaleOrderServiceImpl][addSuffixToShippingIdNewInstance] Error while add suffix." + ex);
        }
    }

    @Override
    public void addSuffixToShippingIdNewInstance(List<Long> ids, String suffixValue, SuffixHandlerRequestType suffixHandlerRequestType) {
        if (isInvalidSuffix(suffixValue)) {
            log.info("[SaleOrderServiceImpl][addSuffixToShippingIdNewInstance] Skipping adding suffix value {} since it is invalid.", suffixValue);
            return;
        }
        try {
            int batchSize = 1000;
            int totalSize = ids.size();
            int batches = (int) Math.ceil((double) totalSize / batchSize);
            log.info("[SaleOrderServiceImpl][addSuffixToShippingIdNewInstance] Updating suffix value {} for total {} shipping ids.", suffixValue, totalSize);
            for (int batch = 0; batch < batches; batch++) {
                int startIdx = batch * batchSize;
                int endIdx = Math.min(totalSize, startIdx + batchSize);
                List<Long> batchIds = ids.subList(startIdx, endIdx);
                processShippingIdsSuffixNewInstance(batchIds, suffixValue, suffixHandlerRequestType);
            }
        } catch (Exception ex) {
            log.error("[SaleOrderServiceImpl][addSuffixToShippingIdNewInstance] Error while add suffix." + ex);
        }
    }

    public void processShippingIdsSuffixNewInstance(List<Long> ids, String suffixValue, SuffixHandlerRequestType suffixHandlerRequestType){
        switch(suffixHandlerRequestType){
            case SALE_ORDER:
                saleOrderWriteRepository.updateSuffixByIdInSaleOrder(ids, suffixValue);
                break;
            case PACKING_SLIP:
                saleOrderWriteRepository.updateSuffixByIdInPackingSlip(ids, suffixValue);
                break;
            case BOTH:
                saleOrderWriteRepository.updateSuffixByIdInSaleOrderPackingSlip(ids, suffixValue);
                break;
            case NONE:
                break;
            default:
                return;
        }
    }
    @Override
    public void updatedFinanceSourceEntityByIds(Set<Long> ids) {
        log.info("[SalesOrderServiceImpl][updatedFinanceSourceEntityByIds] ids {}",ids);
        try {
            if (!CollectionUtils.isEmpty(ids)) {
                List<FinanceSourceSystemSync> financeSourceSystemSyncList= financeSystemSyncRepository.findAllByIdIn(ids);
                for(FinanceSourceSystemSync financeSourceSystemSync:financeSourceSystemSyncList){

                    List<Integer> uwItemIds= saleOrderUtil.getUwItemIds(financeSourceSystemSync.getEntityId(),financeSourceSystemSync.getFacilityCode());
                    if(!CollectionUtils.isEmpty(uwItemIds)){
                        saleOrderUtil.updateToFinanceSystemSync(uwItemIds);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[SalesOrderServiceImpl][updatedFinanceSourceEntity] Error occurred while updating the financeSourceSystemEntity ids : {}",ids,e);
        }
        log.info("[SalesOrderServiceImpl][updatedFinanceSourceEntityByIds] updated successfully.");

    }

    public String getDummyBarCode(UwOrder uwOrderLK,String legalEntity){
        try {
            if(Objects.nonNull(uwOrderLK.getBarcode())) {
                List<InventoryCorrection> inventoryCorrectionList = inventoryCorrectionWriteRepository.findByBarcode(uwOrderLK.getBarcode());

                if (!CollectionUtils.isEmpty(inventoryCorrectionList)) {

                    InventoryCorrection correctedBarcode = inventoryCorrectionList.stream()
                            .filter(inventoryCorrection ->
                                            MovementType.POS.equals(inventoryCorrection.getMovementType()) &&
                                            uwOrderLK.getBarcode().equalsIgnoreCase(inventoryCorrection.getBarcode()) &&
                                            String.valueOf(uwOrderLK.getProductId()).equalsIgnoreCase(inventoryCorrection.getProductId()) &&
                                            "SUCCESS".equalsIgnoreCase(inventoryCorrection.getD365SyncStatus()) &&
                                                    legalEntity.equalsIgnoreCase(inventoryCorrection.getLegalEntity())

                            )
                            .findAny()
                            .orElse(null);

                    if (Objects.nonNull(correctedBarcode))
                        return correctedBarcode.getD365Barcode();
                }
            }
        } catch (Exception e) {
            log.error("[SalesOrderServiceImpl][getDummyBarCode] Error occurred while fetching the dummyBarcode  original {}",uwOrderLK.getBarcode(),e);
        }
        return null;
    }

    public void publishEventForMySQLInsert(Object payload, String eventType,String shipmentId,String facilityCode,Integer incrementId){
        log.info("[SalesOrderServiceImpl][publishEventDetailsToInsertInSQl] Request to publish the event payload {} incrementId {} eventType {} shipmentId {} facilityCode {} ",payload,incrementId,eventType,shipmentId,facilityCode);
        try {
            String documentPayload=ObjectHelper.convertToString(payload);
            KafkaEventDto kafkaEventDto = KafkaEventDto.builder()
                    .eventType(eventType)
                    .incrementId(incrementId)
                    .shipmentId(shipmentId)
                    .facilityCode(facilityCode)
                    .documentPayload(documentPayload)
                    .build();

            String docToPersistenceKafkaDtoObj= ObjectHelper.convertToString(kafkaEventDto);
            kafkaProducerTemplate.send(d365DocToSqlKafkaTopic.trim(),shipmentId+facilityCode+eventType,docToPersistenceKafkaDtoObj);

        }catch (Exception exception){
            log.error("[SalesOrderServiceImpl][publishEventDetailsToInsertInSQl] Error occurred while publishing  the event details payload {} eventType {} incrementId {}",payload,eventType,incrementId,exception);
        }
    }
}