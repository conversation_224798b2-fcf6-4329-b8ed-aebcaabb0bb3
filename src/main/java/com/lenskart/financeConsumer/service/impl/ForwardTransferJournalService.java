package com.lenskart.financeConsumer.service.impl;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.connector.impl.InventoryAdaptorConnectorImpl;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dto.KafkaEventDto;
import com.lenskart.financeConsumer.dto.d365requests.D365RequestDto;
import com.lenskart.financeConsumer.dto.d365requests.forward.TransferJournalDto.TransferJourLines;
import com.lenskart.financeConsumer.dto.d365requests.forward.TransferJournalDto.TransferJournalHeader;
import com.lenskart.financeConsumer.dto.d365requests.forward.TransferJournalDto.TransferJournalRequestDto;
import com.lenskart.financeConsumer.exceptions.FinanceAdaptorResponseFailure;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.TransferJournalHeaderRepository;
import com.lenskart.financeConsumer.model.financeDb.TransferJournalHeaderEntity;
import com.lenskart.financeConsumer.model.financeDb.TransferJournalLinesEntity;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.HubMasterService;
import com.lenskart.financeConsumer.service.OrderHeaderService;
import com.lenskart.financeConsumer.service.ShippingStatusService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.ForwardTransferUtil;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/***
 * <AUTHOR>
 * This service is made for processing transferJournals for forward flow starting with orders for HECBulk , CocoBulk + SBRTOrderItemService flag , this may expand for more navchannel combinations
 */
@Slf4j
@Service
public class ForwardTransferJournalService {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    private TransferJournalHeaderRepository transferJournalHeaderRepository;

    @Autowired
    private OrderHeaderService orderHeaderService;
    @Autowired
    private UwOrdersReadRepositoryFinance uwOrdersReadRepositoryFinance;
    @Autowired
    private ShippingStatusService shippingStatusService;
    @Autowired
    private GenericClientService genericClientService;
    @Autowired
    private HubMasterService hubMasterService;
    @Autowired
    private ForwardTransferUtil forwardTransferUtil;

    @Value("${financeAdapter.url}")
    private String financeAdapterUrl;

    @Value("${transferJournal.sync.block}")
    private boolean isTransferJournalSyncBlocked;

    @Autowired
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @Value("${d365.salesorder.kafka.topic}")
    private String saleOrderConsumerTopic;

    @Autowired
    private InventoryAdaptorConnectorImpl inventoryAdaptorConnector;

    @Autowired
    DateUtils dateUtils ;

    static Pattern documentNoAlreadyExistPattern = Pattern.compile(".*document .* already exist.*",
            Pattern.CASE_INSENSITIVE);

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Value("${d365.doc-to-sql.kafka.topic}")
    private String documentToSQLPersistenceTopic;



    public boolean isValidTransferJournal(FinanceSourceSystemSync financeSourceSystemSync) {
        return FinanceSourceSystemSyncEvent.TRANSFER_JOURNAL.equals(financeSourceSystemSync.getEvent());
    }

    @SneakyThrows
    public ResponseEntity createTransferJournalForShippingIdAndFacilityCode(String shipmentId , String facilityCode){

        if(isTransferJournalSyncBlocked){
            log.info("transfer journal syncing blocked hence not proceeding to sync");
            return new ResponseEntity("Transfer Journals are currently blocked for syncing", HttpStatus.BAD_REQUEST);
        }

        List<FinanceSourceSystemSync> financeSourceSystemSyncList = financeSystemSyncRepository.
                findByEntityIdAndFacilityCode(shipmentId,facilityCode);

        if(CollectionUtils.isEmpty(financeSourceSystemSyncList) || !isValidTransferJournal(financeSourceSystemSyncList.get(0))){
            throw new InvalidRequestException("Either record with given Shipment Id : " + shipmentId + " and facitilityCode : " + facilityCode +"is not valid for transferJournal creation or transferJournal Syncing is blocked");
        }
        FinanceSourceSystemSync financeSourceSystemSync =financeSourceSystemSyncList.get(0);
        return createTransferJournalForShippingIdAndFacilityCode(shipmentId , facilityCode, financeSourceSystemSync);
    }


    @SneakyThrows
    public ResponseEntity createTransferJournalForShippingIdAndFacilityCode(String shipmentId , String facilityCode, FinanceSourceSystemSync financeSourceSystemSync){

        if(isTransferJournalSyncBlocked){
            log.info("transfer journal syncing blocked hence not proceeding to sync");
            return new ResponseEntity("Transfer Journals are currently blocked for syncing", HttpStatus.BAD_REQUEST);
        }
        String responseMessage = null;
        String d365SyncStatus = "Failure";
        try {
            List<UwOrder> uwOrdersList = uwOrdersReadRepositoryFinance.findByShippingPackageIdAndFacilityCode(shipmentId, facilityCode);
            if (CollectionUtils.isEmpty(uwOrdersList)) {
                throw new RecordNotFoundException("UwOrder not found with given Shipment Id : " + shipmentId + " and facitilityCode : " + facilityCode, HttpStatus.BAD_REQUEST);
            }

            D365RequestDto transferJournalRequestPayload = generateTransferJournalRequestPayload(uwOrdersList,financeSourceSystemSync);

            ResponseEntity responseEntity =  postTransferJournalToD365(transferJournalRequestPayload);
            if(HttpStatus.OK.equals(responseEntity.getStatusCode()) && Objects.nonNull(responseEntity.getBody())){

                HashMap responseBody = (HashMap) responseEntity.getBody();

                responseMessage = (String) responseBody.get("Message");
                boolean responseStatus = Boolean.parseBoolean(String.valueOf(responseBody.get("Success")));
                d365SyncStatus = responseStatus ? "Success" : "Failure";

                if(!responseStatus){
                    d365SyncStatus = documentNoAlreadyExistPattern.matcher(responseMessage).find() ? "Success" : "Failure";
                }

            }else{
                throw new FinanceAdaptorResponseFailure("Http Status  : " +   responseEntity.getStatusCode() + " received from Finance Adaptor!");
            }

            return responseEntity;

        } catch (Exception e) {
            responseMessage = genericClientService.getErrorMessage(e);
            log.error("Exception occured while posting request for forward transfer journal for shipmentId : {} , facilityCode : {}  " , shipmentId , facilityCode , e);
            return new ResponseEntity("Some error occured while syncing TransferJournal to D365", HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            financeSourceSystemSync.setSyncedToFinanceSystem(true);
            Integer retryCount =financeSourceSystemSync.getRetryCount();
            if(Objects.isNull(retryCount)){
                retryCount=0;
            }
            financeSourceSystemSync.setRetryCount(retryCount+1);
            financeSourceSystemSync.setErrorMessage(responseMessage);
            financeSourceSystemSync.setUpdatedAt(new Date());
            financeSourceSystemSync.setD365SyncStatus(d365SyncStatus);
            financeSystemSyncRepository.save(financeSourceSystemSync);
        }

    }

    @SneakyThrows
    private D365RequestDto generateTransferJournalRequestPayload(List<UwOrder>  uwOrdersList, FinanceSourceSystemSync financeSourceSystemSync){
        UwOrder uwOrder = uwOrdersList.get(0);
        OrdersHeader ordersHeader = orderHeaderService.getOrderHeader(uwOrder.getIncrementId());

        ShippingStatus shippingStatus = shippingStatusService.getShippingStatusByUnicomCode(String.valueOf(uwOrder.getUnicomOrderCode()));

        String fromWarehouse = uwOrder.getFacilityCode();
        String toWarehouse = ordersHeader.getFacilityCode();
        String receiptDate = dateUtils.fetchStringFromDate(shippingStatus.getComplete_time());

        String documentNo = financeSourceSystemSync.getEntityId() + "_" + fromWarehouse +  "_" + ordersHeader.getFacilityCode();
        TransferJournalHeader transferJournalHeader = new TransferJournalHeader();
        transferJournalHeader.setDescription("SBRT TRANSFER_JOURNAL");
        transferJournalHeader.setJournalName("INVTRANS");
        transferJournalHeader.setLegalEntity(forwardTransferUtil.getLegalEntity(uwOrder.getFacilityCode(),ordersHeader.getLkCountry()));
        transferJournalHeader.setDocumentNumber(documentNo );

        ArrayList<TransferJourLines> transferJournalLines = fetchTransferJournalLines(uwOrdersList, receiptDate, toWarehouse);

        transferJournalHeader.setTransferJourLines(transferJournalLines);
        TransferJournalRequestDto transferJournalRequest = TransferJournalRequestDto.builder().transferJournalHeader(transferJournalHeader).build();


        TransferJournalHeaderEntity transferJournalHeaderEntity = transferJournalHeaderRepository.findByDocumentNo(documentNo);
        if(Objects.isNull(transferJournalHeaderEntity)){
            KafkaEventDto kafkaEventDto = KafkaEventDto.builder().
                    documentPayload(objectMapper.writeValueAsString(transferJournalHeader)).
                    incrementId(uwOrder.getIncrementId()).
                    shipmentId(uwOrder.getShippingPackageId()).
                    eventType("TRANSFER_JOURNAL").
                    build();

            kafkaProducerTemplate.send(documentToSQLPersistenceTopic,financeSourceSystemSync.getEntityId(),objectMapper.writeValueAsString(
                    kafkaEventDto));
        }

        return D365RequestDto.builder().id(financeSourceSystemSync.getId()).requestPayload(transferJournalRequest).build();
    }

    private ResponseEntity postTransferJournalToD365(D365RequestDto d365RequestDto){
        return genericClientService.forwardRequest(financeAdapterUrl + Constants.TransferJournal.FORWARD_TRANSFER_JOURNAL_URL, new HttpHeaders(), HttpMethod.POST, d365RequestDto);
    }

    private ArrayList<TransferJourLines> fetchTransferJournalLines(List<UwOrder> uwOrderList, String receiptDate, String toWarehouse){
        ArrayList<TransferJourLines> transferJournalLines  = new ArrayList<>();

        for(UwOrder uwOrder : uwOrderList){
            TransferJourLines line = new TransferJourLines();
            line.setItemNumber(String.valueOf(uwOrder.getProductId()));
            line.setLineNum((uwOrder.getUwItemId()));
            line.setJournalTransDate(receiptDate);
            line.setFromWarehouse(uwOrder.getFacilityCode());
            line.setToWarehouse(toWarehouse);
            line.setQty(1);
            line.setSerialNumber(uwOrder.getBarcode());
            transferJournalLines.add(line);
        }
        return transferJournalLines;
    }

    @SneakyThrows
    public void persistTransferJournalHeader(String transferOrderPayload,Integer incrementId, String shipmentId){

        TransferJournalHeader transferJournalHeader = objectMapper.readValue(transferOrderPayload,TransferJournalHeader.class);
        buildAndPersistTransferJournalEntityFromDto(transferJournalHeader,incrementId,shipmentId);


    }

    public void buildAndPersistTransferJournalEntityFromDto(TransferJournalHeader transferJournalHeader, Integer incrementId, String shipmentId){
        TransferJournalHeaderEntity transferJournalHeaderEntity = TransferJournalHeaderEntity.builder().
                legalEntity(transferJournalHeader.getLegalEntity()).
                journalName(transferJournalHeader.getJournalName()).
                description(transferJournalHeader.getDescription()).
                documentNo(transferJournalHeader.getDocumentNumber()).
                incrementId(incrementId).
                shipmentId(shipmentId).
                build();

        List<TransferJournalLinesEntity> transferJournalLinesEntityList =new ArrayList<>();
        transferJournalHeaderEntity.setTransferJournalLinesEntityList(transferJournalLinesEntityList);

        for(TransferJourLines transferJourLine : transferJournalHeader.getTransferJourLines()){
            TransferJournalLinesEntity transferJournalLinesEntity =TransferJournalLinesEntity.builder().
                    journalTransDate(DateUtils.getDateFromString(transferJourLine.getJournalTransDate())).
                    brands(transferJourLine.getBrand()).
                    costCentre(transferJourLine.getCostCentre()).
                    employee(transferJourLine.getEmployee()).
                    fromSite(transferJourLine.getFromSite()).
                    toSite(transferJourLine.getToSite()).
                    fromWarehouse(transferJourLine.getFromWarehouse()).
                    itemClassification(transferJourLine.getItemClassification()).
                    lineNumber(transferJourLine.getLineNum()).
                    itemNumber(transferJourLine.getItemNumber()).
                    partnerType(transferJourLine.getPartnerType()).
                    qty(transferJourLine.getQty()).
                    salesChannel(transferJourLine.getSalesChannel()).
                    subChannel(transferJourLine.getSubChannel()).
                    site(transferJourLine.getSite()).
                    serialNumber(transferJourLine.getSerialNumber()).
                    store(transferJourLine.getStore()).
                    toWarehouse(transferJourLine.getToWarehouse()).
                    units(transferJourLine.getUnits()).
                    createdBy("finance-consumer").
                    updatedBy("finance-consumer").
                    build();
            transferJournalHeaderEntity.addTransferJournalLine(transferJournalLinesEntity);
        }
        transferJournalHeaderEntity.setCreatedBy("finance-consumer");
        transferJournalHeaderEntity.setUpdatedBy("finance-consumer");

        transferJournalHeaderRepository.save(transferJournalHeaderEntity);
    }


}

