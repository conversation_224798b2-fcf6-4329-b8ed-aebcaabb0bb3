package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.financeDb.writeRepository.TransferJournalRepository;
import com.lenskart.financeConsumer.dto.d365requests.TransferJournalDTO.D365TransferJournalDTO;
import com.lenskart.financeConsumer.dto.d365requests.TransferJournalDTO.TransferJournalResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.TransferJournalSchedulerDTO.TransferJournalLineDto;
import com.lenskart.financeConsumer.dto.d365requests.TransferJournalSchedulerDTO.TransferJournalHeaderDto;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.model.financeDb.TransferJournal;
import com.lenskart.financeConsumer.model.enums.MovementJournalFailureType;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import com.lenskart.financeConsumer.service.TransferJournal.TransferJournalService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.DateUtils;
import com.lenskart.financeConsumer.util.ObjectHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;


@Slf4j
@Service("TransferJournalServiceV1")
public class TransferJournalServiceV1Impl implements TransferJournalService {

    @Autowired
    private TransferJournalRepository transferJournalRepository;
    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;
    @Autowired
    SchedulerConfigService schedulerConfigService;
    @Value("${financeAdapter.url}")
    String financeAdapterUrl;
    @Value("${transfer_journal.max_limit:5000}")
    private Long MAX_LIMIT;
    @Value("${transfer_journal.pageSize:1000}")
    private int pageSize;
    @Value("${transfer_journal.retryLimit:5}")
    private Integer transferJournalFailureRetryLimit;

    @Value("${d365.transfer_journal.kafka.topic}")
    private String transferJournalKafkaTopic;

    static Pattern socketTimeoutExceptionPattern = Pattern.compile(".*SocketTimeoutException.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern d365InternalServerErrorPattern = Pattern.compile(".*Internal Server Error.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern badGatewayErrorPattern = Pattern.compile(".*502 Bad Gateway.*",
            Pattern.CASE_INSENSITIVE);

    static Pattern cannotEditRecordErrorPattern = Pattern.compile(".*Cannot edit a record.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern cannotSelectRecordErrorPattern = Pattern.compile(".*Cannot select a record.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern cannotCreateRecordErrorPattern = Pattern.compile(".*Cannot create a record.*",
            Pattern.CASE_INSENSITIVE);
    private Map<Pattern,String> transferJournalErrorAndFailureTypeMap;

    @PostConstruct
    public void init() {
        transferJournalErrorAndFailureTypeMap = new HashMap<>();
        transferJournalErrorAndFailureTypeMap.put(badGatewayErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        transferJournalErrorAndFailureTypeMap.put(d365InternalServerErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        transferJournalErrorAndFailureTypeMap.put(socketTimeoutExceptionPattern, MovementJournalFailureType.RETRYABLE.name());
        transferJournalErrorAndFailureTypeMap.put(cannotEditRecordErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        transferJournalErrorAndFailureTypeMap.put(cannotSelectRecordErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        transferJournalErrorAndFailureTypeMap.put(cannotCreateRecordErrorPattern, MovementJournalFailureType.RETRYABLE.name());
    }

    static Pattern documentNoAlreadyExistPattern = Pattern.compile("The document no .* is already exists.",
            Pattern.CASE_INSENSITIVE);

    @SneakyThrows
    @Override
    public void createTransferJournalByRangeSync(Long start, Long end) {

        if ( start>end || end-start>MAX_LIMIT){
            log.error("Please avoid range greater than limit, start : {}, end :{}, limit : {}", start, end, MAX_LIMIT);
            throw new InvalidRequestException("Please avoid range greater than limit ");
        }

        Pageable pageable;

        int total=0;
        int currentPage=0;
        while (total < MAX_LIMIT){
            pageable = PageRequest.of(currentPage, pageSize, Sort.by("id"));
            List<TransferJournal> transferJournals = transferJournalRepository.findTransferJournalByRange(start, end, pageable);

            if (CollectionUtils.isEmpty(transferJournals)){
                break;
            }

            transferJournals.forEach(this::persistAndSyncToD365);
            total+= transferJournals.size();
            currentPage++;
        }
    }

    @Override
    public void createTransferJournalById(Long id) {
        Optional<TransferJournal> transferJournalOptional = transferJournalRepository.findById(id);

        TransferJournal transferJournal = null;
        if (transferJournalOptional.isPresent()){
            transferJournal = transferJournalOptional.get();
        }
        if (Objects.isNull(transferJournal) || Constants.Common.SUCCESS_STATUS.equals(transferJournal.getD365SyncStatus())) {
            return;
        }
        persistAndSyncToD365(transferJournal);
    }

    @SneakyThrows
    @Override
    public void createTransferJournalByRangeAsync(Long start, Long end) {

        if ( start>end || end-start>MAX_LIMIT){
            log.error("Please avoid range greater than limit, start : {}, end :{}, limit : {}", start, end, MAX_LIMIT);
            throw new InvalidRequestException("Please avoid range greater than limit ");
        }

        Pageable pageable;

        int total=0;
        int currentPage=0;
        while (total < MAX_LIMIT){
            pageable = PageRequest.of(currentPage, pageSize, Sort.by("id"));
            List<TransferJournal> transferJournals = transferJournalRepository.findTransferJournalByRange(start, end, pageable);

            if (CollectionUtils.isEmpty(transferJournals)){
                break;
            }

            transferJournals.forEach(p->pushToKafka(p.getId()));
            total+= transferJournals.size();
            currentPage++;
        }
    }

    @Override
    public void createTransferJournalSync(List<Long> ids) {
        ids.forEach(this::createTransferJournalById);
    }
    @Override
    public void createTransferJournalAsync(List<Long> ids) {
        ids.forEach(this::pushToKafka);
    }

    private void persistAndSyncToD365(TransferJournal transferJournal) {

        if (Objects.nonNull(transferJournal)){

            if (Constants.Common.SUCCESS_STATUS.equals(transferJournal.getD365SyncStatus())) {
                return;
            }

            if (StringUtils.isEmpty(transferJournal.getDocumentNo())){
                transferJournal.setDocumentNo(generateDocumentNo(transferJournal));
            }

            if (StringUtils.isEmpty(transferJournal.getD365SyncStatus())){
                transferJournal.setD365SyncStatus("IN_PROGRESS");
            }
            
            transferJournal.setUpdatedBy(Constants.Common.financeConsumer);
            transferJournal = transferJournalRepository.save(transferJournal);
            syncToD365(transferJournal);
        }
    }

    private void syncToD365(TransferJournal transferJournal) {

        TransferJournalHeaderDto header = generateTransferJournalPayload(transferJournal);
        try{
            ResponseEntity responseEntity =
                    genericClientService.forwardRequest(financeAdapterUrl + Constants.TransferJournal.CREATE_URL_V1, new HttpHeaders(), HttpMethod.POST, header);
            HashMap responseBody = (HashMap) responseEntity.getBody();
            if (Objects.nonNull(responseEntity) && responseEntity.getStatusCode().is2xxSuccessful()) {
                if (Objects.nonNull(responseBody)) {
                    String message = (String) responseBody.get("Message");
                    boolean isSuccess= (boolean)responseBody.get("Success");
                    if(isSuccess){
                        transferJournal.setD365SyncStatus(Constants.Common.SUCCESS_STATUS);
                    }else{
                        String status = documentNoAlreadyExistPattern.matcher(message).find() ? Constants.Common.SUCCESS_STATUS : Constants.Common.FAILED_STATUS;
                        transferJournal.setD365SyncStatus(status);
                    }
                    transferJournal.setResponseMessage(message);
                }
            }else {
                if (Objects.nonNull(responseBody)){
                    String message = (String) responseBody.get("Message");
                    transferJournal.setResponseMessage(message);
                }
                transferJournal.setD365SyncStatus(Constants.Common.FAILED_STATUS);
            }
        }catch (Exception e){
            log.error("[TransferJournalServiceV1Impl][syncToD365] Exception occuried during api call of transfer journal request : {} , message : {}, stack_trace : {}", header, e.getMessage(), e);
            transferJournal.setD365SyncStatus("FAILED");
            transferJournal.setResponseMessage(genericClientService.getErrorMessage(e));
        }finally {
            if(Constants.Common.FAILED_STATUS.equals(transferJournal.getD365SyncStatus())){
                transferJournal.setErrorType(getErrorTypeForFailedSync(transferJournal));
            }
            transferJournal.setRetryCount(transferJournal.getRetryCount() + 1);
            transferJournal.setUpdatedBy(Constants.Common.financeConsumer);
            transferJournalRepository.save(transferJournal);
        }
    }

    private TransferJournalHeaderDto generateTransferJournalPayload(TransferJournal transferJournal) {

        TransferJournalHeaderDto header = new TransferJournalHeaderDto();
        List<TransferJournalLineDto> transferJourLines = new ArrayList<>();
        header.setTransferJourLines(transferJourLines);

        header.setLegalEntity(transferJournal.getLegalEntity());
        header.setDocumentNumber(transferJournal.getDocumentNo());
        header.setDescription("");
        header.setJournalNameId("Transfer");
        header.setDocumentNumber(transferJournal.getDocumentNo());

        TransferJournalLineDto transferJournalLineDto = new TransferJournalLineDto();
        transferJournalLineDto.setItemNumber(transferJournal.getProductId());
        transferJournalLineDto.setJournalTransDate(DateUtils.getISODateStringFromLocalDateTime
                (transferJournal.getTransactionDate()));
        transferJournalLineDto.setFromWarehouse(transferJournal.getSourceFacilityCode());
        transferJournalLineDto.setToWarehouse(transferJournal.getDestinationFacilityCode());
        transferJournalLineDto.setQty(transferJournal.getQty());
        transferJournalLineDto.setSerialNumber(transferJournal.getBarcode());
        transferJournalLineDto.setUnit("Pcs");
        transferJourLines.add(transferJournalLineDto);

        return header;
    }
    private String generateDocumentNo(TransferJournal transferJournal) {

        return new StringBuilder()
                .append(transferJournal.getJournalType())
                .append("_")
                .append(UUID.randomUUID())
                .append("_")
                .append(transferJournal.getId())
                .toString();
    }
    private String getErrorTypeForFailedSync(TransferJournal transferJournal) {
        String responseMessage = transferJournal.getResponseMessage();
        if (!StringUtils.isEmpty(responseMessage)) {
            responseMessage = responseMessage.trim();
            for (Map.Entry<Pattern, String> patternErrorTypeEntry : transferJournalErrorAndFailureTypeMap.entrySet()) {
                if (patternErrorTypeEntry.getKey().matcher(responseMessage).find()){
                    return patternErrorTypeEntry.getValue();
                }
            }
        }
        return (MovementJournalFailureType.OTHERS.name());
    }

    private void pushToKafka(Long id) {
        try{
            kafkaProducerTemplate.send(transferJournalKafkaTopic, String.valueOf(id) , ObjectHelper.writeValue(id));
        }catch (Exception e){
            log.error("[TransferJournalServiceV1Impl] pushToKafka : while producing event id: {}, topic: {}, exception: {}",id ,transferJournalKafkaTopic, e);
        }
    }

    @Override
    public void syncNonSyncedTransferJournals() {
        Pageable pageable;
        int currentPage=0;
        int totalRecordsProcessed = 0;

        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(Constants.TransferJournal.TRANSFER_JOURNAL_CRON_NAME);
        if(Boolean.FALSE.equals(schedulerConfig.getEnabled())){
            log.info("[TransferJournalServiceV1Impl] : Terminating new transfer journal job as scheduled cron is disabled");
            return;
        }
        int range = schedulerConfig.getRecordLimit();
        int startId = Integer.parseInt(schedulerConfig.getLastProcessed()) + 1;
        int pageLimit = Integer.parseInt(schedulerConfig.getRecordInterval());
        int endId = startId + range;

        if(startId > transferJournalRepository.findLastId()){
            log.info("[TransferJournalServiceV1Impl] : Terminating transfer journal job as all new rows have been processed, last Id Processed : {}",startId);
            return;
        }
        while(true){
            pageable= PageRequest.of(currentPage, pageLimit, Sort.by("id"));
            List<Long> ids = transferJournalRepository.findNonSyncedTransferRecords(startId,endId,pageable);
            if(CollectionUtils.isEmpty(ids)){
                break;
            }
            ids.forEach(this::pushToKafka);
            totalRecordsProcessed+=ids.size();
            currentPage++;
        }
        schedulerConfigService.updateLastProcessedForSchedulerConfig(schedulerConfig, Integer.toString(endId));
        log.info("[TransferJournalServiceV1Impl] : total records processed : {}",totalRecordsProcessed);
    }

    @Override
    public void syncFailedTransferJournals() {
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(Constants.TransferJournal.TRANSFER_JOURNAL_RETRY_CRON_NAME);
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime updatedAtLimit = currentTime.minusMinutes(Integer.parseInt(schedulerConfig.getRecordInterval()));
        if(Boolean.FALSE.equals(schedulerConfig.getEnabled())){
            log.info("[TransferJournalServiceImpl] : Terminating retry transfer journal job as cron is disabled ");
            return;
        }
        int totalFailedRecordsLimit=schedulerConfig.getRecordLimit();
        List<TransferJournal> failedTransferJournalList = transferJournalRepository.findFailedRecords(transferJournalFailureRetryLimit, totalFailedRecordsLimit,updatedAtLimit);
        if (!CollectionUtils.isEmpty(failedTransferJournalList)){
            processFailedRecords(failedTransferJournalList);
        }
        log.info("[TransferJournalServiceImpl] : total failed Inventory upload records processed for sync : {}", failedTransferJournalList.size());
    }

    private void processFailedRecords(List<TransferJournal> failedTransferJournalList) {
        for (TransferJournal transferJournal : failedTransferJournalList) {
            MovementJournalFailureType failureType = MovementJournalFailureType.valueOf(transferJournal.getErrorType());
            switch (failureType) {
                case RETRYABLE : pushToKafka(transferJournal.getId());
                    break;
            }
        }
    }
    @Override
    public TransferJournalResponseDto createTransferJournalAsync(D365TransferJournalDTO d365TransferJournalDTO) {
        return null;
    }
}
