package com.lenskart.financeConsumer.service.impl;
import au.com.bytecode.opencsv.CSVWriter;
import com.google.gson.Gson;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dao.mongo.FinanceConsumerLogsRepository;
import com.lenskart.financeConsumer.dao.HubMasterRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.d365requests.GenericRetryDto.ApiLogDto;
import com.lenskart.financeConsumer.dto.d365requests.GenericRetryDto.ResponseLogDto;
import com.lenskart.financeConsumer.model.mongo.D365ApiLog;
import com.lenskart.financeConsumer.model.mongo.FinanceConsumerLogs;
import com.lenskart.financeConsumer.service.ApiLogService;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.ObjectHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;


@Slf4j
@Service
public class ApiLogServiceImpl implements ApiLogService {

    @Autowired
    FinanceConsumerLogsRepository financeConsumerLogsRepository;

    @Autowired
    GenericClientService genericClientService;

    @Autowired
    D365LogRepository d365LogRepository;

    @Autowired
    HubMasterRepository hubMasterRepository;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Autowired
    UwOrdersRepository uwOrderRepository;

    @Autowired
    UwOrdersService uwOrdersService;

    @Value("${D365.forward.url}")
    private String D365BaseUrl;

    @Value("${d365.itemmaster.kafka.topic}")
    private String ItemMasterTopic;

    @Value("${packingSlip.kafka.topic}")
    private String packingSlipTopic;

    @Value("${d365.salesorder.kafka.topic}")
    private String SalesOrderTopic;

    public ApiLogServiceImpl() throws IOException {
    }


    @Override
    public List<ApiLogDto> getLogs(String apiUrl, String orderType, Pageable pg) {
        log.info("[ApiLogServiceImpl][getLogs] Fetching logs - apiUrl: {} | orderType: {} | page: {} | size: {}",
                apiUrl, orderType, pg.getPageNumber(), pg.getPageSize());
        List<ApiLogDto> D365LogList = new ArrayList<ApiLogDto>();
        List<ApiLogDto> financeConsumerLogList = new ArrayList<ApiLogDto>();
        List<ApiLogDto> response = new ArrayList<ApiLogDto>();
        if (apiUrl.equals(Constants.SalesOrder.URL)) {
            apiUrl = D365BaseUrl + apiUrl;
            D365LogList = d365LogRepository.findSaleOrderLogs(apiUrl, orderType, Constants.Common.FAILURE, pg);
            response.addAll(D365LogList);
            financeConsumerLogList = financeConsumerLogsRepository.findSaleOrderLogs(apiUrl, orderType, Constants.Common.FAILURE, pg);
            response.addAll(financeConsumerLogList);

        } else {
            apiUrl = D365BaseUrl + apiUrl;
            log.info("apiUrl :"+apiUrl);
            log.info("common:"+Constants.Common.FAILURE);
            log.info("orderType:" +orderType);
            D365LogList = d365LogRepository.findAllByApiUrlAndStatusAndOrderType(apiUrl, orderType, Constants.Common.FAILURE, pg);
            log.info("D365LogList :" + D365LogList);
            if (!D365LogList.isEmpty()) {
                response.addAll(D365LogList);
                log.info("D365 Logs are added");
            }
            if (D365LogList.size() != pg.getPageSize()) {
                Integer sizeNo = (pg.getPageSize() - D365LogList.size());
                Pageable page = PageRequest.of(pg.getPageNumber(), sizeNo, Sort.by("created_at").descending());
                financeConsumerLogList = financeConsumerLogsRepository.findAllByApiUrlAndStatusAndOrderType(apiUrl, orderType, Constants.Common.FAILURE, page);
                log.info("financeConsumerLogList :" + financeConsumerLogList);
                response.addAll(financeConsumerLogList);
            }
        }
        return response;
    }


    public ResponseLogDto genericCall(String apiUrl, String orderType, Pageable pg)
    {
        ResponseLogDto responseLogDto=new ResponseLogDto();
        List<ApiLogDto> response=new ArrayList<ApiLogDto>();
        long fcCount;
        long faCount;
        long count;
        fcCount = FcCount(apiUrl,orderType);
        log.info("fc count:"+fcCount);
        faCount = FaCount(apiUrl,orderType);
        log.info("fa count:"+faCount);
        count=fcCount+faCount;
        response= getLogs(apiUrl,orderType,pg);
        responseLogDto.setApiLogDto(response);
        responseLogDto.setCount(count);
        return responseLogDto;
    }



    public boolean retryD365Log(String id) throws Exception {
        try {
            log.info("[ApiLogServiceImpl][retryD365Log]-id:" + id);
            log.info("getting the status of the record from the database");
            D365ApiLog logs = d365LogRepository.findById(id).orElse(null);
            int retry_count = logs.getRetry_count() + 1;
            logs.setRetry_count(retry_count);
            logs.setStatusCode(logs.getStatusCode());
            logs.setStatus("IN_PROCESS");
            logs.setUpdatedAt(new Date());
            log.info("Log :" + logs);

            D365ApiLog d365ApiLog = d365LogRepository.save(logs);
            log.info("[ApiLogServiceImpl][retryD365Log] Log saved successfully - logId: {} | entityId: {}",
                    d365ApiLog.getId(), logs.getEntityId());
            if (logs.getApiUrl().contains(Constants.ItemMaster.URL)) {
                try {
                    log.info("[ApiLogServiceImpl][retryD365Log] Retrying ItemMaster - entityId: {}", logs.getEntityId());
                    kafkaProducerTemplate.send(ItemMasterTopic,String.valueOf(logs.getEntityId()));
                } catch (Exception e) {
                    log.error("[ApiLogServiceImpl][retryD365Log] ItemMaster retry failed - logId: {} | entityId: {} | error: {}",
                            logs.getId(), logs.getEntityId(), e.getMessage(), e);
                    logs.setMessage(e.getMessage());
                    logs.setStatusCode(logs.getStatusCode());
                    logs.setStatus("Failure");
                    logs.setUpdatedAt(new Date());
                    d365ApiLog = d365LogRepository.save(logs);
                    return false;
                    //throw new Exception(e.getMessage());
                }
            }
            if (logs.getApiUrl().contains(Constants.PackingSlip.URL)) {
                try {
                    log.info("[ApiLogServiceImpl][retryD365Log] Retrying PackingSlip - entityId: {}", logs.getEntityId());
                    List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(logs.getEntityId());
                    pushToD365SaleOrder(unicomOrderIds, packingSlipTopic);
                } catch (Exception e) {
                    log.error("[ApiLogServiceImpl][retryD365Log] PackingSlip retry failed - logId: {} | entityId: {} | error: {}",
                            logs.getId(), logs.getEntityId(), e.getMessage(), e);
                    logs.setMessage(e.getMessage());
                    logs.setStatusCode(logs.getStatusCode());
                    logs.setStatus("Failure");
                    logs.setUpdatedAt(new Date());
                    d365ApiLog = d365LogRepository.save(logs);
                    return false;
                    //throw new Exception(e.getMessage());
                }
            }
            if (logs.getApiUrl().contains(Constants.SalesOrder.URL)) {
                try {
                    log.info("[ApiLogServiceImpl][retryD365Log]-SalesOrder :" + logs.getEntityId());
                    List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(logs.getEntityId());
                    pushToD365SaleOrder(unicomOrderIds, SalesOrderTopic);
                } catch (Exception e) {
                    log.info("[ApiLogServiceImpl][retryD365Log]-SaleOrder Failure:" + logs.getId());
                    log.error(e.getMessage(), e);
                    logs.setMessage(e.getMessage());
                    logs.setStatusCode(logs.getStatusCode());
                    logs.setStatus("Failure");
                    logs.setUpdatedAt(new Date());
                    d365ApiLog = d365LogRepository.save(logs);
                    return false;

                }
            }
            log.info("[ApiLogServiceImpl][retryD365Log]-pushed in kafka" + logs.getId());

            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }

    }

    public boolean retryFinanceConsumerLog(String id) throws Exception {
        try {
            log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-id:" + id);
            FinanceConsumerLogs logs = financeConsumerLogsRepository.findById(id).orElse(null);
            int retry_count = logs.getRetry_count() + 1;
            logs.setRetry_count(retry_count);
            logs.setStatus("IN_PROCESS");
            logs.setUpdatedAt(new Date());
            log.info("Log :" + logs);
            FinanceConsumerLogs financeConsumerLogs = financeConsumerLogsRepository.save(logs);
            log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-after saving :" + financeConsumerLogs);
            if (logs.getApiUrl().contains(Constants.ItemMaster.URL)) {
                try {
                    log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-ItemMaster :" + logs.getEntityId());
                    kafkaProducerTemplate.send( ItemMasterTopic,String.valueOf(logs.getEntityId()));
                } catch (Exception e) {
                    log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-ItemMaster Failure:" + logs.getId());
                    log.error(e.getMessage(), e);
                    logs.setStatusCode(logs.getStatusCode());
                    logs.setMessage(e.getMessage());
                    logs.setStatus("Failure");
                    logs.setUpdatedAt(new Date());
                    financeConsumerLogs = financeConsumerLogsRepository.save(logs);

                    return false;
                }

            }
            if (logs.getApiUrl().contains(Constants.PackingSlip.URL)) {
                try {

                    List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(logs.getEntityId());
                    log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-PackingSlip :" + logs.getEntityId());
                    pushToD365SaleOrder(unicomOrderIds, packingSlipTopic);
                } catch (Exception e) {
                    log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-PackingSlip Failure:" + logs.getId());
                    log.error(e.getMessage(), e);
                    logs.setMessage(e.getMessage());
                    logs.setStatusCode(logs.getStatusCode());
                    logs.setStatus("Failure");
                    logs.setUpdatedAt(new Date());
                    financeConsumerLogs = financeConsumerLogsRepository.save(logs);
                    return false;

                }
            }
            if (logs.getApiUrl().contains(Constants.SalesOrder.URL)) {
                try {
                    List<String> unicomOrderIds = uwOrdersService.getUnicomOrderCodeFromShippingPackageId(logs.getEntityId());
                    //todo: check
                    log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-SalesOrder :" + logs.getEntityId());
                    pushToD365SaleOrder(unicomOrderIds, SalesOrderTopic);
                } catch (Exception e) {
                    log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-SaleOrder Failure:" + logs.getId());
                    log.error(e.getMessage(), e);
                    logs.setMessage(e.getMessage());
                    logs.setStatusCode(logs.getStatusCode());
                    logs.setStatus("Failure");
                    logs.setUpdatedAt(new Date());
                    financeConsumerLogs = financeConsumerLogsRepository.save(logs);
                    return false;

                }
            }
            log.info("[ApiLogServiceImpl][retryFinanceConsumerLog]-pushed in kafka" + logs.getId());

            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public long FaCount(String apiUrl, String orderType) {
        Long count;
        if (apiUrl.equals(Constants.SalesOrder.URL)) {
            apiUrl = D365BaseUrl + apiUrl;
            count = d365LogRepository.CountSales(apiUrl, orderType, Constants.Common.FAILURE);
            log.info("Sales count:" + count);
        } else {
            apiUrl = D365BaseUrl + apiUrl;
            count = d365LogRepository.Count(apiUrl, orderType, Constants.Common.FAILURE);
            log.info("count :" + count);
        }
        return count;
    }

    public long FcCount(String apiUrl, String orderType) {
        Long count;
        if (apiUrl.equals(Constants.SalesOrder.URL)) {
            apiUrl = D365BaseUrl + apiUrl;
            count =  financeConsumerLogsRepository.CountSales(apiUrl, orderType, Constants.Common.FAILURE);
            log.info("Sales count:" + count);
        } else {
            apiUrl = D365BaseUrl + apiUrl;
            count = financeConsumerLogsRepository.Count(apiUrl, orderType, Constants.Common.FAILURE);
            log.info("count :" + count);
        }
        return count;
    }


    @Override
    public boolean skipD365Log(String id) throws Exception {
        try {
            log.info("[ApiLogServiceImpl][skipD365Log] Skipping log - logId: {}", id);
            D365ApiLog logs = d365LogRepository.findById(id).orElse(null);
            logs.setStatus("Skipped");
            logs.setUpdatedAt(new Date());
            D365ApiLog d365ApiLog = d365LogRepository.save(logs);
            log.info("[ApiLogServiceImpl][skipD365Log] Log skipped successfully - logId: {} | entityId: {}",
                    id, logs.getEntityId());
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean skipFinanceConsumerLog(String id) throws Exception {
        try {
            log.info("[ApiLogServiceImpl][skipFinanceConsumerLog]-id:" + id);
            FinanceConsumerLogs logs = financeConsumerLogsRepository.findById(id).orElse(null);
            logs.setStatus("Skipped");
            logs.setUpdatedAt(new Date());
            log.info("Log :" + logs);
            FinanceConsumerLogs financeConsumerLogs = financeConsumerLogsRepository.save(logs);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }


    @Override
     public void pushToD365SaleOrder(List<String> unicomOrderIds, String topic) throws Exception { //158073463
        try {
            log.info("[ApiLogServiceImpl][pushToD365SaleOrder] Processing orders - unicomOrderCount: {} | topic: {}",
                    unicomOrderIds != null ? unicomOrderIds.size() : 0, topic);
            List<UwOrder> uwOrders = uwOrderRepository.findByUnicomOrderCodeIn(unicomOrderIds);
            Map<String, List<Integer>> ordersMap = new HashMap<>();
            ordersMap = getUwItemIdsForKafka(uwOrders);

            for (Map.Entry<String, List<Integer>> entry : ordersMap.entrySet()) {
                log.info("[ApiLogServiceImpl][pushToD365SaleOrder] Publishing to Kafka - facilityCode: {} | itemCount: {} | topic: {}",
                        entry.getKey(), entry.getValue() != null ? entry.getValue().size() : 0, topic);
                kafkaProducerTemplate.send( topic, ObjectHelper.writeValue(entry.getValue()));
            }

        } catch (Exception e) {
            log.error("[pushToD365SaleOrder] Exception Occurred while pushing to D365 sales order ", e);
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public Map<String, List<Integer>> getUwItemIdsForKafka(List<UwOrder> uwOrders){
        Map<String, List<Integer>> ordersMap = new HashMap<>();
        List<String> facilities = hubMasterRepository.findAllFacilityCode();
        for(UwOrder uwOrder : uwOrders) {
            if(!StringUtils.isEmpty(uwOrder.getProductDeliveryType()) && uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                if(!StringUtils.isEmpty(uwOrder.getNavChannel()) && uwOrder.getNavChannel().toLowerCase().contains("fofo") &&
                        facilities.contains(uwOrder.getFacilityCode())) { // for fofo send warehouse facility orders to D365
                    List<Integer> uwItemIds = ordersMap.getOrDefault(uwOrder.getUnicomOrderCode(), new ArrayList<>());
                    uwItemIds.add(uwOrder.getUwItemId());
                    ordersMap.put(uwOrder.getUnicomOrderCode(), uwItemIds);
                } else if(!StringUtils.isEmpty(uwOrder.getNavChannel())) { // for all B2B only send virtual facility orders
                    UwOrder virtualUwOrder = uwOrderRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId()); // 334600332, LLKST58611098
                    if(virtualUwOrder != null && !facilities.contains(virtualUwOrder.getFacilityCode())) {
                        List<Integer> uwItemIds = ordersMap.getOrDefault(virtualUwOrder.getUnicomOrderCode(), new ArrayList<>());
                        uwItemIds.add(virtualUwOrder.getUwItemId()); //334600332
                        ordersMap.put(virtualUwOrder.getUnicomOrderCode(), uwItemIds);
                    }
                    else if(!facilities.contains(uwOrder.getFacilityCode())) { //370277268
                        List<Integer> uwItemIds = ordersMap.getOrDefault(uwOrder.getUnicomOrderCode(), new ArrayList<>());
                        uwItemIds.add(uwOrder.getUwItemId());
                        ordersMap.put(uwOrder.getUnicomOrderCode(), uwItemIds);
                    }

                }
            } else {// dtc and other order type
                List<Integer> uwItemIds = ordersMap.getOrDefault(uwOrder.getUnicomOrderCode(), new ArrayList<>());
                uwItemIds.add(uwOrder.getUwItemId());
                ordersMap.put(uwOrder.getUnicomOrderCode(), uwItemIds);
            }
        }
        return ordersMap;
    }

    @Override
    public List<Integer> getUwItemIdsFromShipment(List<String> unicomOrderIds) throws Exception {
        try {
            log.info("[pushToD365SaleOrder] unicomOrderIds = "+unicomOrderIds);
            List<UwOrder> uwOrders = uwOrderRepository.findByUnicomOrderCodeIn(unicomOrderIds);
            List<String> facilities = hubMasterRepository.findAllFacilityCode();
            Map<String, List<Integer>> ordersMap = new HashMap<>();
            for(UwOrder uwOrder : uwOrders) {
                if(!StringUtils.isEmpty(uwOrder.getProductDeliveryType()) && uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B")) {
                    if(!StringUtils.isEmpty(uwOrder.getNavChannel()) && uwOrder.getNavChannel().toLowerCase().contains("fofo") &&
                            facilities.contains(uwOrder.getFacilityCode())) { // for fofo send warehouse facility orders to D365
                        List<Integer> uwItemIds = ordersMap.getOrDefault(uwOrder.getUnicomOrderCode(), new ArrayList<>());
                        uwItemIds.add(uwOrder.getUwItemId());
                        ordersMap.put(uwOrder.getUnicomOrderCode(), uwItemIds);
                    } else if(!StringUtils.isEmpty(uwOrder.getNavChannel())) { // for all B2B only send virtual facility orders
                        UwOrder virtualUwOrder = uwOrderRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());
                        if(virtualUwOrder != null && !facilities.contains(virtualUwOrder.getFacilityCode())) {
                            List<Integer> uwItemIds = ordersMap.getOrDefault(virtualUwOrder.getUnicomOrderCode(), new ArrayList<>());
                            uwItemIds.add(virtualUwOrder.getUwItemId());
                            ordersMap.put(virtualUwOrder.getUnicomOrderCode(), uwItemIds);
                        } else if(!facilities.contains(uwOrder.getFacilityCode())) {
                            List<Integer> uwItemIds = ordersMap.getOrDefault(uwOrder.getUnicomOrderCode(), new ArrayList<>());
                            uwItemIds.add(uwOrder.getUwItemId());
                            ordersMap.put(uwOrder.getUnicomOrderCode(), uwItemIds);
                        }
                    }
                } else {// dtc and other order type
                    List<Integer> uwItemIds = ordersMap.getOrDefault(uwOrder.getUnicomOrderCode(), new ArrayList<>());
                    uwItemIds.add(uwOrder.getUwItemId());
                    ordersMap.put(uwOrder.getUnicomOrderCode(), uwItemIds);
                }
            }

            for (Map.Entry<String, List<Integer>> entry : ordersMap.entrySet()) {
                log.info("pushToD365SaleOrder order value pushed to kafka: " + new Gson().toJson(entry.getValue()));
              //  kafkaProducerTemplate.send( topic,new Gson().toJson(entry.getValue()));
                return entry.getValue();
            }

        } catch (Exception e) {
            log.error("[pushToD365SaleOrder] Exception Occurred while pushing to D365 sales order ", e);
            throw new Exception(e.getMessage());
        }
        return null;
    }


    public void exportLogData(Integer tabId, HttpServletResponse servletResponse) throws IOException {
        List<ApiLogDto> financeConsumerLogList = new ArrayList<ApiLogDto>();
        List<ApiLogDto> D365LogList = new ArrayList<ApiLogDto>();
        List<ApiLogDto> response = new ArrayList<ApiLogDto>();
        String apiUrl = D365BaseUrl;
        String orderType = "";
        if (1 == tabId) {
            apiUrl = apiUrl.concat(Constants.ItemMaster.URL);
            financeConsumerLogList = financeConsumerLogsRepository.findAllByApiUrlAndStatusAndOrderTypeToDownload(apiUrl, orderType, Constants.Common.FAILURE);
            D365LogList = d365LogRepository.findAllByApiUrlAndStatusAndOrderTypeToDownload(apiUrl, orderType, Constants.Common.FAILURE);
        }
        if (2 == tabId) {
            apiUrl = apiUrl.concat(Constants.PackingSlip.URL);
            financeConsumerLogList = financeConsumerLogsRepository.findAllByApiUrlAndStatusAndOrderTypeToDownload(apiUrl, Constants.PackingSlip.OrderType, Constants.Common.FAILURE);
            D365LogList = d365LogRepository.findAllByApiUrlAndStatusAndOrderTypeToDownload(apiUrl, Constants.PackingSlip.OrderType, Constants.Common.FAILURE);

        }
        if (3 == tabId) {
            apiUrl = apiUrl.concat(Constants.SalesOrder.URL);
            financeConsumerLogList = financeConsumerLogsRepository.findSaleOrderLogsToDownload(apiUrl,Constants.SalesOrder.OrderType, Constants.Common.FAILURE);
            D365LogList = d365LogRepository.findSaleOderLogsToDownload(apiUrl,Constants.SalesOrder.OrderType, Constants.Common.FAILURE);
        }

        response.addAll(financeConsumerLogList);
        response.addAll(D365LogList);
        String[] header = new String[8];
        header[0] = "ID";
        header[1] = "Entity-id";
        header[2] = "Message";
        header[3] = "Status";
        header[4] = "Created_at";
        header[5] = "Updated_at";
        header[6] = "Retry_count";
        if(3 == tabId)
        {
            header[7] = "Increment-id";
        }
        CSVWriter writer = new CSVWriter(new FileWriter("Log-Data.csv"), Constants.CSV_CONSTANTS.CSV_SEPARATOR);
        writer.writeNext(header);
        try {
            Iterator<ApiLogDto> itr = response.iterator();

            while (itr.hasNext()) {
                ApiLogDto apiLogDto = itr.next();
                String[] fields = objectToStringArray(apiLogDto,tabId);
                writer.writeNext(fields);
            }
            writer.close();
            servletResponse.setContentType("application/csv;charset=utf-8");
            String headerKey = "Content-Disposition";
            String headerValue = String.format("attachment; filename= \"Log-Data.csv\"");
            servletResponse.setHeader(headerKey, headerValue);
            OutputStream output = servletResponse.getOutputStream();
            FileInputStream fileIn = new FileInputStream("Log-Data.csv");
            int i = 0;
            while ((i = fileIn.read()) != -1) {
                output.write((byte) i);
            }
            log.info("Exiting exportLogData function");
            output.close();
        }
        catch(IOException e){
            log.error("Error While writing CSV ", e);
            throw e;
        }

    }
    public String[] objectToStringArray(ApiLogDto apiLogDto,Integer tabId) {
        log.debug("[ApiLogServiceImpl][objectToStringArray] Converting log to array - logId: {} | tabId: {}",
                apiLogDto.getId(), tabId);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String[] fields = new String[8];
        fields[0] =apiLogDto.getId();
        fields[1] = apiLogDto.getEntity_id();
        fields[2] = apiLogDto.getMessage();
        fields[3] = apiLogDto.getStatus() ;
        fields[4] = formatter.format(apiLogDto.getCreatedAt());
        fields[5] = formatter.format(apiLogDto.getUpdatedAt());
        fields[6] = String.valueOf(apiLogDto.getRetry_count());
        if(3 == tabId)
        {
            fields[7] = apiLogDto.getIncrementId();
        }
        return fields;
    }

}
