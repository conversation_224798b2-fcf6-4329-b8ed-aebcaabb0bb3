package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.core.model.D365ReturnTracking;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.core.request.ReturnEInvoicingKafkaRequest;
import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.config.AppConfig;
import com.lenskart.financeConsumer.dao.D365ReturnTrackingRepository;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.OrdersHeaderRepository;
import com.lenskart.financeConsumer.dao.ReturnHistoryRepository;
import com.lenskart.financeConsumer.dao.ReturnOrderAddressUpdateRepository;
import com.lenskart.financeConsumer.dao.ReturnOrderItemRepository;
import com.lenskart.financeConsumer.dao.ReturnOrderRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.FinanceConsumerResponse.FinanceConsumerResponseDto;
import com.lenskart.financeConsumer.dto.GiftCardEligibilityCheckDto;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardEligibilityRequestDto;
import com.lenskart.financeConsumer.dto.d365requests.GiftCardSyncDto;
import com.lenskart.financeConsumer.dto.SerialNumberErrorDto;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnCreateRequest;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnPSlipResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnRequestMessage;
import com.lenskart.financeConsumer.dto.d365requests.Return.ReturnResponseDto;
import com.lenskart.financeConsumer.dto.d365requests.SaleOrderPackingSlipResponseDTO;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.MiscChargesHeader;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.MiscChargesLine;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.PackingSlip;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesLineList;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SalesOrderHeader;
import com.lenskart.financeConsumer.dto.d365requests.SalesOrderDTO.SoLine;
import com.lenskart.financeConsumer.financeDb.inventory.read.UwOrdersReadRepositoryFinance;
import com.lenskart.financeConsumer.financeDb.writeRepository.InventoryCorrectionWriteRepository;
import com.lenskart.financeConsumer.model.OrderItemGSTDetail;
import com.lenskart.financeConsumer.model.ReturnOrderAddressUpdate;
import com.lenskart.financeConsumer.model.financeDb.InventoryCorrection;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.service.CostAverageNewTempService;
import com.lenskart.financeConsumer.service.CourierAssignmentsService;
import com.lenskart.financeConsumer.service.CourierWisePickingService;
import com.lenskart.financeConsumer.service.D365ReturnService;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.HubMasterService;
import com.lenskart.financeConsumer.service.InventoryCorrectionService;
import com.lenskart.financeConsumer.service.ItemMasterService;
import com.lenskart.financeConsumer.service.ItemWisePriceInfoDetails;
import com.lenskart.financeConsumer.service.OrderAddressUpdateService;
import com.lenskart.financeConsumer.service.OrderHeaderService;
import com.lenskart.financeConsumer.service.OrderItemGstDetailsService;
import com.lenskart.financeConsumer.service.OrderService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.service.Return.ReturnOrderItemService;
import com.lenskart.financeConsumer.service.ReturnEInvoiceService;
import com.lenskart.financeConsumer.service.ReturnOrderService;
import com.lenskart.financeConsumer.service.S3InvoiceDetailsService;
import com.lenskart.financeConsumer.service.SBRTOrderItemService;
import com.lenskart.financeConsumer.service.SalesOrderService;
import com.lenskart.financeConsumer.service.ShippingStatusService;
import com.lenskart.financeConsumer.service.SystemPreferenceService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.service.SBRTOrderItemService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.util.MovementType;
import com.lenskart.financeConsumer.util.PosUtils;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.financeConsumer.util.ReturnUtil;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import com.lenskart.inventoryadapter.client.InventoryAdapterClient;
import com.lenskart.ordermetadata.dto.headlessReturn.ReturnDetailsDTO;
import com.lenskart.orderops.model.OrderAddressUpdate;
import com.lenskart.orderops.model.ReturnOrder;
import com.lenskart.orderops.model.ReturnOrderItem;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ReturnOrderServiceImpl
 */
@Service
@Slf4j
public class ReturnOrderServiceImpl implements ReturnOrderService {
    DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);
    @Autowired
    InventoryAdapterClient inventoryAdapterClient;
    @Value("${D365.forward.url}")
    private String D365BaseUrl;
    @Value("${saleOrder.sbrt.sync.enabled}")
    private boolean isSBRTFlowEnabledForSalesOrder;

    @Value("${sbrt.item.flag}")
    private String SBRTItemFlag;

    @Value("${non-sbrt.item.flag}")
    private String nonSBRTItemFlag;

    @Autowired
    private SBRTOrderItemService sbrtOrderItemService;

    @Value("${d365.gift-card.kafka.topic}")
    private String giftCardTopic;

    @Value("${dummy.barcode.suffix.value}")
    private String dummyBarCodeSuffixValue;

    @Value("${d365.returnorder.kafka.topic}")
    private String returnOrderTopic;

    @Autowired
    ObjectMapper objectMapper;

    public static Pattern barcodePattern = Pattern.compile("Serial number (\\S+)", Pattern.CASE_INSENSITIVE);


    @Autowired
    FinanceSystemSyncRepository financeSystemSyncRepository;

    @Autowired
    private D365ReturnService d365ReturnService;

    @Autowired
    UwOrdersRepository uwOrdersRepository;

    @Autowired
    ReturnEInvoiceService returnEInvoiceService;

    @Autowired
    HubMasterService hubMasterService;

    @Autowired
    private NexsClient nexsClient;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Autowired
    D365ReturnTrackingRepository d365ReturnTrackingRepository;
    @Autowired
    InventoryCorrectionService inventoryCorrectionService;
    @Autowired
    UwOrdersReadRepositoryFinance uwOrdersReadRepositoryFinance;
    @Autowired
    ReturnOrderItemRepository returnOrderItemRepository;




    @Autowired
    private InventoryCorrectionWriteRepository inventoryCorrectionWriteRepository;

    @Value("${d365.returnorder.serial-number.cron.start.hours}")
    int startHoursForReturnSerialNumberCron;


    @Value("${d365.serialNumberError.kafka.topic}")
    private String serialNumberCorrectionTopic;

    /**
     * @param returnRequestDto
     * @return returnResponseDto
     * @throws JsonProcessingException
     */
    @Override
    public ReturnResponseDto createReturnNew(ReturnCreateRequest returnRequestDto) throws Exception {
        ReturnResponseDto returnResponseDto = new ReturnResponseDto();
        ObjectMapper mapper = null;
        SalesOrderHeader returnPayload = null;
        ResponseEntity returnResponseEntity = null;
        ResponseEntity packingSlipResponseEntity = null;
        Map returnResponseBody = null;
        Map packingSlipResponseBody = null;
        Boolean status = null;
        Integer uwItemId = returnRequestDto.getUwItemId();
        UwOrder uwOrder = uwOrdersService.getUwOrderByUwItemId(uwItemId);
        Integer incrementId = uwOrder.getIncrementId();
        Integer returnId = null;

        try {
            log.info("[ReturnOrderServiceImpl][createReturn] creating returnPayload for uwItemId : " + uwItemId);
            returnPayload = generatePayloadForReturn(returnPayload, returnRequestDto);
            log.info("[ReturnOrderServiceImpl][createReturn] json returnPayload for uwItemId : {} : {}",uwItemId, gson.toJson(returnPayload));
            if (returnPayload != null) {
                try {
                    checkGiftCardEligibility(uwOrder, returnPayload.getLegalEntity());
                    returnResponseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.ReturnOrder.CREATE_URL, new HttpHeaders(), HttpMethod.POST, returnPayload);
                    log.info("[ReturnOrderServiceImpl][createReturn] returnResponseEntity : " + returnResponseEntity);
                } catch (Exception e) {
                    log.error("ReturnOrderServiceImpl : exception found from client for : {} : ",uwItemId,e);
                    genericClientService.saveLog((String.valueOf(uwItemId))+"_Return", genericClientService.getErrorMessage(e), returnPayload, D365BaseUrl + Constants.ReturnOrder.CREATE_URL, Constants.ORDER_TYPES.RETURNED_ORDER, Constants.Common.FAILURE);
                    returnResponseDto = new ReturnResponseDto();
                    returnResponseDto.setReturnCreationMessage("ReturnOrderServiceImpl : exception found from client for : {}"+uwItemId);
                    return returnResponseDto;
                }
            }
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to generate returnPayload for : {} : ",uwItemId,e);
            returnResponseDto = new ReturnResponseDto();
            returnResponseDto.setReturnCreationMessage("ReturnOrderServiceImpl : exception found to generate returnPayload for : {}"+uwItemId);
            genericClientService.saveLog((String.valueOf(uwItemId))+"_Return", genericClientService.getErrorMessage(e), returnPayload, D365BaseUrl + Constants.ReturnOrder.CREATE_URL, Constants.ORDER_TYPES.RETURNED_ORDER, Constants.Common.FAILURE);
            String responseMessage = getErrorLogMessage(e);
            returnUtil.updateD365Tracking(returnRequestDto, responseMessage);
            return returnResponseDto;
        }

        if (returnResponseEntity != null) {
            returnResponseBody = (Map) returnResponseEntity.getBody();
            log.info("[ReturnOrderServiceImpl][createReturn] return responseBody for uwItemId : {} : {}",uwItemId, returnResponseBody);

        }
        String returnCreationMessage = "";
        if (returnResponseBody != null) {
            returnCreationMessage = (String) returnResponseBody.get("Message");
        }

        if (null != returnResponseBody && returnResponseBody.containsKey(Constants.Common.SUCCESS)) {
            if (returnResponseBody.containsKey(Constants.Common.SUCCESS)) {
                List<String> whitelistedErrors = returnUtil.getReturnOrderErrorWhitelist();
                if(!CollectionUtils.isEmpty(whitelistedErrors) && whitelistedErrors.stream().anyMatch(returnCreationMessage::contains)) {
                    status = true;
                }
                else {
                    status = Boolean.valueOf(String.valueOf(returnResponseBody.get(Constants.Common.SUCCESS)));
                }
            }
            if (returnResponseBody.containsKey(Constants.Common.MESSAGE)) {
                returnId = returnUtil.getReturnIdFromSalesOrderNumber(returnPayload.getSalesOrderNumber());
            }

            if (returnRequestDto.isRetryFlag()) {
                log.info("[ReturnOrderServiceImpl] updating d365 flag in return retry for uwItemId : {}",uwItemId);
                returnUtil.updateReturnD365SyncFlagRetry(returnId, status, returnCreationMessage, returnPayload, returnRequestDto);
            } else {
                log.info("[ReturnOrderServiceImpl] updating d365 flag in regular return for uwItemId : {}",uwItemId);
                returnUtil.updateReturnD365SyncFlag(returnId, status, returnCreationMessage, returnPayload, returnRequestDto);
            }

            //not required in this job
//            if(Objects.nonNull(status) && status) {
//                generateReturnEInvoiceRequestAndPushToKafka(returnPayload);
//            }

        }
        if (returnResponseEntity != null && !returnResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
            log.error("No response received for return creation against uw_item_id " + uwItemId);
            genericClientService.saveLog(String.valueOf(uwItemId)+ "_" + "Return", returnCreationMessage, returnPayload, D365BaseUrl + Constants.ReturnOrder.CREATE_URL, Constants.ORDER_TYPES.RETURNED_ORDER, Constants.Common.FAILURE);
            returnResponseDto.setReturnResponse(returnResponseBody);
            returnResponseDto.setReturnCreationMessage("No response received from client for uw_item_id : " + uwItemId);
            return returnResponseDto;
        }
        log.info("[ReturnOrderServiceImpl] validating return creation message for uwItemId : {} : {}",uwItemId, returnCreationMessage);
        validateReturnCreation(uwItemId, returnPayload, status, returnCreationMessage, returnResponseDto);
        returnResponseDto.setReturnResponse(returnResponseBody);
        if (Boolean.TRUE.equals(status)) {
            returnResponseDto.setReturnCreationMessage(returnCreationMessage);
        } else {
            returnResponseDto.setReturnCreationMessage("Return created failed for uwOrder : " + uwItemId);
            handleReturnSOErrors(returnPayload, returnCreationMessage);
        }
        // packingSlip creation process starts
        String pSlipCreationMessage = null;
        PackingSlip packingSlip = null;
        if (Boolean.TRUE.equals(status) || Boolean.FALSE.equals(status)) {
            try {
                packingSlip = generatePackingSlipPayload(returnPayload, returnRequestDto);
                log.info("[ReturnOrderServiceImpl][createPackingSlip] packingSlip json payload for uwItemId : {} : {}",uwItemId, gson.toJson(packingSlip));
                if (packingSlip != null) {
                    packingSlipResponseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, new HttpHeaders(), HttpMethod.POST, packingSlip);
                    log.info("[ReturnOrderServiceImpl][createPackingSlip] packagingSlip responseEntity : {}",packingSlipResponseEntity);
                }
            } catch (Exception e) {
                genericClientService.saveLog(String.valueOf(uwItemId)+ "_" + "PSlip", genericClientService.getErrorMessage(e), packingSlip, D365BaseUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, Constants.ORDER_TYPES.RETURN_PACKING_SLIP, Constants.Common.FAILURE);
                log.error("createPackingSlip : packingSlip creation failed for uw_item_id {} : ",uwItemId, e);
                String responseMessage = getErrorLogMessage(e);
                returnUtil.updateD365PSlipSyncFlag(returnId, false, responseMessage, returnRequestDto);
            }
            if (packingSlipResponseEntity != null) {
                packingSlipResponseBody = (HashMap) packingSlipResponseEntity.getBody();

            }
            if (packingSlipResponseBody != null) {
                pSlipCreationMessage = (String) packingSlipResponseBody.get("Message");
            }

            Boolean pSlipResponseStatus = null;
            log.info("ReturnOrderServiceImpl : pSlipCreationMessage for uwItemId : {}",uwItemId);
            if (null != packingSlipResponseBody && packingSlipResponseBody.containsKey(Constants.Common.SUCCESS)) {
                pSlipResponseStatus = Boolean.valueOf(String.valueOf(packingSlipResponseBody.get(Constants.Common.SUCCESS)));
                log.info("ReturnOrderServiceImpl : updating pSlipFlag for uwItemId : {}",uwItemId);
                returnUtil.updateD365PSlipSyncFlag(returnId, pSlipResponseStatus, pSlipCreationMessage, returnRequestDto);
            }
            if (!packingSlipResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
                genericClientService.saveLog(String.valueOf(uwItemId)+ "_" + "PSlip", String.valueOf(pSlipCreationMessage), packingSlip, D365BaseUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, Constants.ORDER_TYPES.RETURN_PACKING_SLIP, Constants.Common.FAILURE);
                returnResponseDto.setPackingSlipResponse(packingSlipResponseBody);
                return returnResponseDto;
            }
            returnResponseDto.setPackingSlipResponse(packingSlipResponseBody);
            String pSlipCreationStatus;

            if (Boolean.TRUE.equals(pSlipResponseStatus)) {
                pSlipCreationStatus = Constants.Common.SUCCESS;
                genericClientService.saveLog(String.valueOf(uwItemId)+"_"+"PSlip", pSlipCreationMessage, packingSlip, D365BaseUrl + Constants.SalesOrder.URL, Constants.ORDER_TYPES.RETURN_PACKING_SLIP, pSlipCreationStatus);
            } else {
                pSlipCreationStatus = Constants.Common.FAILURE;
                log.info("pSlipCreationMessage for uwItemId : {} : {}",uwItemId, pSlipCreationMessage);
                log.info("checking for already exists barcode for uwItemId : {}",uwItemId);
//                if (pSlipCreationMessage != null && pSlipCreationMessage.contains(Constants.ReturnOrder.PSlip_Barcode_Already_Exist)) {
//                    log.info("ReturnOrderServiceImpl : processing for movementJournal api for uwItemId : {}", uwItemId);
//                    Date saleOrderCompleteTime = returnUtil.getSaleOrderDispatchDate(uwOrder);
//                    Date financeClosingDate = returnUtil.getFinanceClosingDate(uwItemId);
//                    boolean negativeAdjustmentFlag = returnUtil.compareDate(saleOrderCompleteTime, financeClosingDate, uwItemId);
//                    log.info("ReturnOrderServiceImpl : date compare flag for uwItemId : {} {}",uwItemId, negativeAdjustmentFlag);
//                    if (negativeAdjustmentFlag) {
//                        log.info("saleOrderCompleteTime is BEFORE 31st March Processing for negative adjustment for uwItemId : {}", uwItemId);
//                        processForNegativeAdjustment(uwOrder, packingSlip, returnResponseDto, returnId, returnRequestDto);
//                    } else  {
//                        log.info("saleOrderCompleteTime is AFTER 31st March creating salesOrder for uwItemId : {}", uwItemId);
//                        processForSaleOrderCreation(uwOrder, returnResponseDto);
//                    }
//                    log.info("ReturnOrderServiceImpl : movementJournal api process ends for uwItemId : {}", uwItemId);
//                }
                log.info("checking for shipmentId for uwItemId : {} : {}",uwItemId, pSlipCreationMessage);
                if (pSlipCreationMessage != null && pSlipCreationMessage.contains(Constants.ReturnOrder.PackingSlip_Id_Missing)) {
                    log.info("ReturnOrderServiceImpl : processForShippingPackageIdCreation for uwItemId : {}", uwItemId);
                    boolean shipmentCreated = processForShippingPackageIdCreation(uwOrder);
                    log.info("ReturnOrderServiceImpl : shipmentCreated for uwItemId : {} : {}", uwItemId, shipmentCreated);
                }
                handlePSlipErrors(packingSlip, pSlipCreationMessage);
            }
            returnResponseDto.setPSlipCreationMessage(pSlipCreationMessage);
        } else {
            returnResponseDto.setPackingSlipResponse(packingSlipResponseBody);
            returnResponseDto.setPSlipCreationMessage(pSlipCreationMessage);
        }
        log.info("ReturnOrderServiceImpl : returnResponseDto for d365 return for uwItemId : {} : {}",uwItemId, gson.toJson(returnResponseDto));
        return returnResponseDto;
    }

    private void checkGiftCardEligibility(UwOrder uwOrder, String legalEntity) throws Exception {
        log.error("[ReturnOrderServiceImpl][checkGiftCardEligibility] uwOrder: {}. legalEntity: {}", uwOrder, legalEntity);
        GiftCardEligibilityRequestDto giftCardEligibilityRequestDto = new GiftCardEligibilityRequestDto();
        List<Integer> uwItems = new ArrayList<>();
        uwItems.add(uwOrder.getUwItemId());
        giftCardEligibilityRequestDto.setSource(Constants.GIFT_CARD.RETURN_SOURCE);
        giftCardEligibilityRequestDto.setUwItems(uwItems);
        FinanceConsumerResponseDto financeConsumerResponseDto = salesOrderService.checkIfGiftCardApplicableForUwItems(giftCardEligibilityRequestDto);
        if (financeConsumerResponseDto.getData() != null) {
            GiftCardEligibilityCheckDto giftCardEligibilityCheckDto = (GiftCardEligibilityCheckDto) financeConsumerResponseDto.getData();
            log.error("[ReturnOrderServiceImpl][checkGiftCardEligibility] Eligibility: {}. uwItemId: {}", giftCardEligibilityCheckDto.getIsEligible(), uwOrder.getUwItemId());
            if (giftCardEligibilityCheckDto.getIsEligible()) {
                if (Constants.Common.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && !Constants.Common.FOFOB2B.equalsIgnoreCase(uwOrder.getNavChannel())) {
                    uwOrder = uwOrdersRepository.findByUwItemId(uwOrder.getB2bRefrenceItemId());   //warehouse order
                }
                GiftCardSyncDto giftCardSyncDto = GiftCardSyncDto.builder().
                        shipmentId(uwOrder.getShippingPackageId()).
                        facilityCode(uwOrder.getFacilityCode()).
                        incrementId(uwOrder.getIncrementId()).
                        legalEntity(legalEntity).
                        source(Constants.GIFT_CARD.RETURN_SOURCE).
                        uwItemIds(uwItems).
                        build();

                kafkaProducerTemplate.send(giftCardTopic, uwOrder.getShippingPackageId(), ObjectHelper.writeValue(giftCardSyncDto));
                log.error("[ReturnOrderServiceImpl][checkGiftCardEligibility] Pushed to kafka, Eligibility: {}. uwItemId: {}", giftCardEligibilityCheckDto.getIsEligible(), uwOrder.getUwItemId());
            }
            return;
        }

        log.error("[ReturnOrderServiceImpl][checkGiftCardEligiblity] Could not fetch gift card eligibility. uwItemId: {}", uwOrder.getUwItemId());
        throw new Exception("Could not fetch gift card eligibility");
    }

    private void generateReturnEInvoiceRequestAndPushToKafka(SalesOrderHeader returnPayload) {
        log.info("[generateReturnEInvoiceRequestAndPushToKafka] START");
        String triggerEinvoiceFlag = systemPreferenceService.getSystemPreferenceValues(Constants.SYSTEM_PREFERENCE_KEYS.RETURN_EINVOICE_FLAG,Constants.SYSTEM_PREFERENCE_GROUPS.E_INVOICE);
        if(StringUtils.isNotBlank(triggerEinvoiceFlag) && Constants.Common.TRUE.equalsIgnoreCase(triggerEinvoiceFlag)) {
            ReturnEInvoicingKafkaRequest request = new ReturnEInvoicingKafkaRequest();
            try {
                String[] saleOrderNo = returnPayload.getSalesOrderNumber().split("_");
                Integer uwItemId = Integer.valueOf(saleOrderNo[1]);
                Integer returnId = Integer.valueOf(saleOrderNo[0]);
                UwOrder uwOrder = uwOrderRepository.findByUwItemId(uwItemId);
                ReturnDetailsDTO returnDetailsDTO = returnUtil.getReturns("RETURN_ID", String.valueOf(returnId));
                ReturnOrder returnOrder = returnUtil.getReturnOrder(returnDetailsDTO);
                if (Objects.nonNull(uwOrder) && Objects.nonNull(returnOrder)) {
                    OrdersHeader ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
                    boolean triggerReturnEInvoice = returnUtil.checkIfReturnEInvoiceRequired(returnOrder, uwOrder, ordersHeader);

                    if (triggerReturnEInvoice) {
                        log.info("[generateReturnEInvoiceRequestAndPushToKafka] uwItemId {} returnId {} facilityCode {}", uwItemId, returnId, uwOrder.getFacilityCode());
                        request.setIncrementId(Integer.valueOf(returnPayload.getWebOrderNo()));
                        request.setReturnId(returnId);
                        request.setUwItemId(uwItemId);
                        request.setReturnType(returnOrder.getReturnType());

                        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        request.setReturnCreatedAt(dateFormat.parse(returnPayload.getOrderCreationDate()));

                        returnEInvoiceService.pushToReturnEInvoiceKafkaTopic(request);
                    }
                }
            } catch (Exception e) {
                log.error("[generateReturnEInvoiceRequestAndPushToKafka] exception: {} {}", e.getMessage(), e);
            }
        }
    }

    @Override
    public ReturnResponseDto createReturnPSlipRetry(PackingSlip packingSlip, Integer uwItemId, ReturnCreateRequest returnRequestDto) {
        ResponseEntity packingSlipResponseEntity = null;
        Map packingSlipResponseBody = null;
        Boolean pSlipResponseStatus = null;
        String pSlipCreationMessage = "";
        ReturnOrderItem returnOrderItem = null;
        ReturnOrder returnOrder =  null;
        UwOrder uwOrder = null;
        ReturnPSlipResponseDto returnPSlipResponseDto = null;
        ReturnResponseDto returnResponseDto = null;

        try {
            returnResponseDto = new ReturnResponseDto();
            returnPSlipResponseDto = new ReturnPSlipResponseDto();
            uwOrder = uwOrderRepository.findByUwItemId(uwItemId);
            ReturnDetailsDTO returnDetailsDTO = returnUtil.getReturns("UW_ITEM_ID", String.valueOf(uwItemId));
            returnOrderItem = returnUtil.getReturnOrderItem(returnDetailsDTO);
            if (returnOrderItem != null) {
                returnOrder = returnUtil.getReturnOrder(returnDetailsDTO);
            }
            returnRequestDto.setUwOrder(uwOrder);
            if (returnOrder!= null) {
                returnRequestDto.setReturnId(returnOrder.getReturnId());
                returnRequestDto.setReturnOrder(returnOrder);
            }

            log.info("PSlipRetry : Processing pSlip creation for uwItemId : {}",uwItemId);
            packingSlipResponseEntity = genericClientService.forwardRequest(financeAdapterUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, new HttpHeaders(), HttpMethod.POST, packingSlip);
            log.info("PSlipRetry : packingSlipResponseEntity for uwItemId : {} : {}",uwItemId, packingSlipResponseEntity);
            packingSlipResponseBody = (Map) packingSlipResponseEntity.getBody();
            log.info("PSlipRetry : packingSlipResponseBody : {}", packingSlipResponseBody);
            if (null != packingSlipResponseBody) {
                if (packingSlipResponseBody.containsKey(Constants.Common.SUCCESS)) {
                    pSlipResponseStatus = Boolean.valueOf(String.valueOf(packingSlipResponseBody.get(Constants.Common.SUCCESS)));
                }
                pSlipCreationMessage = (String) packingSlipResponseBody.get("Message");
                returnPSlipResponseDto.setPackingSlipResponseBody(packingSlipResponseBody);
                returnPSlipResponseDto.setPSlipRetryMessage(pSlipCreationMessage);
                returnPSlipResponseDto.setPackingSlipPayload(packingSlip);
                returnResponseDto.setPackingSlipResponse(packingSlipResponseBody);
                returnResponseDto.setPSlipCreationMessage(pSlipCreationMessage);
                returnResponseDto.setPackingSlipPayload(packingSlip);
                log.info("PSlipRetry : updating retry table for uwItemId : {}",uwItemId);
                returnUtil.updateD365PSlipSyncFlagInRetry(returnOrder.getReturnId(), pSlipResponseStatus, pSlipCreationMessage, returnRequestDto);
            }
            if (!packingSlipResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
                log.error("PSlipRetry : No response received for PSlip creation against uw_item_id " + uwItemId);
                genericClientService.saveLog(String.valueOf(uwItemId)+ "_" + "PSlip", pSlipCreationMessage, packingSlip, D365BaseUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, Constants.ORDER_TYPES.RETURN_PACKING_SLIP, Constants.Common.FAILURE);
                returnPSlipResponseDto.setPackingSlipResponseBody(packingSlipResponseBody);
                returnPSlipResponseDto.setPSlipRetryMessage(pSlipCreationMessage);
                returnResponseDto.setPackingSlipResponse(packingSlipResponseBody);
                returnResponseDto.setPSlipCreationMessage(pSlipCreationMessage);
                return returnResponseDto;
            }
            if (Boolean.FALSE.equals(pSlipResponseStatus)) {
                log.info("PSlipRetry : pSlipCreationMessage for uwItemId : {} : {}",uwItemId, pSlipCreationMessage);
                log.info("PSlipRetry : checking for already exists barcode for uwItemId : {}",uwItemId);
//                if (pSlipCreationMessage != null && pSlipCreationMessage.contains(Constants.ReturnOrder.PSlip_Barcode_Already_Exist)) {
//                    log.info("PSlipRetry : ReturnOrderServiceImpl : processing for movementJournal api for uwItemId : {}", uwItemId);
//                    Date saleOrderCompleteTime = returnUtil.getSaleOrderDispatchDate(uwOrder);
//                    Date financeClosingDate = returnUtil.getFinanceClosingDate(uwItemId);
//                    boolean negativeAdjustmentFlag = returnUtil.compareDate(saleOrderCompleteTime, financeClosingDate, uwItemId);
//                    log.info("PSlipRetry : ReturnOrderServiceImpl : date compare flag for uwItemId : {} {}",uwItemId, negativeAdjustmentFlag);
//                    if (negativeAdjustmentFlag) {
//                        log.info("PSlipRetry : saleOrderCompleteTime is BEFORE 31st March Processing for negative adjustment for uwItemId : {}", uwItemId);
//                        processForNegativeAdjustment(uwOrder, packingSlip, returnResponseDto, returnOrder.getReturnId(), returnRequestDto);
//                    } else  {
//                        log.info("PSlipRetry : saleOrderCompleteTime is AFTER 31st March creating salesOrder for uwItemId : {}", uwItemId);
//                        processForSaleOrderCreation(uwOrder, returnResponseDto);
//                    }
//                    log.info("PSlipRetry : ReturnOrderServiceImpl : movementJournal api process ends for uwItemId : {}", uwItemId);
//                }
                log.info("PSlipRetry : checking for shipmentId for uwItemId : {} : {}",uwItemId, pSlipCreationMessage);
                if (pSlipCreationMessage != null && pSlipCreationMessage.contains(Constants.ReturnOrder.PackingSlip_Id_Missing)) {
                    log.info("PSlipRetry : ReturnOrderServiceImpl : processForShippingPackageIdCreation for uwItemId : {}", uwItemId);
                    boolean shipmentCreated = processForShippingPackageIdCreation(uwOrder);
                    log.info("PSlipRetry : ReturnOrderServiceImpl : shipmentCreated for uwItemId : {} : {}", uwItemId, shipmentCreated);
                }
                handlePSlipErrors(packingSlip, pSlipCreationMessage);
            }
            log.info("PSlipRetry : pSlipResponseStatus in retry for uwItemId : {} : {} : {}", uwItemId, pSlipResponseStatus, pSlipCreationMessage);
        } catch (Exception e) {
            genericClientService.saveLog(String.valueOf(uwItemId)+ "_" + "PSlip", genericClientService.getErrorMessage(e), packingSlip, D365BaseUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, Constants.ORDER_TYPES.RETURN_PACKING_SLIP, Constants.Common.FAILURE);
            log.error("PSlipRetry : exception found in pSlip creation for uwItemId : {}",uwItemId, e);
        }
        log.error("PSlipRetry : returnResponseDto in pSlip creation for uwItemId : {} : {}",uwItemId,returnResponseDto);
        return  returnResponseDto;
    }

    private boolean processForShippingPackageIdCreation(UwOrder uwOrder) {
        Integer uwItemId = uwOrder.getUwItemId();
        log.info("processForShippingPackageIdCreation for uwItemId : {}", uwItemId);
        List<Integer> uwItemIds = null;
        boolean shipmentCreated = false;
        try {
            uwItemIds = new ArrayList<>();
            uwItemIds.add(uwItemId);
            shipmentCreated = salesOrderService.shipmentIdRepopulate(uwItemIds);
        } catch (Exception e) {
            log.error("processForShippingPackageIdCreation : exception found to create shipment for uwItemId : {} : ",uwItemId, e);
        }
        log.info("shipmentCreated for uwItemId : {} : {}", uwItemId, shipmentCreated);
        return shipmentCreated;
    }

    private void validateReturnCreation(Integer uwItemId, SalesOrderHeader returnPayload, Boolean status, String returnCreationMessage, ReturnResponseDto returnResponseDto) {
        log.info("[ReturnOrderServiceImpl] validating the returnMessage for uwItemId : {}", uwItemId);
        if (returnCreationMessage.contains(Constants.ReturnOrder.INVALID_SALESCHANNEL)) {
            log.info("invalid salesChannel found for uwItemId : {}", uwItemId);
            boolean salesChannelCreated;
            String salesChannel = returnPayload.getSalesChannel();
            salesChannelCreated = returnUtil.createSalesChannelAtPos(salesChannel, uwItemId);
            log.info("salesChannelCreated at POS end for uwItemId : {} {}", uwItemId, salesChannelCreated);
        } else if (returnCreationMessage.contains("does not exist at line number")
                || returnCreationMessage.contains("missing in company")) {
            Integer pid = Integer.parseInt(returnPayload.getSoLines().get(0).getItemNumber());
            String itemDoesNotExist = "";
            ResponseEntity response = null;
            log.info("[ReturnOrderServiceImpl] item does not exists found for uwItemId for pid : {} : {}", uwItemId, pid);
            response = returnUtil.createItemForD365(returnPayload, uwItemId, pid, returnResponseDto);
            log.info("[ReturnOrderServiceImpl] response received from item-master api for uwItemId : {} : {} : {}", uwItemId, pid, response);

        } else {
            log.info("returnCreation valid message for uwItemId : {} : {}", uwItemId, returnCreationMessage);
        }
    }

    private void processForNegativeAdjustment(UwOrder uwOrder, PackingSlip packingSlip, ReturnResponseDto returnResponseDto, Integer returnId, ReturnCreateRequest returnRequestDto) {
        ResponseEntity response = null;
        Map movJournalResponseBody = null;
        Integer uwItemId = uwOrder.getUwItemId();
        boolean status = false;
        try {
            log.info("ReturnOrderServiceImpl : negative adjustment for packingSlip for uwItemId : {}",uwItemId);
            response = d365ReturnService.doNegativeAdjustment(packingSlip, uwOrder,returnRequestDto.isInventoryClearanceFlow());
            log.info("ReturnOrderServiceImpl : response from movement journal api for uwItemId : {} : {}",uwItemId,response);
            if (response != null && response.getBody() != null) {
                movJournalResponseBody = (Map) response.getBody();
                log.info("ReturnOrderServiceImpl : movJournal movJournalResponseBody for uwItemId : {} : {}",uwItemId, movJournalResponseBody);
                if (movJournalResponseBody.containsKey(Constants.Common.SUCCESS)) {
                    status = Boolean.valueOf(String.valueOf(movJournalResponseBody.get(Constants.Common.SUCCESS)));
                }
                log.info("ReturnOrderServiceImpl : movJournal creation flag for uwItemId : {} : {}",uwItemId, status);
                returnResponseDto.setMovJournalResponse(movJournalResponseBody);
                returnResponseDto.setMovJournalCreationMessage((String) movJournalResponseBody.get("Message"));
                returnResponseDto.setMovJournalType("Negative Adjustment");
                // update the d365_return_tracking table with movJournal flag and movJournalType
                returnUtil.updateD365MovJournalSyncFlag(returnId, status, uwItemId, returnRequestDto);
            }
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found during negative adjustment for uwItemId : {}",uwItemId, e);
        }
    }

    private void processForSaleOrderCreation(UwOrder uwOrder, ReturnResponseDto returnResponseDto) {
        Integer uwItemId = uwOrder.getUwItemId();
        log.info("ReturnOrderServiceImpl : creating salesOrder for already exists barcode and uwItemId : {}", uwItemId);
        List<Integer> uwItemIds = new ArrayList<>();
        List<String> shippingPackageIds = new ArrayList<>();
        uwItemIds.add(uwItemId);
        shippingPackageIds.add(uwOrder.getShippingPackageId());

        // Processing for SalesOrder creation
        SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO = new SaleOrderPackingSlipResponseDTO();
        String shipmentId = null;
        try {
            shipmentId = String.valueOf(uwOrdersService.getUwOrderByUwItemIdForShipmentId(uwItemIds.get(0)).getShippingPackageId());
            log.info("ReturnOrderServiceImpl : creating sale order in forward flow for uwItemId shippingPackageIds : {} : {}", uwItemId, shippingPackageIds);
            saleOrderPackingSlipResponseDTO = salesOrderService.createSalesOrder(shipmentId, uwItemIds, Constants.SalesOrder.CREATE_PROD_URL, null);
            returnResponseDto.setSaleOrderPackingSlipResponseDTO(saleOrderPackingSlipResponseDTO);
            String salesCreationStatus = validateSalesCreationStatus(saleOrderPackingSlipResponseDTO, shippingPackageIds, uwItemId, returnResponseDto);
            log.info("ReturnOrderServiceImpl : salesCreationStatus in forward flow for uwItemId : {} : {}", uwItemId, salesCreationStatus);
            if (StringUtils.isEmpty(salesCreationStatus) || salesCreationStatus.equalsIgnoreCase(Constants.Common.FAILURE)) {
                log.info("ReturnOrderServiceImpl : sales creation failed in forward flow pushing data to kafka for uwItemId : {} : {}", uwItemId, shippingPackageIds);
                salesOrderService.pushShippingPackageIdsInSaleOrderKafkaProd(shippingPackageIds);
            }
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to creating salesOrder in forward flow for uwItemId : {} : ",uwItemId, e);
        }
        log.info("ReturnOrderServiceImpl : saleOrderPackingSlipResponseDTO in forward flow for uwItemId : {} : {}", uwItemId, gson.toJson(saleOrderPackingSlipResponseDTO));
    }

    private String validateSalesCreationStatus(SaleOrderPackingSlipResponseDTO saleOrderPackingSlipResponseDTO, List<String> shippingPackageIds, Integer uwItemId, ReturnResponseDto returnResponseDto) {
        log.info("validateSalesCreationStatus : validating salesCreationStatus for uwItemId shippingPackageIds : {} : {}", uwItemId, shippingPackageIds);
        ResponseEntity salesResponseEntity = null;
        Map salesResponseBody = null;
        String message = "";
        boolean responseStatus = false;
        String status = "";
        if (saleOrderPackingSlipResponseDTO != null) {
            salesResponseEntity = saleOrderPackingSlipResponseDTO.getSaleOrderResponse();
        }
        log.info("validateSalesCreationStatus : salesResponseEntity for uwItemId : {} : {}", uwItemId, salesResponseEntity);
        if (salesResponseEntity != null) {
            salesResponseBody = (Map) salesResponseEntity.getBody();
        }
        log.info("validateSalesCreationStatus : salesResponseBody for uwItemId : {} : {}", uwItemId, salesResponseBody);
        if (salesResponseBody != null) {
            message = (String) salesResponseBody.get("Message");
            responseStatus = Boolean.valueOf(String.valueOf(salesResponseBody.get("Success")));
        }
        log.info("validateSalesCreationStatus : sales creation responseStatus for uwItemId : {} : {}", uwItemId, responseStatus);
        if (responseStatus || StringUtils.containsIgnoreCase(message, "already exist")) {
            status = Constants.Common.SUCCESS;
        } else {
            status = Constants.Common.FAILURE;
        }
        log.info("validateSalesCreationStatus : sales creation status for uwItemId : {} : {}", uwItemId, status);
        return  status;
    }


    private void handlePSlipErrors(PackingSlip packingSlip, String pSlipCreationMessage) {
        try {
            Boolean pSlipSyncBarcodeFlag = returnUtil.getPSlipSyncBarcodeFlag();
            log.info("[handlePSlipErrors] pSlipSyncBarcodeFlag: {}",pSlipSyncBarcodeFlag);
            if(pSlipSyncBarcodeFlag && null!=packingSlip && !packingSlip.getSalesLineList().isEmpty() && null!=packingSlip.getSalesLineList().get(0).getSerialId()) {
                String serialNoExistsError = new StringBuilder("Serial Number ").append(packingSlip.getSalesLineList().get(0).getSerialId()).append(" already exists physically in the inventory..Operation has been cancelled..").toString();
                log.info("[handlePSlipErrors] serialNoExistsError string: {}", serialNoExistsError);
                if (pSlipCreationMessage != null && pSlipCreationMessage.equalsIgnoreCase(serialNoExistsError) && null!=packingSlip.getPackingSlipId()) {
                    List<String> shippingPackageId = new ArrayList<>();
                    shippingPackageId.add(packingSlip.getPackingSlipId());
                    log.info("[handlePSlipErrors] calling pushShippingPackageIdsInPackingSlipKafka for shippingPackageId: {}",packingSlip.getPackingSlipId());
                    salesOrderService.pushShippingPackageIdsInPackingSlipKafka(shippingPackageId);
                }
            }
        }
        catch (Exception e){
            log.error("exception while calling pushShippingPackageIdsInPackingSlipKafka {} {}",e.getMessage(),e);
        }
    }

    private void handleReturnSOErrors(SalesOrderHeader returnPayload, String returnCreationMessage) {
        try {
            if (null != returnPayload && null != returnCreationMessage) {
                String saleOrderNo = returnPayload.getSalesOrderNumber();
                String pid = returnPayload.getSoLines().get(0).getItemNumber();
                Long uwItemId = returnPayload.getSoLines().get(0).getLineNumber();
                String itemDoesNotExistError = new StringBuilder("Unable to create sales order ").append(saleOrderNo).append(", since the item ").append(pid).append(" does not exist at line number ").append(uwItemId).toString();
                if (null != itemDoesNotExistError && itemDoesNotExistError.equalsIgnoreCase(returnCreationMessage)) {
                    Boolean callProductSyncFlag = returnUtil.getTriggerProductSyncFlag();
                    if(callProductSyncFlag) {
                        log.info("[handleReturnSOErrors] calling product sync");
                        itemMasterService.pushItemToKafka(Integer.parseInt(pid));
                    }
                }
            }
        }
        catch (Exception e){
            log.error("[handleReturnSOErrors] exception {} {}",e.getMessage(),e);
        }
    }

    /**
     * @param returnRequestDto
     * @return salesOrderHeaderPayload
     */
    @Transactional(value = "inventoryTransactionManager" ,isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    public SalesOrderHeader generatePayloadForReturn(SalesOrderHeader returnPayload, ReturnCreateRequest returnRequestDto) throws Exception {
        log.info("Generating payload for uwItemId : " + returnRequestDto.getUwItemId());
        ReturnOrderAddressUpdate returnOrderAddressUpdate = null;
        ShippingStatus shippingStatus = null;
        com.lenskart.orderops.model.ReturnOrderItem returnOrderItem = null;

        UwOrder uwOrder = null;
        ReturnOrder returnOrder = null;
        Order order = null;
        OrdersHeader ordersHeader = null;
        Integer uwItemId = returnRequestDto.getUwItemId();
        Integer returnId = returnRequestDto.getReturnId();
        SalesOrderHeader salesOrderHeader = returnPayload;
        OrderAddressUpdate orderAddressUpdate = null;

        if (null == returnPayload) {
            salesOrderHeader = new SalesOrderHeader();
        }
        ArrayList<SoLine> soLines = new ArrayList<SoLine>();
        ArrayList<MiscChargesLine> miscChargesLines = new ArrayList<MiscChargesLine>();
        ArrayList<MiscChargesHeader> miscChargesHeader = new ArrayList<MiscChargesHeader>();
        try {
            log.info("fetching details from returnRequestDto for uwItemId : {}",uwItemId);
            try {
                if (null == returnRequestDto.getReturnOrderItem()) {
                    log.info("[ReturnOrderServiceImpl] return_order_item not found in returnRequest, so fetching from DB");
                    returnOrderItem = returnOrderItemService.findByUwItemId(uwItemId);
                    if (null != returnOrderItem) {
                        returnId = returnOrderItem.getReturnId();
                        returnRequestDto.setReturnOrderItem(returnOrderItem);
                    }
                    if (null == returnId && null == returnOrderItem) {
                        throw new Exception("No entry found in returnOrderItem table for uw_item_id " + uwItemId);
                    }
                } else {
                    log.info("[ReturnOrderServiceImpl] Fetching ReturnOrderItem from requestDto");
                    returnOrderItem = returnRequestDto.getReturnOrderItem();
                    returnId = returnOrderItem.getReturnId();
                    log.info("[ReturnOrderServiceImpl] returnOrderItem found in requestDto : " + returnOrderItem);
                }
                log.info("[ReturnOrderServiceImpl] returnId : {}", returnId);
                if (null == returnRequestDto.getReturnOrder()) {
                    log.info("[ReturnOrderServiceImpl] Null return_order in returnRequest, fetching from DB");
                    ReturnDetailsDTO returnDetailsDTO = returnUtil.getReturns("RETURN_ID", String.valueOf(returnId));
                    returnOrder = returnUtil.getReturnOrder(returnDetailsDTO);
                    if (null == returnOrder) {
                        log.error("No entry in found in return_order table for returnId " + returnId);
                    } else {
                        returnRequestDto.setReturnOrder(returnOrder);
                        returnRequestDto.setReturnId(returnOrder.getReturnId());
                    }

                } else {
                    log.info("[ReturnOrderServiceImpl] Fetching returnOrder from requestDto : " + returnOrderItem);
                    returnOrder = returnRequestDto.getReturnOrder();
                    log.info("[ReturnOrderServiceImpl] returnOrder found in requestDto : " + returnOrder);
                }
                if (null == returnRequestDto.getUwOrder()) {
                    log.info("[ReturnOrderServiceImpl] Null uwOrder in returnRequest, fetching from DB");
                    uwOrder = uwOrdersService.getUwOrderByUwItemId(uwItemId);
                    if (null == uwOrder) {
                        log.error("[ReturnOrderServiceImpl] no entry found in uw_orders for uw_item_id " + uwItemId);
                    } else {
                        returnRequestDto.setUwOrder(uwOrder);
                    }
                } else {
                    log.info("[ReturnOrderServiceImpl] Fetching uwOrder from requestDto");
                    uwOrder = returnRequestDto.getUwOrder();
                    log.info("[ReturnOrderServiceImpl] uwOrder found in requestDto : " + returnOrder);
                }
                if (uwOrder != null) {
                    log.info("[ReturnOrderServiceImpl] Fetching orders for uwItemId : {}",uwItemId);
                    order = orderService.findOrderByItemId(uwOrder.getItemId());
                }

                //update uwOrders for sbrt
                returnUtil.setSbrtUwOrderDetails(uwOrder, returnRequestDto.isSBRTEnabled());

            } catch (Exception e) {
                log.error("exception found to get details from returnRequestDto for uwItemId : {} : ",uwItemId,e);
                throw e;
            }

            if(Objects.nonNull(uwOrder) && StringUtils.isNotBlank(uwOrder.getProductDeliveryType()) && Constants.Common.B2B.equalsIgnoreCase(uwOrder.getProductDeliveryType()) && returnUtil.isB2BPhysicalItem(uwOrder)){
                log.info("[generatePayloadForReturn] not triggering d365 return for physical item uwItemId: "+uwOrder.getUwItemId());
                returnUtil.updateReturnD365SyncFlag(returnId,true,Constants.ReturnOrder.LK_ITEM_RETURN,null,returnRequestDto);
                return null;
            }
            try {
                log.info("fetching returnOrderAddressUpdate for uwItemId : {}",uwItemId);
                if (null != returnOrder) {
                    log.info("fetching returnOrderAddressUpdate for " + returnOrder.getReturnId());
                    returnOrderAddressUpdate = returnUtil.getReturnOrderAddress(returnOrder.getOrderNo());
                    if (returnOrderAddressUpdate == null) {
                        log.info("fetching orderAddressUpdate for " + returnOrder.getReturnId());
                        if (order != null) {
                            orderAddressUpdate = orderAddressUpdateService.getOrderAddressUpdateShipping(order.getOrderId());
                        }
                    }
                } else {
                    log.error("return_order found null for uwItemId : {}",uwItemId);
                }
            } catch (Exception e) {
                log.error("exception found to get returnAddress for uwItemId : {} : ",uwItemId,e);
                throw e;
            }

            if (uwOrder != null) {
                shippingStatus = shippingStatusService.getShippingStatusByUnicomCode(String.valueOf(uwOrder.getUnicomOrderCode()));
            }
            if (order != null) {
                ordersHeader = orderHeaderService.getOrderHeader(order.getIncrementId());
            }

            ProductUtil.setCountryMapping();
            ProductUtil.setStateMapping();
            String salesOrderNumber = returnId + "_" + uwItemId;
            log.info("[ReturnOrderServiceImpl][createReturn] salesOrderNumber : " + salesOrderNumber);
            String rmaNumber = returnId + "_" + uwOrder.getIncrementId();
            log.info("[ReturnOrderServiceImpl][createReturn] rmaNumber : " + rmaNumber);
            if (returnOrder != null && null != returnOrder.getReturnMethod() && returnOrder.getReturnMethod().equalsIgnoreCase(Constants.ReturnOrder.RETURN_METHOD)) {
                setReturnSpecificDetailsToReturnSales(returnOrderAddressUpdate, order, ordersHeader, salesOrderHeader);
            } else {
                setReturnSpecificDetailsToReturnSales1(orderAddressUpdate, order, ordersHeader, salesOrderHeader);
            }
            if (null == returnPayload) {
                addReturnSalesDetails(shippingStatus, uwOrder, order, ordersHeader, uwItemId, salesOrderHeader, soLines, miscChargesLines, miscChargesHeader, salesOrderNumber, rmaNumber, returnOrder, returnRequestDto);
            }
        } catch (Exception e) {
            log.error("Exception found to create returnOrder payload for uwItemId : {} : ",returnRequestDto.getUwItemId(), e);
            throw e;
        }
        log.info("salesOrderHeader generated for uwItemId : " + returnRequestDto.getUwItemId());
        return salesOrderHeader;
    }

    private void setReturnSpecificDetailsToReturnSales(ReturnOrderAddressUpdate returnOrderAddressUpdate, Order order, OrdersHeader ordersHeader, SalesOrderHeader salesOrderHeader) {
        log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales] inside setReturnSpecificDetailsToReturnSales");
        try {
            if (returnOrderAddressUpdate == null) {
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales] returnOrderAddressUpdate found NULL, adding blank values in address");
                prepareBlankDataForReturnPayload(salesOrderHeader);
            } else {
                salesOrderHeader.setFirstName(StringUtils.defaultString(returnOrderAddressUpdate.getFirstName()));
                salesOrderHeader.setMiddleName(Constants.ReturnOrder.BLANK);
                salesOrderHeader.setLastName(StringUtils.defaultString(returnOrderAddressUpdate.getLastName()));
                salesOrderHeader.setExportReason(saleOrderUtil.getTaxCheck(returnOrderAddressUpdate.getCountry(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setIsWithTax(saleOrderUtil.getTaxCheck(returnOrderAddressUpdate.getCountry(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setDeliveryName(StringUtils.defaultString(saleOrderUtil.getDeliveryNameForReturn(order, returnOrderAddressUpdate)));
                saleOrderUtil.setAddressForReturn(salesOrderHeader, order, returnOrderAddressUpdate);
                salesOrderHeader.setContactPersonId(StringUtils.defaultString(returnOrderAddressUpdate.getEmail()));
                salesOrderHeader.setEmail(StringUtils.defaultString(returnOrderAddressUpdate.getEmail()));
                salesOrderHeader.setPhone(StringUtils.defaultString(returnOrderAddressUpdate.getTelephone()));
                salesOrderHeader.setOrderType(Constants.ReturnOrder.OrderType);
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales] exit from setReturnSpecificDetailsToReturnSales");
            }
        } catch (Exception e) {
            log.error("setReturnSpecificDetailsToReturnSales : exception found : ",e);
            throw e;
        }
    }

    private void setReturnSpecificDetailsToReturnSales1(OrderAddressUpdate orderAddressUpdate, Order order, OrdersHeader ordersHeader, SalesOrderHeader salesOrderHeader) {
        try {
            if (orderAddressUpdate == null) {
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales1] orderAddressUpdate found NULL, adding blank values in address");
                prepareBlankDataForReturnPayload(salesOrderHeader);
            } else {
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales1] ordersHeader : " + ordersHeader.toString());
                salesOrderHeader.setFirstName(StringUtils.defaultString(orderAddressUpdate.getFirstName()));
                salesOrderHeader.setMiddleName(Constants.ReturnOrder.BLANK);
                salesOrderHeader.setLastName(StringUtils.defaultString(orderAddressUpdate.getLastName()));
                salesOrderHeader.setExportReason(saleOrderUtil.getTaxCheck(orderAddressUpdate.getCountryId(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setIsWithTax(saleOrderUtil.getTaxCheck(orderAddressUpdate.getCountryId(), ordersHeader.getLegalOwnerCountry()));
                salesOrderHeader.setDeliveryName(StringUtils.defaultString(saleOrderUtil.getDeliveryName(order, orderAddressUpdate)));
                saleOrderUtil.setAddress(salesOrderHeader, order, orderAddressUpdate);
                salesOrderHeader.setContactPersonId(StringUtils.defaultString(orderAddressUpdate.getEmail()));
                salesOrderHeader.setEmail(StringUtils.defaultString(orderAddressUpdate.getEmail()));
                salesOrderHeader.setPhone(StringUtils.defaultString(orderAddressUpdate.getTelephone()));
                salesOrderHeader.setOrderType(Constants.ReturnOrder.OrderType);
                log.info("[ReturnOrderServiceImpl][setReturnSpecificDetailsToReturnSales1] exit from setReturnSpecificDetailsToReturnSales");
            }
        } catch (Exception e) {
            log.info("setReturnSpecificDetailsToReturnSales1 : exception found : ",e);
            throw e;
        }

    }

    private void prepareBlankDataForReturnPayload(SalesOrderHeader salesOrderHeader) {
        log.info("[ReturnOrderServiceImpl][prepareBlankDataForReturnPayload] adding blank values for address");
        salesOrderHeader.setFirstName(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setMiddleName(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setLastName(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setExportReason(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setIsWithTax(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setDeliveryName(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setAddressStreet(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setAddressZipCode(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setAddressCountryRegionId(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setAddressState(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setAddressCity(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setContactPersonId(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setEmail(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setPhone(Constants.ReturnOrder.BLANK);
        salesOrderHeader.setOrderType(Constants.ReturnOrder.OrderType);
        log.info("[ReturnOrderServiceImpl][prepareBlankDataForReturnPayload] added blank values for address");
    }

    private void addReturnSalesDetails(ShippingStatus shippingStatus, UwOrder uwOrder, Order order, OrdersHeader ordersHeader, Integer uwItemId, SalesOrderHeader salesOrderHeader, List<SoLine> soLines, ArrayList<MiscChargesLine> miscChargesLines, ArrayList<MiscChargesHeader> miscChargesHeader, String salesOrderNumber, String rmaNumber, ReturnOrder returnOrder, ReturnCreateRequest returnRequestDto) throws Exception {
        log.info("[ReturnOrderServiceImpl][addReturnSalesDetails] inside addReturnSalesDetails");
        int classificationId = 0;
        String classificationDisplayName = "";
        SoLine soLine;
        Product product = null;
        OrderItemGSTDetail orderItemGSTDetail = null;
        soLine = new SoLine();
        String salesChannel = "";
        String subChannel = "";
        UwOrder uwOrderBulk = null;
        Boolean isOTC = false;
        String inventLocationIdForReturn = "";
        String fulfillmentWh = "";


        try {
            product = productService.getProduct(uwOrder.getProductId());
            classificationId = product.getClassification();
            orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwItemId);
            classificationDisplayName = classificationService.getClassification(classificationId).getDisplayName();
            salesChannel = returnUtil.getSalesChannel(uwOrder, ordersHeader, returnOrder);
            subChannel = saleOrderUtil.getSubChannel(uwOrder, order);

            log.info("ReturnOrderServiceImpl : preparing salesOrderHeader for : {}",uwItemId);
            salesOrderHeader.setIsUpdateSalesOrder(0);
            salesOrderHeader.setSalesOrderNumber(StringUtils.defaultString(salesOrderNumber));
            salesOrderHeader.setRmaNumber(StringUtils.defaultString(rmaNumber));

            isOTC = Constants.Common.OTC.equalsIgnoreCase(uwOrder.getProductDeliveryType());
            if(isOTC){
                inventLocationIdForReturn = salesChannel;
            }else{
                inventLocationIdForReturn = returnUtil.getInventLocationIdForReturn(uwOrder, ordersHeader);
                fulfillmentWh = returnUtil.getFulfillmentWH(uwOrder);
            }
            if(returnRequestDto.isInventoryClearanceFlow()){
                inventLocationIdForReturn = Constants.FacilityCodes.DK03;
                if(StringUtils.isNotBlank(fulfillmentWh) && fulfillmentWh.contains(Constants.ReturnOrder.Intransit)){
                    fulfillmentWh = fulfillmentWh.replace(Constants.ReturnOrder.Intransit,"");
                }
            }
            salesOrderHeader.setInventLocationId(inventLocationIdForReturn);
            returnUtil.setLegalEntity(uwOrder, salesOrderHeader, ordersHeader, returnRequestDto.isSBRTEnabled());
            returnUtil.setModeOfPayment(order, salesOrderHeader, uwOrder);
            salesOrderHeader.setMethodOfPayment("Prepaid");
            //returnUtil.setMethodOfPayment(ordersHeader, subChannel, salesOrderHeader, uwOrder, order);
            returnUtil.setCourierCode(uwOrder, ordersHeader, returnOrder, salesOrderHeader);
            returnUtil.setRefInvoices(uwOrder, salesOrderHeader);
            salesOrderHeader.setSalesChannel(salesChannel);
            salesOrderHeader.setCustomerGroup(Constants.Customer.GROUP);
            salesOrderHeader.setGender(Constants.Customer.GENDER);
            salesOrderHeader.setStores(returnUtil.getReturnStoreCode(ordersHeader, uwOrder));
            salesOrderHeader.setSubChannel(StringUtils.defaultString(subChannel));
            salesOrderHeader.setCurrencyCode(ProductUtil.CountryCurrencyMapping.get(ordersHeader.getLkCountry()));
            salesOrderHeader.setModeOfDelivery(Constants.SalesOrder.ModeOfDelivery);
            salesOrderHeader.setWebOrderNo(String.valueOf(order.getIncrementId()));
            salesOrderHeader.setTCSGroup(Constants.ReturnOrder.BLANK);
            salesOrderHeader.setTDSGroup(Constants.ReturnOrder.BLANK);
            //return creation date_time
            if (returnOrder != null) {
                salesOrderHeader.setOrderCreationDate(dateFormat.format(returnOrder.getReturnCreateDatetime()));
            }
            salesOrderHeader.setFulfillmentWH(fulfillmentWh);
            salesOrderHeader.setIsIntegrated(1);
            returnUtil.setCustomerDetails(salesOrderHeader, ordersHeader, order, uwOrder);
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to prepare salesOrderHeader for : {} : ",uwItemId, e);
            throw e;
        }

        try {
            //soLines payload preparation
            List<Integer> uwItemIdList = new ArrayList<>();
            uwItemIdList.add(uwItemId);
            List<Integer> lensUwItemIds = uwOrderRepository.findUwItemIdByParentUwItemId(uwItemId);
            uwItemIdList.addAll(lensUwItemIds);
            log.info("ReturnOrderServiceImpl : uwItemIdList for soLines : {} : {}",uwItemIdList, uwItemId);
            UwOrder uwOrder1 = null;

            if (!CollectionUtils.isEmpty(uwItemIdList)) {
                for (Integer uwItemIdNew:uwItemIdList
                     ) {
                    uwOrder1 = uwOrderRepository.findByUwItemId(uwItemIdNew);
                    UwOrder preSbrtUwOrder = returnUtil.cloneUwOrders(uwOrder1);
                    returnUtil.setSbrtUwOrderDetails(uwOrder1, returnRequestDto.isSBRTEnabled());
                    log.info("preparing soLine for uwItemId : {}",uwItemIdNew);
                    soLine = prepareSoLinesPayload(uwOrder1, preSbrtUwOrder, order, ordersHeader, returnOrder, returnRequestDto, salesOrderHeader);
                    log.info("soLine prepared for uwItemId : {} : {}",uwItemIdNew, gson.toJson(soLine));
                    soLines.add(soLine);
                    log.info("final soLines prepared for uwItemId : {} : {}",uwItemIdNew, gson.toJson(soLines));
                }

            }

        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to prepare soLine for : {} : ",uwItemId,e);
            throw e;
        }
        salesOrderHeader.setSoLines(soLines);
        salesOrderHeader.setMiscChargesLines(miscChargesLines);
        salesOrderHeader.setMiscChargesHeader(miscChargesHeader);
        log.info("[ReturnOrderServiceImpl] salesOrderHeader prepared for uwItemId : {} : {}", uwItemId, gson.toJson(salesOrderHeader));
    }

    private SoLine prepareSoLinesPayload(UwOrder uwOrder1, UwOrder preSbrtUwOrder, Order order, OrdersHeader ordersHeader, ReturnOrder returnOrder, ReturnCreateRequest returnRequestDto, SalesOrderHeader salesOrderHeader) throws Exception {
        Integer uwItemId = uwOrder1.getUwItemId();
        SoLine soLine = null;
        Product product = null;
        Integer classificationId = 0;
        OrderItemGSTDetail orderItemGSTDetail = null;
        String classificationDisplayName = "";
        String salesChannel = "";
        String subChannel = "";
        log.info("ReturnOrderServiceImpl : preparing soLines for uwItemId : {}",uwItemId);
        try {
            soLine = new SoLine();
            log.info("soLine uwOrder : {}",uwOrder1);
            product = productService.getProduct(uwOrder1.getProductId());
            log.info("soLine product : {}",product);
            if (product != null) {
                classificationId = product.getClassification();
            }
            //orderItemGSTDetail = orderItemGstDetailsService.getOrderItemGSTDetail(uwItemId);
            classificationDisplayName = classificationService.getClassification(classificationId).getDisplayName();
            log.info("soLine classificationDisplayName : {}, {}",uwItemId, classificationDisplayName);
            salesChannel = returnUtil.getSalesChannel(uwOrder1, ordersHeader, returnOrder);
            subChannel = saleOrderUtil.getSubChannel(uwOrder1, order);
            log.info("soLine salesChannel subChannel : {}, {}, {}",uwItemId, salesChannel, subChannel);
            returnUtil.setSoLineBasicDetails(uwOrder1, order, soLine);
            returnUtil.setSoLinePrices(preSbrtUwOrder, soLine);
            soLine.setInventLocationId(salesOrderHeader.getInventLocationId());
            soLine.setStores(returnUtil.getReturnStoreCode(ordersHeader, uwOrder1));
            returnUtil.setSoLineDiscount(uwOrder1, soLine);
            returnUtil.setSoLineConfirmedReceiptDate(returnOrder, uwOrder1, soLine);
            soLine.setInventSiteId(Constants.ReturnOrder.BLANK);
            soLine.setBrand(product.getBrand().trim());
            soLine.setSubChannel(subChannel);
            log.info("ReturnOrderServiceImpl : step1 completed for uwItemId : {}",uwItemId);
            if (uwOrder1.getProductDeliveryType().equals(Constants.Common.B2B) && !(uwOrder1.getNavChannel().equals(Constants.Common.FOFOB2B))
                    && !(intCountries.contains(ordersHeader.getLkCountry()))) {
                soLine.setDeliveryType(StringUtils.defaultString(Constants.Common.DIRECT_DELIVERY));
            } else {
                soLine.setDeliveryType(Constants.Common.STOCK);
            }
            if (order.getProductDeliveryType().equals(Constants.Common.B2B) && !uwOrder1.getNavChannel().equals(Constants.Common.FOFOB2B)
                    && !(intCountries.contains(ordersHeader.getLkCountry()))) {
                soLine.setSourcingVendor(Constants.ReturnOrder.SOURCING_VENDOR);
            } else {
                soLine.setSourcingVendor(Constants.ReturnOrder.BLANK);
            }
            soLine.setSalesChannel(salesChannel);
            soLine.setMagentoItemId(order.getMagentoItemId());
            soLine.setItemClassification(classificationDisplayName);
            returnUtil.setSoLineHsnAndSacCode(product, soLine, classificationId, uwOrder1);
            returnUtil.setSoLineTax(uwOrder1, soLine);
            returnUtil.setSoLinePurchPrice(uwOrder1, soLine);
            soLine.setItemTemplateName(Constants.ReturnOrder.BLANK);
            soLine.setDeliveryModeCode(Constants.ReturnOrder.BLANK);
            List<Integer> SBRTUwItemIds =null;
            if(isSBRTFlowEnabledForSalesOrder){
                SBRTUwItemIds = sbrtOrderItemService.fetchUwItemIdsFromSBRTOrderItem(Collections.singletonList(uwItemId));
            }
            setSBRTFlagForLineItem(uwOrder1, SBRTUwItemIds, soLine);
            soLine.setCostCenter(Constants.ReturnOrder.BLANK);
            soLine.setPartnerType(Constants.ReturnOrder.BLANK);
            soLine.setEmployee(Constants.ReturnOrder.BLANK);
            soLine.setLkReferenceWh(returnUtil.getLkReferenceWhForReturn(uwOrder1,ordersHeader, returnOrder));
            soLine.setBarcode(uwOrder1.getBarcode());
            String lkPurchPrice = "";
            if (uwOrder1.getProductDeliveryType().equals(Constants.Common.B2B)) {
                UwOrder b2bUwOrder = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder1.getB2bRefrenceItemId());
                if (b2bUwOrder != null) {
                    lkPurchPrice = returnUtil.getUnitPrice(b2bUwOrder);
                    if(!StringUtils.isEmpty(lkPurchPrice)) {
                        soLine.setLkPurchasePrice(Double.valueOf(lkPurchPrice));
                    }
                }
            }

            log.info("ReturnOrderServiceImpl : soLines prepared for uwItemId : {} : {}",uwItemId, gson.toJson(soLine));
        } catch (Exception e) {
            log.error("ReturnOrderServiceImpl : exception found to prepare soLine : {}",uwItemId, e);
            throw e;
        }
        return soLine;
    }

    public PackingSlip generatePackingSlipPayload(SalesOrderHeader returnPayload, ReturnCreateRequest
            returnRequestDto) throws Exception {
        PackingSlip packingSlip = new PackingSlip();
        SalesLineList salesLineList = new SalesLineList();
        ArrayList<SalesLineList> salesLineLists = new ArrayList<SalesLineList>();

        try {
            log.info("preparing packingSlip payload : {}", packingSlip);
            UwOrder uwOrder = uwOrdersService.getUwOrderByUwItemId(returnRequestDto.getUwItemId());
            OrdersHeader ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
            packingSlip.setSalesId(returnRequestDto.getReturnId() + "_" + returnRequestDto.getUwItemId());
            packingSlip.setPackingSlipId(returnUtil.getShippingPackageId(uwOrder));
            //packingSlip.setPackingSlipId(String.valueOf(uwOrder.getShippingPackageId()));
            packingSlip.setLegalEntity(returnUtil.getPSlipLegalEntity(uwOrder, ordersHeader, returnPayload, packingSlip, returnRequestDto.isSBRTEnabled()));
            //returnUtil.setPSlipLegalEntity(uwOrder, ordersHeader, returnPayload, packingSlip);
            returnUtil.setPSlipDate(returnRequestDto, uwOrder, packingSlip);
            returnUtil.setPSlipTrackingNo(returnRequestDto, uwOrder, packingSlip);
            returnUtil.setPSlipSalesInvoiceNo(uwOrder, packingSlip);
            packingSlip.setDescription(String.valueOf(uwOrder.getIncrementId()));

            //SalesLine
            List<Integer> uwItemIdList = new ArrayList<>();
            uwItemIdList.add(uwOrder.getUwItemId());
            List<Integer> lensUwItemIds = uwOrderRepository.findUwItemIdByParentUwItemId(uwOrder.getUwItemId());
            uwItemIdList.addAll(lensUwItemIds);
            UwOrder uwOrder1 = null;
            boolean  isFWPackingSlipSuccessFul = isFWPackingslipSyncSuccessFul(uwOrder);
            log.info("Forward packing slip is : {} for returnID : {}",isFWPackingSlipSuccessFul,returnRequestDto.getReturnId());

            if (!CollectionUtils.isEmpty(uwItemIdList)) {
                for (Integer uwItemIdNew:uwItemIdList
                ) {
                    uwOrder1 = uwOrderRepository.findByUwItemId(uwItemIdNew);
                    salesLineList = prepareSoLinesForPSlip(uwOrder1, returnRequestDto,isFWPackingSlipSuccessFul);
                    salesLineLists.add(salesLineList);
                }
            }
            packingSlip.setSalesLineList(salesLineLists);
            String packSlipJson = new ObjectMapper().writeValueAsString(packingSlip);
            log.info("packingSlip payload prepared {} ", packSlipJson);
            return packingSlip;
        } catch (Exception e) {
            log.error("[ReturnOrderServiceImpl][createReturn] Exception found to create packingSlip in consumer : " + e.getMessage());
            genericClientService.saveLog(String.valueOf(returnRequestDto.getUwItemId())+ "_" + "PSlip", genericClientService.getErrorMessage(e), packingSlip, D365BaseUrl + Constants.ReturnOrder.RETURN_PACKAGING_SLIP_URL, Constants.ORDER_TYPES.RETURN_PACKING_SLIP, Constants.Common.FAILURE);
            throw e;
        }
    }

    private SalesLineList prepareSoLinesForPSlip(UwOrder uwOrder, ReturnCreateRequest returnRequestDto,boolean isFWPackingSlipSuccessFul) {

        SalesLineList salesLineList = new SalesLineList();
        salesLineList.setSalesId(returnRequestDto.getReturnId() + "_" + returnRequestDto.getUwItemId());
        salesLineList.setQuantity(Constants.Common.ITEM_QTY);
        salesLineList.setBatchId(Constants.ReturnOrder.BLANK);
        salesLineList.setItemNumber(StringUtils.defaultString(String.valueOf(uwOrder.getProductId())));
        salesLineList.setSerialId(fetchD365Barcode(uwOrder,isFWPackingSlipSuccessFul));
        salesLineList.setLineNumber(Long.valueOf(uwOrder.getUwItemId()));
        return salesLineList;

    }
    public String fetchD365Barcode(UwOrder uwOrder,boolean isFWPackingSlipSuccessFul){
        String originalBarcode = returnUtil.getBarCode(uwOrder);
        final String  mainBarcode = originalBarcode;

        List<InventoryCorrection> inventoryCorrectionList = inventoryCorrectionWriteRepository.findByBarcode(originalBarcode);

        if (!CollectionUtils.isEmpty(inventoryCorrectionList)) {

            InventoryCorrection correctedBarcode = inventoryCorrectionList.stream().
                    filter(inventoryCorrection ->
                            MovementType.NEG.equals(inventoryCorrection.getMovementType()) &&
                                    "Success".equalsIgnoreCase(inventoryCorrection.getD365SyncStatus()) &&
                                    inventoryCorrection.getProductId().equalsIgnoreCase(String.valueOf(uwOrder.getProductId()))
                    ).
                    sorted( (a,b) ->  -((int)a.getId()-(int)b.getId())) .    // sort in descending order
                    findFirst().                                          // take one with highest id means latest entry
                    orElse(null);

            if (Objects.nonNull(correctedBarcode)) {
                originalBarcode = correctedBarcode.getD365Barcode();    // use corrected dummy barcode
            }else if (!isFWPackingSlipSuccessFul) {   // fw packing not success so just using barcode_1 for syncing return PS
                log.info("Forward packing slip is failed for uwItemID : {} , hence _1 is appending at end of barcode for return packing slip sync ",uwOrder.getUwItemId());
                return originalBarcode + "_1";
            }

        } else {
            if (!isFWPackingSlipSuccessFul) {   // fw packing not success so just using barcode_1 for syncing return PS
                log.info("Forward packing slip is failed for uwItemID : {} , hence _1 is appending at end of barcode for return packing slip sync ",uwOrder.getUwItemId());
                return originalBarcode + "_1";
            }
        }

        return originalBarcode;
    }

    boolean isFWPackingslipSyncSuccessFul(UwOrder uwOrder) {
        String shipmentIdLk = null;
        String facilityLk = null;

        if (uwOrder.getProductDeliveryType().equalsIgnoreCase("B2B") && returnUtil.b2BVirtualOrder(uwOrder)) {
            UwOrder uwOrderLk = uwOrdersRepository.findByB2bRefrenceItemId(uwOrder.getB2bRefrenceItemId());
            if (Objects.nonNull(uwOrderLk)) {
                shipmentIdLk = uwOrderLk.getShippingPackageId();
                facilityLk = uwOrderLk.getFacilityCode();
            }
        } else {
            shipmentIdLk = uwOrder.getShippingPackageId();
            facilityLk = uwOrder.getFacilityCode();
        }
        FinanceSourceSystemSync financeSourceSystemSync = financeSystemSyncRepository.
                findByEventAndEntityTypeAndEntityIdAndFacilityCode(
                        FinanceSourceSystemSyncEvent.PACKING_SLIP, FinanceSourceSystemSyncEntityType.SHIPMENT_ID, shipmentIdLk, facilityLk);

        if (Objects.isNull(financeSourceSystemSync) || !"Success".equals(financeSourceSystemSync.getD365SyncStatus())) {
            return false;
        } else {
            return true;
        }
    }

    public PackingSlip generatePackingSlipPayloadRetry(ReturnCreateRequest pSlipRequest) throws Exception {
        PackingSlip packingSlip = new PackingSlip();
        SalesLineList salesLineList = new SalesLineList();
        ArrayList<SalesLineList> salesLineLists = new ArrayList<SalesLineList>();
        Integer uwItemId = pSlipRequest.getUwItemId();
        Integer returnId = pSlipRequest.getReturnId();
        UwOrder uwOrder = null;
        ReturnOrder returnOrder = null;
        OrdersHeader ordersHeader = null;
        ReturnOrderItem returnOrderItem = null;
        log.info("PSlipRetry : pSlipRequest for uwItemId : {} : {}",uwItemId, pSlipRequest);
        try {
            log.info("PSlipRetry : preparing packingSlip payload for retry for uwItemId : {}",uwItemId);
            uwOrder = uwOrderRepository.findByUwItemId(uwItemId);
            ReturnDetailsDTO returnDetails = returnUtil.getReturns("UW_ITEM_ID", String.valueOf(uwItemId));
            if (uwOrder != null) {
                returnOrderItem = returnUtil.getReturnOrderItem(returnDetails);
                ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
            }
            if (returnOrderItem != null) {
                returnOrder = returnUtil.getReturnOrder(returnDetails);
            }
            if (returnOrder != null) {
                pSlipRequest.setReturnOrder(returnOrder);
                pSlipRequest.setReturnId(returnOrder.getReturnId());
            }
            pSlipRequest.setUwOrder(uwOrder);
            pSlipRequest.setUwItemId(uwItemId);
            log.info("PSlipRetry : uwOrders for uwItemId : {} : {}",uwItemId,uwOrder);

            if (uwOrder != null && returnOrder != null) {
                log.info("PSlipRetry : set SalesId in pSlipRetry for uwItemId : {}",uwItemId);
                packingSlip.setSalesId(returnOrder.getReturnId() + "_" + uwOrder.getUwItemId());
                log.info("PSlipRetry : SalesId in pSlipRetry for uwItemId : {} {}",uwItemId, returnOrder.getReturnId() + "_" + uwOrder.getUwItemId());
            }
            assert uwOrder != null;
            packingSlip.setPackingSlipId(returnUtil.getShippingPackageId(uwOrder));
            returnUtil.setPSlipLegalEntityRetry(uwOrder, ordersHeader, pSlipRequest, packingSlip);
            returnUtil.setPSlipDateRetry(pSlipRequest, uwOrder, packingSlip);
            returnUtil.setPSlipTrackingNoRetry(pSlipRequest, uwOrder, packingSlip);
            returnUtil.setPSlipSalesInvoiceNo(uwOrder, packingSlip);
            packingSlip.setDescription(String.valueOf(uwOrder.getIncrementId()));

            //SalesLine
            List<Integer> uwItemIdList = new ArrayList<>();
            uwItemIdList.add(uwOrder.getUwItemId());
            List<Integer> lensUwItemIds = uwOrderRepository.findUwItemIdByParentUwItemId(uwOrder.getUwItemId());
            uwItemIdList.addAll(lensUwItemIds);
            UwOrder uwOrder1 = null;
            boolean  isFWPackingSlipSuccessFul =isFWPackingslipSyncSuccessFul(uwOrder);

            if (!CollectionUtils.isEmpty(uwItemIdList)) {
                for (Integer uwItemIdNew:uwItemIdList
                ) {
                    uwOrder1 = uwOrderRepository.findByUwItemId(uwItemIdNew);
                    salesLineList = prepareSoLinesForPSlip(uwOrder1, pSlipRequest,isFWPackingSlipSuccessFul);
                    salesLineLists.add(salesLineList);
                }
            }
            packingSlip.setSalesLineList(salesLineLists);
            log.info("PSlipRetry : packingSlip payload prepared for uwItemId : {} : {} ",uwItemId, gson.toJson(packingSlip));
        } catch (Exception e) {
            log.error("PSlipRetry : Exception found to create packingSlip payload in retry for uwItemId : {} : {}",uwItemId, e);
        }
        return packingSlip;
    }

    private String getErrorLogMessage(Exception e) {
        String exceptionMessage = e.getMessage() != null ? e.getMessage() : "Could not generate payload";
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw));
        String stackTrace = sw.toString();
        return exceptionMessage + "\n" + stackTrace;
    }


    @SneakyThrows
    public void syncReturnOrderByReturnId(int returnId){

        ReturnDetailsDTO returnDetails = returnUtil.getReturns("RETURN_ID", String.valueOf(returnId));
        ReturnOrderItem returnOrderItem = returnUtil.getReturnOrderItem(returnDetails);

        if(returnOrderItem != null){
            ReturnRequestMessage returnRequestMessage =new ReturnRequestMessage();
            returnRequestMessage.setUwItemId(returnOrderItem.getUwItemId());
            returnRequestMessage.setReturnId(returnOrderItem.getReturnId());
            kafkaProducerTemplate.send(returnOrderTopic,objectMapper.writeValueAsString(returnRequestMessage));

        }
    }


    public void syncReturnOrderForSerialNumberErrorViaCron() {

        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(startHoursForReturnSerialNumberCron);
        log.info("ReturnOrderServiceImpl : cron started for start :{} , end :{}", startTime, endTime);
        List<D365ReturnTracking> failedReturnPackingSlips = new ArrayList<>();
        failedReturnPackingSlips = d365ReturnTrackingRepository.fetchFailedReturnIdsForPackingslip(startTime, endTime);
        if(!CollectionUtils.isEmpty(failedReturnPackingSlips)){
            failedReturnPackingSlips.forEach( failedReturnPackingSlip -> syncReturnOrderPacking(failedReturnPackingSlip));
        }
        log.info("ReturnOrderServiceImpl : cron finished for return  event: {} , count:{}", "RETURN_PACKING_SLIP", failedReturnPackingSlips.size());

    }



    @SneakyThrows
    public void generatePayloadAndPushToKafka(D365ReturnTracking failedReturnPackingSlip) {

        log.info("ReturnOrderServiceImpl : About to start inventory correction for returnId : {}" , failedReturnPackingSlip.getReturnId());
        ReturnDetailsDTO returnDetails = returnUtil.getReturns("RETURN_ID", String.valueOf(failedReturnPackingSlip.getReturnId()));
        ReturnOrderItem returnOrderItem = returnUtil.getReturnOrderItem(returnDetails);
//        List<ReturnOrderItem> returnOrderItemList = returnOrderItemRepository.findReturnItemListByReturnId(failedReturnPackingSlip.getReturnId());
//        if (!CollectionUtils.isEmpty(returnOrderItemList)) {
//
//        if (returnOrderItemList.size() > 1) {
//            log.info("ReturnOrderServiceImpl :  returnId : {} is having multiple uwITemIDs hence not processing it ",
//                    failedReturnPackingSlip.getReturnId());
//            return;
//        }
//            ReturnOrderItem returnOrderItem = returnOrderItemList.get(0);
            String barcode = null;
            Matcher barCodeMatcher = barcodePattern.matcher(failedReturnPackingSlip.getPslipSyncMessage());
            if (barCodeMatcher.find()) {
                barcode= barCodeMatcher.group(1);
            }

            if(barcode.contains("_")){     // handling if dummy barcode comes for correction for serial number has not been created..
                int underscoreIndex = barcode.indexOf("_");
                barcode = barcode.substring(0, underscoreIndex);
            }
            String barcodeToFind = barcode;
            UwOrder uwOrder = uwOrdersReadRepositoryFinance.findByUwItemId(returnOrderItem.getUwItemId());
            List<UwOrder> uwOrderList = uwOrdersReadRepositoryFinance.findByIncrementId(uwOrder.getIncrementId());
            List<UwOrder> uwOrdersWithMatchingBarcode = uwOrderList.stream().filter(uw -> barcodeToFind.equalsIgnoreCase(uw.getBarcode())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(uwOrdersWithMatchingBarcode)){
                log.error("no uwOrder found for failed barcode : {} , for return Id : {}", barcode,failedReturnPackingSlip.getReturnId());
                return;
            }
            OrdersHeader ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());


            boolean isSBRT = returnUtil.getSBRTFlag(uwOrder);
            String legalEntity = returnUtil.getPSlipLegalEntity(uwOrder,ordersHeader,isSBRT);

        if("COCOBulk".equalsIgnoreCase(uwOrder.getNavChannel())){
            List<Integer> SBRTUwItemIds =null;
            SBRTUwItemIds = sbrtOrderItemService.fetchUwItemIdsFromSBRTOrderItem(Arrays.asList(uwOrdersWithMatchingBarcode.get(0).getUwItemId()));
            if(!CollectionUtils.isEmpty(SBRTUwItemIds)){  // uwItem is SBRT item
                log.info("Return ID : {} has sbrt item ,hence setting legal entity as LKIN  ",failedReturnPackingSlip.getReturnId());
                legalEntity="LKIN";
            }else{
                legalEntity="DKIN";
            }
        }
        SerialNumberErrorDto serialNumberErrorDto = SerialNumberErrorDto.builder().
                entityId(failedReturnPackingSlip.getReturnId().toString()).
                errorString(failedReturnPackingSlip.getPslipSyncMessage()).
                eventTime(LocalDateTime.now()).
                pid(String.valueOf(uwOrdersWithMatchingBarcode.get(0).getProductId())).
                facilityCode(uwOrdersWithMatchingBarcode.get(0).getFacilityCode()).
                legalEntity(legalEntity).
                source("RETURN").
                build();
        kafkaProducerTemplate.send(serialNumberCorrectionTopic,failedReturnPackingSlip.getReturnId().toString(),objectMapper.writeValueAsString(serialNumberErrorDto));

        //}

    }

    public void syncReturnOrdersByReturnIds(List<Integer> returnIds){
        if(CollectionUtils.isEmpty(returnIds)){
            log.info("return id list is empty , hence exiting the process");
            return;
        }
        log.info("start to process returnId list with size : {}", returnIds.size());
        for(int returnId : returnIds){
            try{
                syncReturnOrderByReturnId(returnId);
            }catch (Exception e){
                log.info("Exception occured while syncing list of returnIDs with size : ",returnIds.size(),e);
            }
        }

        log.info("finished processing returnId list with size : {}", returnIds.size());
    }

    @SneakyThrows
    public void syncReturnOrderPacking(D365ReturnTracking failedReturnPackingSlip) {
        log.info("starting to retry returnPackingSlip with returnId : {}",failedReturnPackingSlip.getReturnId());

        ReturnDetailsDTO returnDetails = returnUtil.getReturns("RETURN_ID", String.valueOf(failedReturnPackingSlip.getReturnId()));
        ReturnOrderItem returnOrderItem = returnUtil.getReturnOrderItem(returnDetails);
        if (returnOrderItem != null) {

//            if (returnOrderItemList.size() > 1) {
//                log.info("ReturnOrderServiceImpl :  returnId : {} is having multiple uwITemIDs hence not processing it ",
//                        failedReturnPackingSlip.getReturnId());
//                return;
//            }
//            ReturnOrderItem returnOrderItem = returnOrderItemList.get(0);
            ReturnRequestMessage returnRequestMessage =new ReturnRequestMessage();
            returnRequestMessage.setReturnId(failedReturnPackingSlip.getReturnId());
            returnRequestMessage.setUwItemId(returnOrderItem.getUwItemId());

            kafkaProducerTemplate.send(returnOrderTopic,failedReturnPackingSlip.getReturnId().toString(),objectMapper.writeValueAsString(returnRequestMessage));

        }

    }
    private void setSBRTFlagForLineItem(UwOrder uwOrder, List<Integer> sbrtUwItemIds, SoLine soLine) {
        log.info("isSBRTFlowEnabledForSalesOrder : {}",isSBRTFlowEnabledForSalesOrder);
        if(!isSBRTFlowEnabledForSalesOrder){
            soLine.setUnits(Constants.ReturnOrder.BLANK);
            return;
        }
        if(!CollectionUtils.isEmpty(sbrtUwItemIds) && sbrtUwItemIds.contains(uwOrder.getUwItemId())){
            soLine.setUnits(SBRTItemFlag);
        }else{
            soLine.setUnits(nonSBRTItemFlag);
        }
    }

    private List<String> intCountries;

    @PostConstruct
    void init() {
        intCountries = systemPreferenceService.getValuesAsList(Constants.SYSTEM_PREFERENCE_GROUPS.D365_RETURN, Constants.SYSTEM_PREFERENCE_KEYS.D365_Country_List);
    }

    @Autowired
    SystemPreferenceService systemPreferenceService;

    @Autowired
    AppConfig appConfig;
    @Value("${inventory.adapter.url}")
    private String inventoryAdapterBaseUrl;

    @Value("${financeAdapter.url}")
    String financeAdapterUrl;

    @Autowired
    private CostAverageNewTempService costAverageNewTempService;

    @Autowired
    private ItemWisePriceInfoDetails itemWisePriceInfoService;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    @Autowired
    private ReturnOrderItemService returnOrderItemService;

    @Autowired
    ReturnOrderAddressUpdateRepository returnOrderAddressUpdateRepository;

    @Autowired
    ReturnOrderService returnOrderService;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private UwOrdersService uwOrdersService;

    @Autowired
    private ProductService productService;

    @Autowired
    private OrderAddressUpdateService orderAddressUpdateService;

    @Autowired
    private ShippingStatusService shippingStatusService;

    @Autowired
    private SaleOrderUtil saleOrderUtil;

    @Autowired
    private OrderHeaderService orderHeaderService;

    @Autowired
    private CourierAssignmentsService courierAssignmentsService;

    @Autowired
    private OrderItemGstDetailsService orderItemGstDetailsService;

    @Autowired
    private ClassificationService classificationService;

    @Autowired
    private CourierWisePickingService courierWisePickingService;

    @Autowired
    private S3InvoiceDetailsService s3InvoiceDetailsService;

    @Autowired
    ReturnHistoryRepository returnHistoryRepository;

    @Autowired
    UwOrdersRepository uwOrderRepository;

    @Autowired
    ReturnUtil returnUtil;

    @Autowired
    OrdersHeaderRepository ordersHeaderRepository;

    @Autowired
    SalesOrderService salesOrderService;

    @Autowired
    ItemMasterService itemMasterService;

    @Autowired
    PosUtils posUtils;

    @Autowired
    Gson gson;
}