package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.core.model.Classification;
import com.lenskart.core.model.ItemResolutionFlat;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.clients.NexsClient;
import com.lenskart.financeConsumer.dao.D365TransferJournalTrackingRepository;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.ItemResolutionFlatRepository;
import com.lenskart.financeConsumer.dao.OrdersHeaderRepository;
import com.lenskart.financeConsumer.dao.ReturnOrderItemRepository;
import com.lenskart.financeConsumer.dao.ReturnOrderRepository;
import com.lenskart.financeConsumer.dao.ShippingStatusRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dto.d365requests.CostPriceResponseBody;
import com.lenskart.financeConsumer.dto.d365requests.MovJourLine;
import com.lenskart.financeConsumer.dto.d365requests.MovJournalRequestLine;
import com.lenskart.financeConsumer.dto.d365requests.MovementJournalDTO;
import com.lenskart.financeConsumer.dto.d365requests.MovementJournalRequest;
import com.lenskart.financeConsumer.dto.d365requests.MovementJournalV2Request;
import com.lenskart.financeConsumer.dto.d365requests.TransferJournalDTO.D365TransferJournalDTO;
import com.lenskart.financeConsumer.dto.d365requests.TransferOrderDTO.TransferOrderResponseDto;
import com.lenskart.financeConsumer.exceptions.InvalidRequestException;
import com.lenskart.financeConsumer.financeDb.writeRepository.InventoryWriteRepository;
import com.lenskart.financeConsumer.model.enums.AdjustmentType;
import com.lenskart.financeConsumer.model.enums.MovementJournalFailureType;
import com.lenskart.financeConsumer.model.financeDb.Inventory;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.service.AdjustmentProcessor;
import com.lenskart.financeConsumer.service.ClassificationService;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.HubMasterService;
import com.lenskart.financeConsumer.service.InvoiceDetailsService;
import com.lenskart.financeConsumer.service.ItemMasterService;
import com.lenskart.financeConsumer.service.MovementJournalService;
import com.lenskart.financeConsumer.service.MovementJournalValidator;
import com.lenskart.financeConsumer.service.OrderService;
import com.lenskart.financeConsumer.service.PositiveMovementJournalService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.service.Return.PutawayService;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import com.lenskart.financeConsumer.service.TransferJournal.NegativeMovementJournalService;
import com.lenskart.financeConsumer.service.TransferJournal.TransferJournalService;
import com.lenskart.financeConsumer.service.TransferOrderService;
import com.lenskart.financeConsumer.service.UwOrdersService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.MovementJournalUtils;
import com.lenskart.financeConsumer.util.ObjectHelper;
import com.lenskart.financeConsumer.util.PackingSlipUtil;
import com.lenskart.financeConsumer.util.ProductUtil;
import com.lenskart.financeConsumer.util.ReturnUtil;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MovementJournalServiceImpl implements MovementJournalService {

    @Value("${financeAdapter.url}")
    String financeAdapterUrl;

    @Value("${d365.movementjournal-v2.kafka.topic}")
    String movementJournalV2Topic;

    static Pattern documentNoAlreadyExistPattern = Pattern.compile("The document .* already exists.",
            Pattern.CASE_INSENSITIVE);
    static Pattern socketTimeoutExceptionPattern = Pattern.compile(".*SocketTimeoutException.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern itemMustBeSpecifiedErrorPattern = Pattern.compile(".*Item number must be specified.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern barcodeAlreadyPresentPhysically = Pattern.compile(".*already exists physically in the inventory.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern d365InternalServerErrorPattern = Pattern.compile(".*Internal Server Error.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern badGatewayErrorPattern = Pattern.compile(".*502 Bad Gateway.*",
            Pattern.CASE_INSENSITIVE);

    static Pattern cannotEditRecordErrorPattern = Pattern.compile(".*Cannot edit a record.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern cannotSelectRecordErrorPattern = Pattern.compile(".*Cannot select a record.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern cannotCreateRecordErrorPattern = Pattern.compile(".*Cannot create a record.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern cannotExecuteProcedureErrorPattern = Pattern.compile(".*Cannot execute a stored procedure.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern cannotInsertMultipleRecordsErrorPattern = Pattern.compile(".*Cannot insert multiple records in General journal.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern expectedBeginObjectErrorPattern = Pattern.compile(".*Expected BEGIN_OBJECT but was STRING.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern unspecifiedErrorOnSQLErrorPattern = Pattern.compile(".*Unspecified error occurred on SQL Server.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern stackTraceCompanyLKINNotExistOnSQLErrorPattern = Pattern.compile(".*Stack trace: The company LKI.*",
            Pattern.CASE_INSENSITIVE);
    static Pattern voucherIsAlreadyUsedErrorPattern = Pattern.compile(".*is already used as at date.*",
            Pattern.CASE_INSENSITIVE);



    @Value("${file.insertion.batch.size:200}")
    private int fileDateInsertionBatchSize;

    @Value("${D365.forward.url}")
    private String D365BaseUrl;

    @Autowired
    private GenericClientService genericClientService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private UwOrdersService uwOrdersService;

    @Autowired
    private PutawayService putawayService;

    @Autowired
    private SaleOrderUtil saleOrderUtil;

    @Autowired
    private ProductUtil productUtil;

    @Autowired
    private PackingSlipUtil packingSlipUtil;

    @Autowired
    private ReturnOrderRepository returnOrderRepository;

    @Autowired
    ReturnOrderItemRepository returnOrderItemRepository;

    @Autowired
    OrdersHeaderRepository ordersHeaderRepository;

    @Autowired
    ItemResolutionFlatRepository itemResolutionFlatRepository;

    @Autowired
    ClassificationService classificationService;

    @Autowired
    ProductService productService;

    @Autowired
    HubMasterService hubMasterService;

    @Autowired
    SchedulerConfigService schedulerConfigService;

    @Autowired
    UwOrdersRepository uwOrdersRepository;
    @Autowired
    InvoiceDetailsService invoiceDetailsService;
    @Autowired
    private ShippingStatusRepository shippingStatusRepository;

    @Autowired
    NexsClient nexsClient;

    @Autowired
    D365TransferJournalTrackingRepository d365TransferJournalTrackingRepository;

    @Autowired
    private ItemMasterService itemMasterService;

    @Autowired
    ObjectMapper objectMapper;

    DateFormat dateFormat = new SimpleDateFormat(Constants.Common.DATE_FORMAT);

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);


    @Autowired
    MovementJournalUtils movementJournalUtils;

    @Autowired
    PositiveMovementJournalService positiveMovementJournalService;
    @Autowired
    NegativeMovementJournalService negativeMovementJournalService;

    @Autowired
    MovementJournalValidator movementJournalValidator;
    @Autowired
    FinanceSystemSyncRepository financeSystemSyncRepository;

    @Autowired
    D365LogRepository d365LogRepository;

    @Autowired
    TransferOrderService transferOrderService;

    @Autowired
    TransferJournalService transferJournalService;

    @Autowired
    ReturnUtil returnUtil;

    @Value("${d365.inventory.create.max.Limit:5000}")
    private int limit;

    @Value("${d365.inventory.create.pageSize:1000}")
    private int pageSize;
    @Value("${d365.inventory.create.kafka.topic}")
    private String mjInventoryCreateTopic;

    @Value("${d365.inventory.upload.retryLimit:3}")
    private Integer inventoryUploadFailureRetryLimit;

    @Autowired
    private InventoryWriteRepository inventoryWriteRepository;

    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    private Map<Pattern,String> patternMovementJournalFailureTypeMap;

    @PostConstruct
    public void init() {
        patternMovementJournalFailureTypeMap = new HashMap<>();
        patternMovementJournalFailureTypeMap.put(itemMustBeSpecifiedErrorPattern, MovementJournalFailureType.ITEM_NOT_FOUND.name());
        patternMovementJournalFailureTypeMap.put(badGatewayErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(d365InternalServerErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(socketTimeoutExceptionPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(cannotEditRecordErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(cannotSelectRecordErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(cannotCreateRecordErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(cannotExecuteProcedureErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(cannotInsertMultipleRecordsErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(expectedBeginObjectErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(unspecifiedErrorOnSQLErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(stackTraceCompanyLKINNotExistOnSQLErrorPattern, MovementJournalFailureType.RETRYABLE.name());
        patternMovementJournalFailureTypeMap.put(voucherIsAlreadyUsedErrorPattern, MovementJournalFailureType.RETRYABLE.name());
    }

    @Override
    public boolean validateMovementJournalRequest(MovementJournalRequest movementJournalRequest) {
        try {
            String shippingPackageId = movementJournalRequest.getShippingPackageId();
            boolean validBarcode = false;
            List<UwOrder> allItems = uwOrdersRepository.findAllByShippingPackageId(shippingPackageId);
            if (CollectionUtils.isEmpty(allItems))
                return false;
            //UwOrder uwOrder = allItems.get(0);
            //barcode request validation
            Set<String> barcodeSet = allItems.stream().map(UwOrder::getBarcode).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(movementJournalRequest.getBarcodes())) {
                for (String barcode : movementJournalRequest.getBarcodes()) {
                    if (!barcodeSet.contains(barcode)) {
                        validBarcode = false;
                        log.info("given barcodeSet invalid:{}", barcodeSet);
                        return validBarcode;
                    }
                }
            }
            ShippingStatus shippingStatus = shippingStatusRepository.findByShippingPackageId(shippingPackageId);
            validBarcode = movementJournalUtils.isMovementJournalAllow(shippingStatus.getShipping_time(),
                    shippingStatus.getManifestDate());
            log.info("[validateMovementJournalRequest] movementJournalRequest {} is invalid :{}",
                    objectMapper.writeValueAsString(movementJournalRequest), validBarcode);
            return validBarcode;
        } catch (Exception e) {
            log.info("[validateMovementJournalRequest] movementJournalRequest {} exception {}",
                    movementJournalRequest, e);
            return false;
        }
    }

    public boolean validateMovementJournalRequestV2(MovementJournalV2Request movementJournalRequest) {
        for(MovJournalRequestLine movJournalRequestLine : movementJournalRequest.getMovJournalRequestLines()){
            if(movJournalRequestLine.getBarcode() == null  || movJournalRequestLine.getBarcode().isEmpty()) {
                log.info("[MovementJournalServiceImpl][validateMovementJournalRequestV2] No barcode found: {}", movJournalRequestLine);
                return false;
            }

            if(movJournalRequestLine.getAdjustmentType() == AdjustmentType.TRANSFER) {
                if(movJournalRequestLine.getNewBarcode() == null || movJournalRequestLine.getNewBarcode().isEmpty()) {
                    return false;
                }

                if(movJournalRequestLine.getCostPrice() == null) {
                    return false;
                }
            }

            if(movJournalRequestLine.getUwItemId() == null ) {
                log.info("[MovementJournalServiceImpl][validateMovementJournalRequestV2] Both shippingPackageId and uwItemId are empty :{}", movJournalRequestLine.getBarcode());
                return false;
            }
        }

        return true;
    }



    public boolean validateMovementJournalRequestForCorrections(MovementJournalRequest movementJournalRequest) {
        try {
            String shippingPackageId = movementJournalRequest.getShippingPackageId();
            boolean validBarcode = false;
            List<UwOrder> allItems = uwOrdersRepository.findAllByShippingPackageId(shippingPackageId);
            if (CollectionUtils.isEmpty(allItems))
                return false;
            Set<String> barcodeSet = allItems.stream().map(UwOrder::getBarcode).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(movementJournalRequest.getBarcodes())) {
                for (String barcode : movementJournalRequest.getBarcodes()) {
                    if (!barcodeSet.contains(barcode)) {
                        validBarcode = false;
                        log.info("given barcodeSet invalid:{}", barcodeSet);
                        return validBarcode;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.info("[validateMovementJournalRequestForCorrections] movementJournalRequest {} exception {}",
                    movementJournalRequest, e);
            return false;
        }
    }


    @Override
    public Map<String, Object> createMovementJournalWithMultipleBarcodes(List<MovementJournalRequest> movementJournalRequests) throws Exception {
        log.info("[createMovementJournal] :{}", movementJournalRequests);
        Map<String, Object> response = new HashMap<>();
/*        if (movementJournalRequests.size() > maxAllowedMovementJournals) {
            response.put("Message", "Request has more than "+ maxAllowedMovementJournals+ " requests in list");
            response.put("Success", false);
            return response;
        }*/
        for (MovementJournalRequest movementJournalRequest : movementJournalRequests) {
            if (validateMovementJournalRequestForCorrections(movementJournalRequest)) {
                if (CollectionUtils.isEmpty(movementJournalRequest.getBarcodes())) {
                    String facilityCode = movementJournalRequest.getFacilityCode();
                    List<String> barcodes =
                            uwOrdersRepository.findBarcodeFromShipmentAndFacilityCode(movementJournalRequest.getShippingPackageId(), facilityCode);
                    log.info("MovementJournalServiceImpl : list of barcodes to "
                            + "be processed for shippingPackageId : {},  barcodes : {}", movementJournalRequest.getShippingPackageId(), new Gson().toJson(barcodes));
                    movementJournalRequest.setBarcodes(barcodes);
                    if (CollectionUtils.isEmpty(barcodes)) {
                        log.error("No Barcode Found for ShippingPackageId : {} , FacilityCode : {}", movementJournalRequest.getFacilityCode());
                        continue;
                    }
                }
                for (int i = 0; i < movementJournalRequest.getBarcodes().size(); i++) {
                    log.info("MovementJournalServiceImpl : processing barcode : {} for movement journal", movementJournalRequest.getBarcodes().get(i));
                    /***
                     *   movement journal is triggered for one barcode at a time because document
                     *   number is linked to barcode and only one document number can be sent at a time in current request payload
                     */
                    List<String> barcodeToBeSynced = new ArrayList<>();
                    barcodeToBeSynced.add(movementJournalRequest.getBarcodes().get(i));
                    MovementJournalRequest movementJournalRequestForBarcode = new MovementJournalRequest();
                    if (ObjectUtils.isEmpty(movementJournalRequest.getAdjustmentType())) {
                        movementJournalRequestForBarcode.setAdjustmentType(AdjustmentType.POS);
                    } else {
                        movementJournalRequestForBarcode.setAdjustmentType(movementJournalRequest.getAdjustmentType());
                    }
                    movementJournalRequestForBarcode.setBarcodes(barcodeToBeSynced);
                    movementJournalRequestForBarcode.setFacilityCode(movementJournalRequest.getFacilityCode());
                    movementJournalRequestForBarcode.setMovementJournalType(movementJournalRequest.getMovementJournalType());
                    movementJournalRequestForBarcode.setShippingPackageId(movementJournalRequest.getShippingPackageId());
                    String entityIdForDocumentNo = saleOrderUtil.getSalesOrderNo((movementJournalRequestForBarcode.getShippingPackageId())) + "_" + barcodeToBeSynced.get(0);
                    AdjustmentProcessor adjustmentProcessor = getAdjustmentProcessor(movementJournalRequestForBarcode);
                    String documentNo = adjustmentProcessor.generateDocumentNumber(entityIdForDocumentNo);
                    ResponseEntity responseEntity = createMovementJournalBarcodeWise(movementJournalRequestForBarcode, documentNo);
                    if (responseEntity != null && responseEntity.getBody() != null) {
                        HashMap responseBody = (HashMap) responseEntity.getBody();
                        responseBody.put("Barcode", barcodeToBeSynced.get(0));
                        responseBody.put("DocumentNo", documentNo);
                        response.put(documentNo, responseBody);
                    }
                    response.put(documentNo, responseEntity.getBody());
                    // response is now mapped to barcode instead of shipping package id
                }
            } else {
                response.put(movementJournalRequest.getShippingPackageId(), "Invalid movement journal request");
                response.put("Success", false);
            }
        }
        log.info("final Response map : {} ,", new Gson().toJson(response));
        return response;
    }

    public AdjustmentProcessor getAdjustmentProcessor(MovementJournalRequest movementJournalRequestForBarcode) {
        if (Objects.isNull(movementJournalRequestForBarcode.getAdjustmentType())) {
            return positiveMovementJournalService;
        }
        switch (movementJournalRequestForBarcode.getAdjustmentType()) {
            case POS:
                return positiveMovementJournalService;
            case NEG:
                return negativeMovementJournalService;
            default:
                log.error("wrong type of adjustment type entered by user : {}", movementJournalRequestForBarcode.getAdjustmentType());
                return null;
        }
    }

    public AdjustmentProcessor getAdjustmentProcessor(MovJournalRequestLine movJournalRequestLine) {
        if (Objects.isNull(movJournalRequestLine.getAdjustmentType())) {
            return positiveMovementJournalService;
        }
        switch (movJournalRequestLine.getAdjustmentType()) {
            case POS:
                return positiveMovementJournalService;
            case NEG:
                return negativeMovementJournalService;
            default:
                log.error("wrong type of adjustment type entered by user : {}", movJournalRequestLine.getAdjustmentType());
                return null;
        }
    }

    /***
     * This method syncs one barcode at a time to inventory or removes one barcode at a time from inventory via adjustmentType
     * @param movementJournalRequest
     * @return
     */
    @Override
    public ResponseEntity createMovementJournalBarcodeWise(MovementJournalRequest movementJournalRequest, String documentNumber) {
        log.info("D365 request received for createMovementJournal : {} " + new Gson().toJson(movementJournalRequest));
        ResponseEntity responseEntity;
        String barcode = movementJournalRequest.getBarcodes().get(0);
        MovementJournalDTO movementJournalDTO = new MovementJournalDTO();
        try {
            if (ObjectUtils.isEmpty(movementJournalRequest.getAdjustmentType())) {
                movementJournalRequest.setAdjustmentType(AdjustmentType.POS);
            }
            movementJournalDTO = generateMovementJournalPayload(movementJournalRequest, movementJournalRequest.getAdjustmentType());
            log.info("createMovementJournal payload:{}", objectMapper.writeValueAsString(movementJournalRequest));
            movementJournalDTO.setType(Constants.MovementJournal.ORDER_TYPE);
            movementJournalDTO.setDocumentNumber(documentNumber);
            FinanceSourceSystemSyncEntityType entityType = null;
            if (AdjustmentType.NEG.equals(movementJournalRequest.getAdjustmentType())) {
                entityType = FinanceSourceSystemSyncEntityType.NEGATIVE_MOVEMENT_JOURNAL;
            } else {
                entityType = FinanceSourceSystemSyncEntityType.POSITIVE_MOVEMENT_JOURNAL;
            }
            entityType = Objects.nonNull(movementJournalRequest.getMovementJournalType()) ? movementJournalRequest.getMovementJournalType() : entityType;
            movementJournalUtils.persistJournalToFinanceSourceSync(entityType, documentNumber, movementJournalRequest);
            movementJournalDTO.setType(entityType.name());
            responseEntity = syncMovementJournalToD365(movementJournalDTO);

            if (!responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                HashMap responseBody = (HashMap) responseEntity.getBody();
                String message = (String) responseBody.get("Message");
            } else {
                HashMap responseBody = (HashMap) responseEntity.getBody();
            }
        } catch (Exception e) {
            log.error("MovementJournalServiceImpl : Exception occured while syncing Movement Journal to D365 , Exception : {}", e);
            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> body = new HashMap<String, Object>();
            body.put("Message", genericClientService.getErrorMessage(e));
            body.put("Success", false);
            ResponseEntity response = new ResponseEntity(body, header, HttpStatus.INTERNAL_SERVER_ERROR);
            return response;
        }
        return responseEntity;
    }

    @Override
    public boolean createMovementJournalV2(MovementJournalV2Request movementJournalV2Request) {
        log.info("[MovementJournalServiceImpl][createMovementJournalV2] D365 request received for createMovementJournalV2 : {} ", new Gson().toJson(movementJournalV2Request));
        boolean success = false;
        try {
            for (MovJournalRequestLine movJournalRequestLine : movementJournalV2Request.getMovJournalRequestLines()) {
                if(movJournalRequestLine.getAdjustmentType() == AdjustmentType.TRANSFER) {
                    success = doStockTransfer(movJournalRequestLine);
                }
                else {
                    success = sendSync(movJournalRequestLine, movementJournalV2Request.isTransfer());
                }
            }
            success = true;
        }
        catch (Exception e) {
            log.error("[MovementJournalServiceImpl][createMovementJournalV2] Exception occured while syncing Movement Journal to D365 , Exception : {}", e, e.getMessage());
        }
        return success;
    }


    public MovementJournalDTO generateMovementJournalPayload(MovementJournalRequest movementJournalRequest, AdjustmentType adjustmentType) throws Exception {
        MovementJournalDTO movementJournalDTO = new MovementJournalDTO();
        UwOrder uwOrder = uwOrdersRepository.findTopByShippingPackageId(movementJournalRequest.getShippingPackageId());
        OrdersHeader ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
        movementJournalDTO.setLegalEntity(packingSlipUtil.getLegalEntity(ordersHeader, uwOrder));
        movementJournalDTO.setJournalNameId(Constants.MovementJournal.JOURNAL_NAME_ID);
        List<MovJourLine> movJourLines = new ArrayList<MovJourLine>();
        List<CostPriceResponseBody> costPriceResponseBodyList = nexsClient.getCostPrice(movementJournalRequest.getBarcodes(), movementJournalRequest.getFacilityCode());
        List<String> barcodesList = movementJournalRequest.getBarcodes();
        int index = 0, requestBarcodesSize = barcodesList.size();
 /*       List<D365ApiLog> packingSlipLog= d365LogRepository.findByEntityIdAndOrderType(movementJournalRequest.getShippingPackageId(),"PackingSlip");
        if(packingSlipLog.isEmpty()){
            throw new Exception("Packing slip data is not present on mongoDB for fetching packingSlipDate");
        }
        HashMap<Object,HashMap<Object,Object>> mp=((LinkedHashMap) packingSlipLog.get(0).getRequest()) ; // .get("_contract").get("PackingSlipDate");
        String packingSlipDate= mp.get("_contract").get("PackingSlipDate").toString();
        Date transactionDate = dateFormat.parse(packingSlipDate.toString());
        log.info("packingSlipDate : {}  ,  transactionDate : {}",packingSlipDate,transactionDate);*/
        for (index = 0; index < requestBarcodesSize; index++) {
            if (costPriceResponseBodyList.get(index) != null) {
                MovJourLine movJourLine = getMovementJournalLineItem(ordersHeader, barcodesList.get(index), costPriceResponseBodyList.get(index).getPrice(), adjustmentType);
                log.info("MovJourLine for barcode {} is {}", barcodesList.get(index), new Gson().toJson(movJourLine));
                movJourLines.add(movJourLine);
            }
        }
        movementJournalDTO.setMovJourLines(movJourLines);
        return movementJournalDTO;
    }

    private MovementJournalDTO generateMovementJournalPayloadV2(MovJournalRequestLine movJournalRequestLine, boolean isTransfer) throws Exception {
        log.info("[MovementJournalServiceImpl][generateMovementJournalPayloadV2] Generating payload for : {} ", movJournalRequestLine.getBarcode());
        MovementJournalDTO movementJournalDTO = new MovementJournalDTO();
        UwOrder uwOrder;
        AdjustmentProcessor adjustmentProcessor = getAdjustmentProcessor(movJournalRequestLine);
        String entityIdForDocumentNo = "";
        OrdersHeader ordersHeader;

        if (movJournalRequestLine.getShippingPackageId() != null) {
            uwOrder = uwOrdersRepository.findTopByShippingPackageId(movJournalRequestLine.getShippingPackageId());
            ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
            entityIdForDocumentNo = saleOrderUtil.getSalesOrderNo((movJournalRequestLine.getShippingPackageId())) + "_" + movJournalRequestLine.getBarcode();
        }
        else {
            uwOrder = uwOrdersRepository.findByUwItemId(movJournalRequestLine.getUwItemId());
            ordersHeader = ordersHeaderRepository.findByIncrementId(uwOrder.getIncrementId());
            String site = saleOrderUtil.getInventLocationId(uwOrder, ordersHeader);
            entityIdForDocumentNo = site + "_" + movJournalRequestLine.getBarcode();
        }

        String documentNo = adjustmentProcessor.generateDocumentNumber(entityIdForDocumentNo);
        movementJournalDTO.setDocumentNumber(documentNo);
        movementJournalDTO.setLegalEntity(packingSlipUtil.getLegalEntity(ordersHeader, uwOrder));
        movementJournalDTO.setJournalNameId(Constants.MovementJournal.JOURNAL_NAME_ID);
        List<MovJourLine> movJourLines = new ArrayList<MovJourLine>();

        movementJournalDTO.setType(Constants.MovementJournal.ORDER_TYPE);
        FinanceSourceSystemSyncEntityType entityType = null;
        if (AdjustmentType.NEG.equals(movJournalRequestLine.getAdjustmentType())) {
            entityType = FinanceSourceSystemSyncEntityType.NEGATIVE_MOVEMENT_JOURNAL;
        } else {
            entityType = FinanceSourceSystemSyncEntityType.POSITIVE_MOVEMENT_JOURNAL;
        }
        entityType = Objects.nonNull(movJournalRequestLine.getMovementJournalType()) ? movJournalRequestLine.getMovementJournalType() : entityType;
        //movementJournalUtils.persistJournalToFinanceSourceSync(entityType, documentNo, movementJournalRequest); // required?
        movJournalRequestLine.setMovementJournalType(entityType);
        movementJournalDTO.setType(entityType.name());

        if(movJournalRequestLine.getCostPrice() != null && isTransfer) {
            MovJourLine movJourLine = getMovementJournalLineItemV2(ordersHeader, uwOrder, movJournalRequestLine.getBarcode(), String.valueOf(movJournalRequestLine.getCostPrice()), movJournalRequestLine.getAdjustmentType(), true);
            log.info("MovJourLine for barcode {} is {}", movJournalRequestLine.getBarcode(), new Gson().toJson(movJourLine));
            movJourLines.add(movJourLine);
        }
        else {
            List<String> barcodes = new ArrayList<>();
            barcodes.add(movJournalRequestLine.getBarcode());
            List<CostPriceResponseBody> costPriceResponseBodyList = nexsClient.getCostPrice(barcodes, movJournalRequestLine.getFacilityCode());
            int index, requestBarcodesSize = barcodes.size();
            for (index = 0; index < requestBarcodesSize; index++) {
                if (costPriceResponseBodyList.get(index) != null) {
                    MovJourLine movJourLine = getMovementJournalLineItemV2(ordersHeader, uwOrder, barcodes.get(index), costPriceResponseBodyList.get(index).getPrice(), movJournalRequestLine.getAdjustmentType(), isTransfer);
                    log.info("MovJourLine for barcode {} is {}", barcodes.get(index), new Gson().toJson(movJourLine));
                    movJourLines.add(movJourLine);
                }
            }
        }

        movementJournalDTO.setMovJourLines(movJourLines);
        log.info("[MovementJournalServiceImpl][generateMovementJournalPayloadV2] Payload : {} ", new Gson().toJson(movementJournalDTO));
        return movementJournalDTO;
    }

    private boolean doStockTransfer(MovJournalRequestLine movJournalRequestLine) throws Exception {
        log.error("[MovementJournalServiceImpl][doStockTransfer] Stock Transfer for oldBarcode: {}, New Barcode: {}", movJournalRequestLine.getBarcode(), movJournalRequestLine.getNewBarcode());
        boolean isTransferComplete = createTransferJournalOrOrder(movJournalRequestLine);
        if(isTransferComplete) {
            movJournalRequestLine.setAdjustmentType(AdjustmentType.NEG);
            List<MovJournalRequestLine> movJournalRequestLineList = new ArrayList<>();
            movJournalRequestLineList.add(movJournalRequestLine);
            MovementJournalV2Request movementJournalV2Request = new MovementJournalV2Request();
            movementJournalV2Request.setMovJournalRequestLines(movJournalRequestLineList);
            movementJournalV2Request.setTransfer(true);
            movementJournalUtils.pushToKafka(movementJournalV2Topic, new ObjectMapper().writeValueAsString(movementJournalV2Request));
        }

        log.error("[MovementJournalServiceImpl][doStockTransfer] Stock Transfer failed for oldBarcode: {}, New Barcode: {}", movJournalRequestLine.getBarcode(), movJournalRequestLine.getNewBarcode());
        return false;

    }

    private boolean createTransferJournalOrOrder(MovJournalRequestLine movJournalRequestLine) throws Exception {
        log.info("[MovementJournalServiceImpl][createTransferJournalOrOrder] Transfer Order for oldBarcode: {}, New Barcode: {}", movJournalRequestLine.getBarcode(), movJournalRequestLine.getNewBarcode());
        UwOrder uwOrder = uwOrdersRepository.findByUwItemId(movJournalRequestLine.getUwItemId());
        ItemResolutionFlat itemResolutionFlat = itemResolutionFlatRepository.findByUwItemId(movJournalRequestLine.getUwItemId());
        D365TransferJournalDTO d365TransferJournalDTO = new D365TransferJournalDTO();
        d365TransferJournalDTO.setIsTransferOrder(true);
        d365TransferJournalDTO.setUwItemId(movJournalRequestLine.getUwItemId());
        d365TransferJournalDTO.setReceivedDate(itemResolutionFlat.getCreatedAt());
        d365TransferJournalDTO.setBarcode(uwOrder.getBarcode());
        d365TransferJournalDTO.setIncrementId(itemResolutionFlat.getIncrementId());
        d365TransferJournalDTO.setInventoryType(itemResolutionFlat.getInventoryType());
        d365TransferJournalDTO.setPutawayCode(String.valueOf(itemResolutionFlat.getPutawayCode()));
        if (null != movJournalRequestLine.getUwItemId()) {
            d365TransferJournalDTO.setPutawayCode(String.valueOf(movJournalRequestLine.getUwItemId()));
        }


        log.info("[MovementJournalServiceImpl][createTransferJournalOrOrder] Creating transfer Journal for barcode: {}", movJournalRequestLine.getBarcode());
        TransferOrderResponseDto transferOrderResponseDto = transferOrderService.createTransferOrder(d365TransferJournalDTO);
        Map<String, Object> responseBody = transferOrderResponseDto.getTransferOrderResponse();

        if (responseBody != null && responseBody.containsKey("Success")) {
            Boolean successFlag = (Boolean) responseBody.get("Success");
            log.info("[MovementJournalServiceImpl][createTransferJournalOrOrder] Transfer is successful for barcode: {}", movJournalRequestLine.getBarcode());
            return successFlag;
        }

        log.info("[MovementJournalServiceImpl][createTransferJournalOrOrder] Transfer failed for barcode: {}", movJournalRequestLine.getBarcode());
        return false;
    }

    private boolean sendSync(MovJournalRequestLine movJournalRequestLine, boolean isTransfer) throws Exception {
        boolean status = false;
        String error = "";
        try {
            MovementJournalDTO movementJournalDTO = generateMovementJournalPayloadV2(movJournalRequestLine, isTransfer);
            movementJournalUtils.persistReverseTransferToFinanceSourceSync(movJournalRequestLine.getMovementJournalType(), movementJournalDTO.getDocumentNumber(), movJournalRequestLine);
            ResponseEntity<Map> responseEntity = syncReverseMovementJournalToD365(movementJournalDTO);
            Map<String, Object> responseBody = responseEntity.getBody();
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                log.error("[MovementJournalServiceImpl][createMovementJournalV2] Movement Journal created successfully {}", responseEntity.getBody());

                if (responseBody != null) {
                    if (responseBody.containsKey("Success")) {
                        log.error("[MovementJournalServiceImpl][createMovementJournalV2] Movement Journal created successfully {}", responseEntity.getBody());
                        status = (boolean) responseBody.get("Success");
                    }
                    else {
                        error = responseBody.get("Message").toString();
                        log.error("[MovementJournalServiceImpl][createMovementJournalV2] Movement Journal not created successfully {}", responseEntity);
                    }
                }
                else {
                    error = "No response from d365";
                    log.error("[MovementJournalServiceImpl][createMovementJournalV2] Error syncing Movement Journal to D365 , Response : {}", responseEntity.getBody());
                }

            } else {
                error = responseBody.get("Message").toString();
                log.error("[MovementJournalServiceImpl][createMovementJournalV2] Error syncing Movement Journal to D365 , Response : {}", responseEntity.getBody());
                //pushToRetry?
            }

            movementJournalUtils.updateReverseTransferToFinanceSourceSync(movJournalRequestLine.getMovementJournalType(), movementJournalDTO.getDocumentNumber(), status, error);
        }
        catch (Exception e) {
            log.error("[MovementJournalServiceImpl][createMovementJournalV2] Error syncing Movement Journal to D365 , Error : {} {}", e, e.getStackTrace());
            if(e.getMessage().equalsIgnoreCase("Document number already exists")) {
                log.info("[MovementJournalServiceImpl][createMovementJournalV2] Document number already exists and is already synced to d365");
                return true;
            }
        }

        return status;
    }

    private MovJourLine getMovementJournalLineItem(OrdersHeader ordersHeader, String barcode, String costPrice, AdjustmentType adjustmentType) throws Exception {
        try {
            UwOrder uwOrder = uwOrdersService.getUwOrderByBarcode(barcode);
            Order order = orderService.findOrderByItemId(uwOrder.getItemId());
            Product product = productService.getProduct(uwOrder.getProductId());
            Classification classification = classificationService.getClassification(product.getClassification());

            MovJourLine movJourLine = new MovJourLine();
            Date transactionDate = DateUtils.addDays(order.getCreatedAt(), -1);
            movJourLine.setTransactionDate(dateFormat.format(transactionDate));
            movJourLine.setItemNumber(String.valueOf(uwOrder.getProductId()));
            String locationId = saleOrderUtil.getInventLocationId(uwOrder, ordersHeader);
//            movJourLine.setInventSiteId(locationId);
            movJourLine.setInventLocationId(locationId);
            movJourLine.setInventSerialId(barcode);
            if (!ObjectUtils.isEmpty(adjustmentType) && adjustmentType.equals(AdjustmentType.NEG)) {
                movJourLine.setQty(-1);
            } else {
                movJourLine.setQty(1);
            }
            movJourLine.setLineNumber(Long.valueOf(uwOrder.getUwItemId()));
            //cost price
            movJourLine.setCostPrice(costPrice);
            movJourLine.setStores(saleOrderUtil.getStoreCode(ordersHeader));
            movJourLine.setSalesChannel(locationId);
            movJourLine.setSubChannel(saleOrderUtil.getSubChannel(uwOrder, order));
            movJourLine.setItemClassification(productUtil.getClassificationDisplayName(classification.getId(), classification.getDisplayName()));
            movJourLine.setBrands(StringUtils.defaultString(product.getBrand()).trim());
            return movJourLine;
        } catch (Exception e) {
            log.error("[MovementJournalServiceImpl][setMovementJournalLineItems] error {} ", genericClientService.getErrorMessage(e));
            throw e;
        }
    }

    private MovJourLine getMovementJournalLineItemV2(OrdersHeader ordersHeader, UwOrder uwOrder, String barcode, String costPrice, AdjustmentType adjustmentType, boolean isTransfer) throws Exception {
        try {
            Order order = orderService.findOrderByItemId(uwOrder.getItemId());
            Product product = productService.getProduct(uwOrder.getProductId());
            Classification classification = classificationService.getClassification(product.getClassification());

            MovJourLine movJourLine = new MovJourLine();
            Date transactionDate = DateUtils.addDays(order.getCreatedAt(), -1);
            movJourLine.setTransactionDate(dateFormat.format(transactionDate));
            movJourLine.setItemNumber(String.valueOf(uwOrder.getProductId()));
            String locationId = saleOrderUtil.getInventLocationId(uwOrder, ordersHeader);
//            movJourLine.setInventSiteId(locationId);
            movJourLine.setInventLocationId(locationId);
            movJourLine.setInventSerialId(barcode);
            if (!ObjectUtils.isEmpty(adjustmentType) && adjustmentType.equals(AdjustmentType.NEG)) {
                movJourLine.setQty(-1);
            } else {
                movJourLine.setQty(1);
            }
            movJourLine.setLineNumber(Long.valueOf(uwOrder.getUwItemId())); // what should this be for the new barcode
            //cost price
            movJourLine.setCostPrice(costPrice);
            //not required
            if(!isTransfer) {
                movJourLine.setStores(saleOrderUtil.getStoreCode(ordersHeader));
                movJourLine.setSalesChannel(locationId);
                movJourLine.setSubChannel(saleOrderUtil.getSubChannel(uwOrder, order));
            }

            movJourLine.setItemClassification(productUtil.getClassificationDisplayName(classification.getId(), classification.getDisplayName()));
            movJourLine.setBrands(StringUtils.defaultString(product.getBrand()).trim());
            return movJourLine;
        } catch (Exception e) {
            log.error("[MovementJournalServiceImpl][setMovementJournalLineItems] error {} ", genericClientService.getErrorMessage(e));
            throw e;
        }
    }

    @Override
    public ResponseEntity syncMovementJournalToD365(MovementJournalDTO movementJournalDTO) throws Exception {
        String documentNumber = movementJournalDTO.getDocumentNumber();
        if (!StringUtils.isEmpty(documentNumber)) {
            if (movementJournalValidator.isDocumentNumberExistingAlready(documentNumber, movementJournalDTO.getLegalEntity(), movementJournalDTO.getType())) {
                log.error("Document number already exists, hence not processing movement Journal for document number " + documentNumber);
                throw new Exception("Document number already exists, hence not processing movement Journal for document number " + documentNumber);
            }
        }
        ResponseEntity responseEntity =
                genericClientService.forwardRequest(financeAdapterUrl + Constants.MovementJournal.CREATE_URL,
                        new HttpHeaders(), HttpMethod.POST, movementJournalDTO);
        log.info("createMovementJournal response:{}", objectMapper.writeValueAsString(responseEntity));
        return responseEntity;
    }

    @Override
    public ResponseEntity syncReverseMovementJournalToD365(MovementJournalDTO movementJournalDTO) throws Exception {
        String documentNumber = movementJournalDTO.getDocumentNumber();
        if (!StringUtils.isEmpty(documentNumber)) {
            if (movementJournalValidator.isReverseDocumentNumberExistingAlready(documentNumber, movementJournalDTO.getLegalEntity(), movementJournalDTO.getType())) {
                log.error("Document number already exists, hence not processing movement Journal for document number " + documentNumber);
                throw new Exception("Document number already exists, hence not processing movement Journal for document number " + documentNumber);
            }
        }
        ResponseEntity responseEntity =
                genericClientService.forwardRequest(financeAdapterUrl + Constants.MovementJournal.CREATE_URL,
                        new HttpHeaders(), HttpMethod.POST, movementJournalDTO);
        log.info("createMovementJournal response:{}", objectMapper.writeValueAsString(responseEntity));
        return responseEntity;
    }

    @lombok.SneakyThrows
    @Override
    public void createInventoryByRangeSync(Long start, Long end) {

        if (start > end || (end - start >= limit)) {
            throw new InvalidRequestException("start must not greater than end OR end-start can't be exceeded to limit");
        }

        Pageable pageable;
        int currentPage = 0;
        int total = 0;

        while (total <= limit) {
            pageable = PageRequest.of(currentPage, pageSize, Sort.by("id"));
            List<Inventory> inventories = inventoryWriteRepository.findInventoryByRange(start, end, pageable);
            if (CollectionUtils.isEmpty(inventories)) {
                break;
            }
            inventories.forEach(this::syncAndPersistInventoryUploadResult);
            total += inventories.size();
            currentPage++;
        }
    }

    @lombok.SneakyThrows
    @Override
    public void createInventoryByRangeAsync(Long start, Long end) {
        if (start > end || (end - start >= limit)) {
            throw new InvalidRequestException("start must not greater than end   OR    end-start can't be exceeded to limit");
        }

        Pageable pageable;
        int currentPage = 0;
        int total = 0;

        while (total <= limit) {
            pageable = PageRequest.of(currentPage, pageSize, Sort.by("id"));
            List<Inventory> inventories = inventoryWriteRepository.findInventoryByRange(start, end, pageable);
            if (CollectionUtils.isEmpty(inventories)) {
                break;
            }
            inventories.forEach(i ->  pushToKafka(i.getId()));
            total += inventories.size();
            currentPage++;
        }
    }

    private void pushToKafka(long inventoryId) {
        try{
            kafkaProducerTemplate.send(mjInventoryCreateTopic,String.valueOf(inventoryId) , ObjectHelper.writeValue(inventoryId));
        }catch (Exception e){
            log.error("pushToKafka : while producing event inventoryId {} to topic: {}, exception : {}",inventoryId, mjInventoryCreateTopic,e);
        }
    }

    public void createInventoryById(int id){
        Inventory inventory = inventoryWriteRepository.findById(id);
        if(Objects.isNull(inventory) || Constants.Common.SUCCESS_STATUS.equals(inventory.getD365SyncStatus())){
            return;
        }
        inventory.setD365SyncStatus(Constants.Common.InProgress);

        if ("POS".equals(inventory.getMovementType()) && Objects.isNull(inventory.getCostPrice())) {
            try {
                updateCostPriceDetails(inventory);
            } catch (Exception e) {
                log.error("Error occurred while fetching cost price for barcode :{} and facility code :{}",inventory.getBarcode(),inventory.getFacilityCode(),e);
                inventory.setD365SyncStatus(Constants.Common.FAILED_STATUS);
                inventory.setResponseMessage(genericClientService.getErrorMessage(e));
                inventoryWriteRepository.save(inventory);
                return;
            }
        }
        inventory=inventoryWriteRepository.save(inventory);
        syncAndPersistInventoryUploadResult(inventory);
    }

    public void updateCostPriceDetails(Inventory inventory) throws Exception {
        List<CostPriceResponseBody> costPriceResponseBodyList = nexsClient.getCostPrice(Collections.singletonList(
                inventory.getBarcode()), inventory.getFacilityCode());
        log.info("[updateCostPriceDetails] : costPriceResponseBodyList :{}", costPriceResponseBodyList);
        if (!CollectionUtils.isEmpty(costPriceResponseBodyList)) {
            inventory.setCostPrice(Double.valueOf(costPriceResponseBodyList.get(0).getPrice()));
        }
    }

    public void syncInventoryWithListOfIds(List<Integer> ids) {
        ids.forEach(this::pushToKafka);
    }

    @Override
    public void syncNonSyncedInventory() {
        Pageable pageable;
        int currentPage=0;
        int totalRecordsProcessed = 0;
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(Constants.MovementJournal.INVENTORY_UPLOAD_CRON_NAME);
        if(Boolean.FALSE.equals(schedulerConfig.getEnabled())){
            log.info("MovementJournalServiceImpl : Terminating new inventory upload job as scheduled cron is disabled");
            return;
        }
        int range = schedulerConfig.getRecordLimit();
        int startId = Integer.parseInt(schedulerConfig.getLastProcessed()) + 1;
        int pageLimit = Integer.parseInt(schedulerConfig.getRecordInterval());
        int endId = startId + range;

        if(startId> inventoryWriteRepository.findLastId()){
            log.info("MovementJournalServiceImpl : Terminating new inventory upload job as all new rows have been processed, last Id Processed : {}",startId);
            return;
        }
        while(true){
            pageable= PageRequest.of(currentPage,pageLimit, Sort.by("id"));
            List<Integer> nonSyncedInventoryIds = inventoryWriteRepository.findNonSyncedInventoryRecords(startId,endId,pageable);
            if(nonSyncedInventoryIds.isEmpty()){
                break;
            }
            nonSyncedInventoryIds.forEach(this::pushToKafka);
            totalRecordsProcessed+=nonSyncedInventoryIds.size();
            currentPage++;
        }
        schedulerConfigService.updateLastProcessedForSchedulerConfig(schedulerConfig, Integer.toString(endId));
        log.info("MovementJournalServiceImpl : total Inventory upload records processed : {}",totalRecordsProcessed);
    }

    @Override
    public void syncFailedInventory() {
        SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(Constants.MovementJournal.INVENTORY_UPLOAD_RETRY_CRON_NAME);
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime updatedAtLimit = currentTime.minusMinutes(Integer.parseInt(schedulerConfig.getRecordInterval()));
        if(Boolean.FALSE.equals(schedulerConfig.getEnabled())){
            log.info("MovementJournalServiceImpl : Terminating retry inventory upload job as cron is disabled ");
            return;
        }
        int totalFailedRecordsLimit=schedulerConfig.getRecordLimit();
        List<Inventory> failedInventoryList = inventoryWriteRepository.findFailedRecords(inventoryUploadFailureRetryLimit, totalFailedRecordsLimit,updatedAtLimit);
        processFailedRecords(failedInventoryList);
        log.info("MovementJournalServiceImpl : total failed Inventory upload records processed for sync : {}", failedInventoryList.size());
    }

    public void processFailedRecords(List<Inventory> failedInventoryList) {
        for (Inventory inventory : failedInventoryList) {
            if(Constants.Common.InProgress.equals(inventory.getD365SyncStatus())){
                pushToKafka(inventory.getId());
                continue;
            }
            MovementJournalFailureType failureType = MovementJournalFailureType.valueOf(inventory.getErrorType());
            switch (failureType) {
                case RETRYABLE : pushToKafka(inventory.getId());
                break;
                case ITEM_NOT_FOUND : syncItemAndUpdateInventory(inventory);
                break;
            }
        }
    }

    public void syncItemAndUpdateInventory(Inventory inventory){
        try{
            Integer productId = Integer.valueOf(inventory.getProductId());
            itemMasterService.pushItemToKafka(productId);
            inventory.setErrorType(MovementJournalFailureType.RETRYABLE.name());
            inventoryWriteRepository.save(inventory);
        }catch (Exception e){
            log.error("MovementJournalServiceImpl :  error while sending item for sync via kafka",e);
        }
    }

    public void syncAndPersistInventoryUploadResult(Inventory inventory){
        try {
            MovementJournalDTO movementJournalDTO = prepareMovementJournalPayload(inventory);
            updateInventoryFields(inventory,movementJournalDTO);
            ResponseEntity responseEntity =
                    genericClientService.forwardRequest(financeAdapterUrl + Constants.MovementJournal.CREATE_URL_V2, new HttpHeaders(), HttpMethod.POST, movementJournalDTO);
            HashMap responseBody = (HashMap) responseEntity.getBody();
            if (Objects.nonNull(responseEntity) && responseEntity.getStatusCode().is2xxSuccessful()) {
                if (Objects.nonNull(responseBody)) {
                    String message = (String) responseBody.get("Message");
                    boolean isSuccess= (boolean)responseBody.get("Success");
                    if(isSuccess){
                        inventory.setD365SyncStatus(Constants.Common.SUCCESS_STATUS);
                    }else{
                        String status = documentNoAlreadyExistPattern.matcher(message).find() ? Constants.Common.SUCCESS_STATUS : Constants.Common.FAILED_STATUS;
                        inventory.setD365SyncStatus(status);
                    }
                    inventory.setResponseMessage(message);
                }
            }else {
                String message = (String) responseBody.get("Message");
                inventory.setD365SyncStatus(Constants.Common.FAILED_STATUS);
                inventory.setResponseMessage(message);
            }
        } catch (Exception e) {
            log.error("MovementJournalServiceImpl : Exception for uploading inventory enciuntered for inventory : {} , exception : {}",inventory,e);
            inventory.setD365SyncStatus("FAILED");
            inventory.setResponseMessage(genericClientService.getErrorMessage(e));
        }finally {
            if(Constants.Common.FAILED_STATUS.equals(inventory.getD365SyncStatus())){
                inventory.setErrorType(getErrorTypeForFailedSync(inventory));
            }
            inventoryWriteRepository.save(inventory);
            retryFailedRecord(inventory);
        }
    }

    private void retryFailedRecord(Inventory inventory) {
        if (Constants.Common.FAILED_STATUS.equals(inventory.getD365SyncStatus())
                && MovementJournalFailureType.RETRYABLE.name().equals(inventory.getErrorType())) {
            log.info("MovementJournalServiceImpl : Inventory Record id : {}  re-pushed for retrying the error : {} ",inventory.getId(),inventory.getResponseMessage());
            pushToKafka(inventory.getId());
        }
    }

    private String getErrorTypeForFailedSync(Inventory inventory) {
        String responseMessage = inventory.getResponseMessage();
        if (StringUtils.isNotEmpty(responseMessage)) {
            responseMessage = responseMessage.trim();
            for (Map.Entry<Pattern, String> patternErrorTypeEntry : patternMovementJournalFailureTypeMap.entrySet()) {
                if (patternErrorTypeEntry.getKey().matcher(responseMessage).find()){
                    return patternErrorTypeEntry.getValue();
                }
            }
        }
        return (MovementJournalFailureType.OTHERS.name());
    }

    private static void updateInventoryFields(Inventory inventory, MovementJournalDTO movementJournalRequest) {
        if(Objects.isNull(inventory.getDocumentNo())){
            inventory.setDocumentNo(movementJournalRequest.getDocumentNumber());
        }
        inventory.setRetryCount(inventory.getRetryCount() + 1);
        inventory.setUpdatedBy(Constants.Common.financeConsumer);
    }

    private MovementJournalDTO prepareMovementJournalPayload (Inventory inventory){


        String documentNo = inventory.getMovementType() + "_" + inventory.getProductId() + "_" + inventory.getFacilityCode() + "_" + inventory.getBarcode() + "_" + inventory.getId();
        MovementJournalDTO mjDto = new MovementJournalDTO();
        mjDto.setLegalEntity(inventory.getLegalEntity());
        mjDto.setJournalNameId("Movement");
        mjDto.setDocumentNumber(documentNo);
        if (inventory.getMovementType().equals("NEG")) {
            mjDto.setType(FinanceSourceSystemSyncEntityType.NEGATIVE_MOVEMENT_JOURNAL.name());
        } else {
            mjDto.setType(FinanceSourceSystemSyncEntityType.POSITIVE_MOVEMENT_JOURNAL.name());
        }
        List<MovJourLine> movJourLines = new java.util.ArrayList<>();

        MovJourLine movJourLine = new MovJourLine();
        movJourLines.add(movJourLine);
        mjDto.setMovJourLines(movJourLines);
        movJourLine.setTransactionDate(com.lenskart.financeConsumer.util.DateUtils.getISODateStringFromLocalDateTime
                (inventory.getTransactionDate()));
        movJourLine.setItemNumber(inventory.getProductId());
        movJourLine.setInventLocationId(inventory.getFacilityCode());
        movJourLine.setInventSerialId(inventory.getBarcode());
        movJourLine.setQty(inventory.getQty());
        if(Objects.nonNull(inventory.getCostPrice())){
            movJourLine.setCostPrice((String.format("%.2f",inventory.getCostPrice())));
        }

        return mjDto;
    }

    @Override
    public ResponseEntity ingestInventoryFile(MultipartFile file){

        if (Objects.isNull(file) || file.isEmpty() || !"text/csv".equals(file.getContentType())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Please upload a valid CSV file.");
        }

        log.info("MovementJournalServiceImpl : starting to ingest file ");
        List<Inventory> inventoryList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            int lineNumber = 0;
            log.info("MovementJournalServiceImpl : Buffer reader started reading ");
            while (Objects.nonNull(line = reader.readLine())) {


                lineNumber++;

                String[] inventoryRecordDetails = line.split(",");

                if (lineNumber == 1) {      // validate header file
                    validateFileHeader(inventoryRecordDetails);
                }else{
                    inventoryList.add(fetchInventoryObject(inventoryRecordDetails));
                }


                if (inventoryList.size() == fileDateInsertionBatchSize) {
                    saveInventoryRecords(inventoryList);
                    inventoryList.clear();
                }


            }
        } catch (InvalidRequestException e) {
            log.error("MovementJournalServiceImpl : Exception occured while validating inventory file : ", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Error  in validating  the file: " + e.getMessage());
        } catch (Exception e) {
            log.error("MovementJournalServiceImpl : Exception occured while ingesting inventory file : ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error reading the file: " + e.getMessage());
        }

        if (!CollectionUtils.isEmpty(inventoryList)) {       // leftover lines are finally saved to db
            saveInventoryRecords(inventoryList);
            inventoryList.clear();
        }


        return ResponseEntity.status(HttpStatus.OK).body("File ingested successfully!");
    }


    public Inventory fetchInventoryObject(String[] inventoryFileData) {

        return Inventory.builder().
                movementType(inventoryFileData[0].trim()).
                transactionDate(LocalDateTime.parse(inventoryFileData[1].trim(),formatter)).
                barcode(inventoryFileData[4].trim()).
                facilityCode(inventoryFileData[3].trim()).
                legalEntity(inventoryFileData[6].trim()).
                productId(inventoryFileData[2].trim()).
                qty(Integer.parseInt(inventoryFileData[5].trim())).
                createdBy("finance-consumer-file-upload").
                updatedBy("finance-consumer-file-upload")
                .build();
    }

    public void validateFileHeader(String[] fileHeaderContents) throws InvalidRequestException, JsonProcessingException {

        List<String> headerContents =  Arrays.stream(fileHeaderContents).collect(Collectors.toList());

        List<String> contentsToMacth =Arrays.asList("movement_type","transaction_date","product_id","facility_code","barcode","qty","legal_entity");


        for(int i=0;i<headerContents.size();i++){
            String header = headerContents.get(i);
            if(i>=contentsToMacth.size() || !contentsToMacth.get(i).equals(header)){
                throw new InvalidRequestException("Unknown file header : "+ header+ " found or more headers are found than needed , " +
                        "please ensure file headers are placed with correct exact names and correct order as : "+  objectMapper.writeValueAsString(contentsToMacth));
            }
        }
    }

    public void saveInventoryRecords(List<Inventory> inventoryList){
        inventoryWriteRepository.saveAll(inventoryList);
    }

}