package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.clients.AmazonS3Client;
import com.lenskart.financeConsumer.config.AmazonS3Config;
import com.lenskart.financeConsumer.dto.GrnReportRequestDto;
import com.lenskart.financeConsumer.erpEvents.dao.write.InwardEventWriteRepository;
import com.lenskart.financeConsumer.erpEvents.model.InwardEvent;
import com.lenskart.financeConsumer.facade.GrnReportFacade;
import com.lenskart.financeConsumer.financeDb.writeRepository.GrnReportRepository;
import com.lenskart.financeConsumer.financeDb.writeRepository.GrnPidReportRepository;
import com.lenskart.financeConsumer.model.enums.D365ResponseType;
import com.lenskart.financeConsumer.model.financeDb.GrnReport;
import com.lenskart.financeConsumer.model.financeDb.GrnPidReport;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import com.lenskart.financeConsumer.nexs.readRepository.GrnMasterReadRepository;
import com.lenskart.financeConsumer.service.SchedulerConfigService;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.sendgrid.SendGridException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class GrnReportServiceImpl {
    @Autowired
    private InwardEventWriteRepository inwardEventWriteRepository;
    private static final String GRN_REPORT_CRON = "grn-report-cron";
    private static final String GRN_STATUS_CRON = "grn-status-update-cron";
    private static final DateTimeFormatter isoDateFormatter = DateTimeFormatter.ISO_LOCAL_DATE;
    private static final String GRN_REPORT_FILE_NAME = "GRN_REPORT.csv";
    private static final String GRN_PID_REPORT_FILE_NAME = "GRN_PID_REPORT.csv";
    @Value("${nexs.grn.report.email.flag}")
    public boolean emailFlag;
    @Value("${nexs.grn.report.api.flag}")
    public boolean apiFlag;
    @Value("${nexs.grn.report.fromEmailId}")
    public String fromMailId;
    @Value("${nexs.grn.report.toEmailIds}")
    public String[] toMailIds;
    @Value("${nexs.grn.report.ccEmailIds}")
    public String[] ccMailIds;
    @Value("${aws.s3.finance.report.bucket}")
    public String FINANCE_REPORT_BUCKET_NAME;
    @Value("${aws.s3.endpoint.url}")
    public String FINANCE_REPORT_BUCKET_URL;
    @Value("${grn.report.financial.year}")
    public String grnReportFinancialYear;
    @Value("${grn.report.eligible.vendors}")
    public List<String> grnReportEligibleVendors;
    @Autowired
    private MailerService mailerService;
    @Autowired
    private GrnMasterReadRepository grnMasterReadRepository;
    @Autowired
    private SchedulerConfigService schedulerConfigService;
    @Autowired
    private GrnReportRepository grnLevelReportRepository;
    @Autowired
    private GrnPidReportRepository pidLevelReportRepository;
    @Autowired
    private GrnReportFacade grnReportFacade;
    @Autowired
    private AmazonS3Client amazonS3Client;
    @Autowired
    private AmazonS3Config amazonS3Config;

    public void generateAndShareNexsGrnReport() {
        GenericResponseDto generateReportResponse = generateGrnReportFromNexs(false);
        if("Failure".equalsIgnoreCase(generateReportResponse.getMeta().getMessage())) {
            throw new RuntimeException(generateReportResponse.getMeta().getDisplayMessage());
        }
        sendReport();
    }

    public GenericResponseDto generateGrnReportFromNexs(boolean isApiCall) {
        if (isApiCall && !apiFlag) {
            log.info("[GrnReportServiceImpl][generateGrnReportFromNexs] Manual report generation is disabled.");
            return grnReportFacade.getReportGenerationResponse("Failure",
                                                               "Manual report generation is disabled.");
        }
        String message;
        String status;
        try {
            SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(GRN_REPORT_CRON);
            if (!schedulerConfig.getEnabled()) {
                log.info("[GrnReportServiceImpl][generateGrnReportFromNexs] GRN Report generation is disabled.");
                return grnReportFacade.getReportGenerationResponse("Failure", "GRN Report generation is disabled.");
            }
            log.info("[GrnReportServiceImpl][generateGrnReportFromNexs] Fetching data for finance grn report table from start date {} and end date {}",
                     schedulerConfig.getLastProcessed(),
                     schedulerConfig.getRecordInterval());

            LocalDateTime startDate = LocalDate.parse(schedulerConfig.getLastProcessed()).atStartOfDay();
            LocalDateTime endDate = LocalDate.parse(schedulerConfig.getRecordInterval()).atStartOfDay();

            generateFinanceGrnReport(startDate, endDate);

            startDate = endDate;
            endDate = endDate.plusDays(schedulerConfig.getRecordLimit());

            log.info("[GrnReportServiceImpl][generateGrnReportFromNexs] Updating start date to {} and end date to {}", startDate, endDate);

            schedulerConfig.setLastProcessed(startDate.format(isoDateFormatter));
            schedulerConfig.setRecordInterval(endDate.format(isoDateFormatter));
            schedulerConfigService.updateLastProcessedForSchedulerConfig(schedulerConfig, schedulerConfig.getLastProcessed());

            message = "Successfully triggered NEXS GRN Report generation";
            status = "Success";
        } catch (Exception e) {
            log.error("[GrnReportServiceImpl][generateGrnReportFromNexs] Error while fetching grn data from NEXS", e);
            message = e.getMessage();
            status = "Failure";
        }
        return grnReportFacade.getReportGenerationResponse(status, message);
    }

    public GenericResponseDto updateGrnReportByGrnCodes(GrnReportRequestDto grnReportRequestDto) {
        String message;
        String status;
        try {
            if(grnReportRequestDto.isPidLevel()) {
                List<Object[]> pidLevelReportResult = grnMasterReadRepository.fetchPidLevelReportByGrnCodes(grnReportRequestDto.getGrnCodes());
                List<GrnPidReport> pidLevelReports = pidLevelReportResult.stream()
                        .map(grnReportFacade::convertToPidLevelReport)
                        .collect(Collectors.toList());

                log.info("[GrnReportServiceImpl][updateGrnReportByGrnCodes] Fetched {} rows for GRN PID report for GRN codes {}",
                         pidLevelReports.size(),
                         grnReportRequestDto.getGrnCodes());
                pidLevelReportRepository.saveAll(pidLevelReports);
            } else {
                List<Object[]> grnLevelReportResult = grnMasterReadRepository.fetchGrnLevelReportByGrnCodes(grnReportRequestDto.getGrnCodes());
                List<GrnReport> grnLevelReports = grnLevelReportResult.stream()
                        .map(grnReportFacade::convertToGrnLevelReport)
                        .collect(Collectors.toList());

                log.info("[GrnReportServiceImpl][updateGrnReportByGrnCodes] Fetched {} rows for GRN report for GRN codes {}",
                         grnLevelReports.size(),
                         grnReportRequestDto.getGrnCodes());
                grnLevelReportRepository.saveAll(grnLevelReports);
            }
            message = "Successfully added NEXS GRN Report data for the GRN Codes";
            status = "Success";
        } catch (Exception e) {
            log.error("[GrnReportServiceImpl][generateGrnReportFromNexsByGrn] Error while fetching grn data from NEXS for the GRN Codes", e);
            message = e.getMessage();
            status = "Failure";
        }
        return grnReportFacade.getReportGenerationResponse(status, message);
    }

    private void generateFinanceGrnReport(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> grnLevelReportResult = grnMasterReadRepository.fetchGrnLevelReport(startDate, endDate);
        List<GrnReport> grnLevelReports = grnLevelReportResult.stream()
                .map(grnReportFacade::convertToGrnLevelReport)
                .collect(Collectors.toList());

        log.info("[GrnReportServiceImpl][generateFinanceGrnReport] Fetched {} rows for GRN report", grnLevelReports.size());

        List<Object[]> pidLevelReportResult = grnMasterReadRepository.fetchPidLevelReport(startDate, endDate);
        List<GrnPidReport> pidLevelReports = pidLevelReportResult.stream()
                .map(grnReportFacade::convertToPidLevelReport)
                .collect(Collectors.toList());

        log.info("[GrnReportServiceImpl][generateFinanceGrnReport] Fetched {} rows for GRN PID report", pidLevelReports.size());

        grnLevelReportRepository.saveAll(grnLevelReports);
        pidLevelReportRepository.saveAll(pidLevelReports);
    }


    public GenericResponseDto sendReport() {
        if(Boolean.FALSE.equals(emailFlag)) {
            return grnReportFacade.getReportGenerationResponse("Failure", "NEXS GRN Report sharing via is disabled.");
        }
        validateReports();
        String message;
        String status;
        try {
            byte[] grnLevelReports = getAllGrnReportData();
            byte[] pidLevelReports = getAllPidReportData();

            if (grnLevelReports.length == 0 || pidLevelReports.length == 0) {
                return grnReportFacade.getReportGenerationResponse("Failure", "Unable to get NEXS GRN report data.");
            }
            log.info("[GrnReportServiceImpl][sendReport] GRN level data count : {} and PID level data count : {}", grnLevelReports.length, pidLevelReports.length);

            sendReport(grnLevelReports, pidLevelReports);
            message = "Successfully triggered GRN and PID level reports sending on mail";
            status = "Success";
        } catch (Exception e) {
            log.error("[GrnReportServiceImpl][sendReport] Error while sending GRN and PID level report on mail", e);
            message = e.getMessage();
            status = "Failure";
        }
        return grnReportFacade.getReportGenerationResponse(status, message);
    }

    private byte[] getAllGrnReportData() {
        int page = 0;
        List<GrnReport> grnLevelReportList;
        StringBuilder sb = new StringBuilder();
        sb = grnReportFacade.addGrnLevelHeaders(sb);
        Pageable pageable;
        do {
            pageable = PageRequest.of(page, 2000);
            if (CollectionUtils.isEmpty(grnReportEligibleVendors)) {
                grnLevelReportList = grnLevelReportRepository.findAllData(grnReportFinancialYear, pageable);
            } else {
                grnLevelReportList = grnLevelReportRepository.findAllDataByVendorId(grnReportFinancialYear,
                                                                                    grnReportEligibleVendors,
                                                                                    pageable);
            }
            sb = grnReportFacade.addGrnLevelReportLines(sb, grnLevelReportList);
            page++;
        } while (!grnLevelReportList.isEmpty());
        return sb.toString().getBytes();
    }

    private byte[] getAllPidReportData() {
        int page = 0;
        List<GrnPidReport> pidLevelReportList;
        StringBuilder sb = new StringBuilder();
        sb = grnReportFacade.addPidLevelHeaders(sb);
        Pageable pageable;
        do {
            pageable = PageRequest.of(page, 2000);
            if (CollectionUtils.isEmpty(grnReportEligibleVendors)) {
                pidLevelReportList = pidLevelReportRepository.findAllData(grnReportFinancialYear, pageable);
            } else {
                pidLevelReportList = pidLevelReportRepository.findAllDataByVendorId(grnReportFinancialYear,
                                                                                    grnReportEligibleVendors,
                                                                                    pageable);
            }
            sb = grnReportFacade.addPidLevelReportLines(sb, pidLevelReportList);
            page++;
        } while (!pidLevelReportList.isEmpty());
        return sb.toString().getBytes();
    }

    public void sendReport(byte[] grnLevelReportList, byte[] pidLevelReportList) throws SendGridException, IOException {
        try {
            log.info("[GrnReportServiceImpl][sendReport] Sending GRN report via mail");
            String grnLevelReportUrl = uploadFileToS3(GRN_REPORT_FILE_NAME, grnLevelReportList, GRN_REPORT_FILE_NAME);
            String pidLevelReportUrl = uploadFileToS3(GRN_PID_REPORT_FILE_NAME, pidLevelReportList, GRN_PID_REPORT_FILE_NAME);

            String mailBody = grnReportFacade.buildMailBody(grnLevelReportUrl, pidLevelReportUrl);

            mailerService.sendMailWithoutAttachment(mailBody,
                                                    fromMailId,
                                                    toMailIds,
                                                    ccMailIds,
                                                    "NEXS GRN weekly report");

        } catch (Exception e) {
            log.info("[GrnReportServiceImpl][sendReport] Error while sending GRN report via mail", e);
            throw e;
        }
    }

    private String uploadFileToS3(String filePath, byte[] fileByteArray, String fileKey) throws IOException {
        try {
            amazonS3Client.uploadFileToS3Bucket(filePath, fileByteArray, FINANCE_REPORT_BUCKET_NAME, fileKey);
            return "https://" + FINANCE_REPORT_BUCKET_NAME + "." + amazonS3Config.getAwsS3EndpointURL() + "/" + fileKey;
        } catch (Exception e) {
            log.error("[GrnReportServiceImpl][uploadFileToS3] Error while uploading GRN report to S3.", e);
            throw e;
        }
    }

    public void validateReports() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(1);

        List<Object[]> grnLevelReportResult = grnMasterReadRepository.fetchGrnLevelReport(startDate.atStartOfDay(), endDate.atStartOfDay());
        List<GrnReport> grnLevelReports = grnLevelReportResult.stream()
                .map(grnReportFacade::convertToGrnLevelReport)
                .collect(Collectors.toList());

        log.info("[GrnReportServiceImpl][generateFinanceGrnReport] Fetched {} rows for GRN report for date range {} and {}", grnLevelReports.size(), startDate, endDate);
        addMissingRecordsGrnReport(grnLevelReports, startDate, endDate);

        List<Object[]> pidLevelReportResult = grnMasterReadRepository.fetchPidLevelReport(startDate.atStartOfDay(), endDate.atStartOfDay());
        List<GrnPidReport> pidLevelReports = pidLevelReportResult.stream()
                .map(grnReportFacade::convertToPidLevelReport)
                .collect(Collectors.toList());

        log.info("[GrnReportServiceImpl][generateFinanceGrnReport] Fetched {} rows for GRN PID report for date range {} and {}", pidLevelReports.size(), startDate, endDate);
        addMissingRecordsGrnPidReport(pidLevelReports, startDate, endDate);
    }

    private void addMissingRecordsGrnReport(List<GrnReport> nexsGrnReportList, LocalDate startDate, LocalDate endDate) {
        List<GrnReport> financeGrnReport = grnLevelReportRepository.findByGrnDate(startDate.atStartOfDay(),
                                                                                  endDate.atStartOfDay());

        Set<String> nexsGrnReportSet = nexsGrnReportList.stream()
                .map(GrnReport::getGrnCode)
                .collect(Collectors.toSet());
        Set<String> financeGrnReportSet = financeGrnReport.stream()
                .map(GrnReport::getGrnCode)
                .collect(Collectors.toSet());

        nexsGrnReportSet.removeAll(financeGrnReportSet);

        if (!nexsGrnReportSet.isEmpty()) {
            log.info("[GrnReportServiceImpl][addMissingRecordsGrnReport] Found missing {} records in GRN Report for date range {} and {}.",
                     nexsGrnReportSet.size(),
                     startDate,
                     endDate);
            List<GrnReport> missedGrnReport = nexsGrnReportList.stream()
                    .filter(x -> nexsGrnReportSet.contains(x.getGrnCode()))
                    .collect(Collectors.toList());

            grnLevelReportRepository.saveAll(missedGrnReport);
        } else {
            log.info("[GrnReportServiceImpl][addMissingRecordsGrnReport] No missing GRN records found between dates {} and {}", startDate, endDate);
        }
    }

    private void addMissingRecordsGrnPidReport(List<GrnPidReport> nexsGrnPidReportList, LocalDate startDate, LocalDate endDate) {
        List<GrnPidReport> financeGrnPidReport = pidLevelReportRepository.findByGrnDate(startDate.atStartOfDay(),
                                                                                        endDate.atStartOfDay());
        Set<String> nexsGrnPidReportSet = nexsGrnPidReportList.stream()
                .map(x -> x.getGrnCode() + "_" + x.getPid())
                .collect(Collectors.toSet());
        Set<String> financeGrnPidReportSet = financeGrnPidReport.stream()
                .map(x -> x.getGrnCode() + "_" + x.getPid())
                .collect(Collectors.toSet());

        nexsGrnPidReportSet.removeAll(financeGrnPidReportSet);

        if (!nexsGrnPidReportSet.isEmpty()) {
            log.info("[GrnReportServiceImpl][addMissingRecordsGrnReport] Found missing {} records in GRN PID Report for date range {} and {}.",
                     nexsGrnPidReportSet.size(),
                     startDate,
                     endDate);
            List<GrnPidReport> missedGrnPidReport = nexsGrnPidReportList.stream()
                    .filter(x -> nexsGrnPidReportSet.contains(x.getGrnCode() + "_" + x.getPid()))
                    .collect(Collectors.toList());

            pidLevelReportRepository.saveAll(missedGrnPidReport);
        } else {
            log.info("[GrnReportServiceImpl][addMissingRecordsGrnReport] No missing GRN PID records found between dates {} and {}",
                     startDate,
                     endDate);
        }
    }

    public GenericResponseDto updateGrnReportStatus() {
        String message;
        String status;
        try {
            SchedulerConfig schedulerConfig = schedulerConfigService.getSchedulerConfigByName(GRN_STATUS_CRON);
            if (!schedulerConfig.getEnabled()) {
                log.info("[GrnReportServiceImpl][updateGrnReportStatus] GRN status update is disabled.");
                return grnReportFacade.getReportGenerationResponse(D365ResponseType.FAILED.name(), "GRN status update is disabled");
            }

            LocalDate startDate = LocalDate.parse(schedulerConfig.getLastProcessed());
            LocalDate endDate = LocalDate.parse(schedulerConfig.getRecordInterval());

            List<GrnReport> failedGrnReport = grnLevelReportRepository.findByGrnDateBetweenAndStatus(startDate.atStartOfDay(),
                                                                                                     endDate.atStartOfDay(),
                                                                                                     "Failure");
            List<String> grnCodes = failedGrnReport.stream()
                    .map(GrnReport::getGrnCode)
                    .collect(Collectors.toList());

            List<InwardEvent> inwardEvents = inwardEventWriteRepository.findByEntityIdIn(grnCodes);
            List<String> eligibleGrnCodes = inwardEvents.stream()
                    .filter(inwardEvent -> D365ResponseType.SUCCESS.name().equals(inwardEvent.getStatus()))
                    .map(InwardEvent::getEntityId)
                    .collect(Collectors.toList());

            log.info("[GrnReportServiceImpl][updateGrnReportStatus] Total failed grn size {} and eligible grn size {}",
                     grnCodes.size(), eligibleGrnCodes.size());

            if(!eligibleGrnCodes.isEmpty()) {
                grnLevelReportRepository.updateGrnReport(eligibleGrnCodes, "GRN successfully synced to D365", "Success");
                pidLevelReportRepository.updateGrnPidReport(eligibleGrnCodes, "Success");
            }
            status = D365ResponseType.SUCCESS.name();
            message = "Succesfully updated GRN status";
        } catch (Exception e) {
            log.info("[GrnReportServiceImpl][updateGrnReportStatus] Error while updating GRN status", e);
            message = e.getMessage();
            status = D365ResponseType.FAILED.name();
        }
        return grnReportFacade.getReportGenerationResponse(status, message);
    }
}
