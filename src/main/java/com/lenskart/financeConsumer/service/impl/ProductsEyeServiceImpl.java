package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.financeDb.inventory.read.ProductsEyeReadRepository;
import com.lenskart.financeConsumer.service.ProductsEyeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Slf4j
@Service
public class ProductsEyeServiceImpl implements ProductsEyeService {
    @Autowired
    private ProductsEyeReadRepository productsEyeReadRepository;
    public List<Object[]> getLensDetails(Integer productId){
        try {
            List<Object[]> data = productsEyeReadRepository.getLensDetails(productId);
            return data;
        }
        catch (Exception e){
            log.error("[ProductsEyeServiceImpl][getLensDetails] {} {}",productId,e);
        }
        return null;
    }

}
