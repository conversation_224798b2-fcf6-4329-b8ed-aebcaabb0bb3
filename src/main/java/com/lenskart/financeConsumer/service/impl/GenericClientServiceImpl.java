package com.lenskart.financeConsumer.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.RuleJsonUtils;
import com.lenskart.financeConsumer.config.AppConfig;
import com.lenskart.financeConsumer.dao.ProductRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dao.mongo.D365LogRepository;
import com.lenskart.financeConsumer.dao.mongo.FinanceConsumerLogsRepository;
import com.lenskart.financeConsumer.errorHandler.GenericClientRestTemplateResponseErrorHandler;
import com.lenskart.financeConsumer.model.mongo.FinanceConsumerLogs;
import com.lenskart.financeConsumer.service.GenericClientService;
import com.lenskart.financeConsumer.service.RuleEngineService;
import com.lenskart.financeConsumer.service.SalesOrderService;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URL;
import java.util.*;

@Slf4j
@Service
public class GenericClientServiceImpl implements GenericClientService {

    @Autowired
    AppConfig appConfig;

    @Autowired
    FinanceConsumerLogsRepository financeConsumerLogsRepository;

    @Autowired
    ProductRepository productRepository;

    @Autowired
    SalesOrderService salesOrderService;

    @Autowired
    RuleEngineService ruleEngineService;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Autowired
    D365LogRepository d365LogRepository;

    @Autowired
    UwOrdersRepository uwOrdersRepository;

    @Value("${d365.itemmaster.kafka.topic}")
    private String ItemMasterTopic;

    @Autowired
    @Qualifier("restTemplateWithErrorHandler")
    private RestTemplate restTemplate;

    static  ObjectMapper mapper = new ObjectMapper();

    @Override
    public void saveInitLog(String entityId, Object payload, String orderType, String status) {
        log.info("[GenericClientServiceImpl][saveInitLog] entityId: {} | orderType: {} | status: {} | payloadType: {}",
                entityId, orderType, status, payload != null ? payload.getClass().getSimpleName() : "null");

        List<FinanceConsumerLogs> logList =
                financeConsumerLogsRepository.findAllByEntityIdAndOrderTypeOrderByCreatedAtDesc(entityId, orderType);
        if(CollectionUtils.isEmpty(logList)) {
            FinanceConsumerLogs logData = new FinanceConsumerLogs();
            logData.setEntityId(entityId);
            logData.setRequest(payload);
            logData.setCreatedAt(new Date());
            logData.setUpdatedAt(new Date());
            logData.setStatus(status);
            logData.setOrderType(orderType);
            financeConsumerLogsRepository.save(logData);
            log.info("[GenericClientServiceImpl][saveInitLog] Saved new log for entityId: {} | orderType: {} | status: {}",
                    entityId, orderType, status);
        }else {
            log.info("[GenericClientServiceImpl][saveInitLog] Log already exists for entityId: {} | orderType: {} | status: {} | existingLogCount: {}",
                    entityId, orderType, status, logList.size());
            logList.forEach(item-> {
                item.setUpdatedAt(new Date());
            });
            financeConsumerLogsRepository.saveAll(logList);
        }
    }

    public ResponseEntity forwardRequest(String service, MultiValueMap<String, String> mapHeaders, HttpMethod httpMethod, Object object) {
        //ResponseEntity<HashMap> response;
        String entityId = "";
        ResponseEntity<String> response = new ResponseEntity<String>("", HttpStatus.OK);
        if (service.contains(Constants.SalesOrder.CREATE_URL) || service.contains(Constants.SalesOrder.CREATE_PROD_URL_NEW_INSTANCE)) {
            try {
                String jsonString = mapper.writeValueAsString(object);
                JsonNode jsonObj = mapper.readTree(jsonString);
                entityId = jsonObj.get("SalesOrderNumber").textValue();
            } catch (Exception e) {
                entityId = "";
            }
        }
        if (service.contains(Constants.PackingSlip.CREATE_URL) || service.contains(Constants.PackingSlip.CREATE_URL_NEW_INSTANCE)) {
            try {
                String jsonString = mapper.writeValueAsString(object);
                JsonNode jsonObj = mapper.readTree(jsonString);
                entityId = jsonObj.get("SalesId").textValue();
            } catch (Exception e) {
                entityId = "";
            }
        }
        URI uri;
        HttpEntity entity = null;
        try {
            JsonNode requestObject = mapper.valueToTree(object);
            if (Objects.nonNull(requestObject.get("requestPayload")) && !ruleEngineService.isPayloadSyncAllowed(requestObject.get("requestPayload"), requestObject.get("event").asText())) {
                log.error("Request blocked by Rule Engine for payload {}  , event : {} ", object, requestObject.get("event").asText());
                throw new Exception("Request blocked by rule for event " + requestObject.get("event").asText());
            }
            HttpHeaders header = new HttpHeaders();
            header.putAll(mapHeaders);
            header.setContentType(MediaType.APPLICATION_JSON);
            if ("GET".equalsIgnoreCase(httpMethod.name())) {
                entity = new HttpEntity<Object>(header);
            } else {
                log.info("[GenericClientServiceImpl][ForwardRequest] API call - entityId: {} | service: {} | method: {} | hasHeaders: {}",
                        entityId, service, httpMethod.name(), mapHeaders != null && !mapHeaders.isEmpty());
                entity = new HttpEntity<Object>(object, header);
            }
                if(StringUtils.isBlank(service)){
                    log.error("[GenericClientServiceImpl][ForwardRequest] Error creating url {} ", entityId);
                    throw new Exception("Error creating url "+entityId);
                }
                URL url = new URL(service);
                uri = new URI(url.toString());
            response = restTemplate.exchange(uri, httpMethod, entity, String.class);
            log.info("[GenericClientServiceImpl][ForwardRequest] API response - entityId: {} | statusCode: {} | hasResponseBody: {}",
                    entityId, response.getStatusCode(), response.getBody() != null && !response.getBody().isEmpty());
            // response = restTemplate.exchange(uri, httpMethod, entity, HashMap.class);
            ResponseEntity<Map> responseEntity;
            if(HttpStatus.OK.equals(response.getStatusCode())) {
                    HashMap<String, Object> mapObj = mapper.readValue(response.getBody(), HashMap.class);
                    log.info("[GenericClientServiceImpl][ForwardRequest] Response parsed successfully - entityId: {} | hasSuccess: {}",
                            entityId, mapObj.containsKey("Success"));
                    responseEntity = new ResponseEntity<Map>(mapObj, response.getHeaders(), HttpStatus.OK);
                    if (!service.contains(Constants.STATUSAPI.API) && !mapObj.containsKey("Success")) {
                        log.error("[GenericClientServiceImpl][ForwardRequest] Error occurred after sending req to Finance adaptor entityId {} ", entityId);
                        throw new Exception("Error occurred after sending req to Finance adaptor entityId " + entityId);
                    }
            }
            else {
                HashMap<String,Object> body = new HashMap<String,Object>();
                body.put("Message",response.getBody());
                body.put("Success",false);
                responseEntity = new ResponseEntity<Map>(body, response.getHeaders(), response.getStatusCode());
            }
            log.info("[GenericClientServiceImpl][ForwardRequest] Request completed successfully - entityId: {} | finalStatusCode: {}",
                    entityId, responseEntity.getStatusCode());
            return responseEntity;
        } catch (Exception e) {
            log.error("[GenericClientServiceImpl][ForwardRequest] Error entityId {} error {}", entityId, getErrorMessage(e));
            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            Map<String,Object> body = new HashMap<String,Object>();
            body.put("Message", getErrorMessage(e));
            body.put("Success",false);
            response = new ResponseEntity(body, header, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return response;
    }

    public void saveLog(String entityId, String message, Object payload, String url, String orderType, String status) {
        try {
            if (Status.valueOf(status) == null){
                log.error("[GenericClientServiceImpl][saveLog] status is not valid for {} status: {}",entityId,status);
                return;
            }
            List<String> statusList = new ArrayList<String>();
            statusList.add(Constants.Common.InProcess);
            statusList.add(Constants.Common.FAILURE);
            List<FinanceConsumerLogs> logList = financeConsumerLogsRepository.findAllByEntityIdAndOrderTypeAndStatusInOrderByCreatedAtDesc(entityId, orderType, statusList);

            if (logList.isEmpty() && !status.equals(Constants.Common.SUCCESS)) {
                FinanceConsumerLogs logData = new FinanceConsumerLogs();
                logData.setRetry_count(0);

                log.info("[GenericClientServiceImpl][saveLog] Creating new log - entityId: {} | url: {} | orderType: {} | status: {} | messageLength: {}",
                        entityId, url, orderType, status, message != null ? message.length() : 0);
                logData.setApiUrl(url);
                logData.setMessage(message);
                logData.setEntityId(entityId);
                logData.setRequest(payload);
                logData.setCreatedAt(new Date());
                logData.setUpdatedAt(new Date());
                logData.setStatus(status);
                logData.setOrderType(orderType);
                if (status.equals(Constants.Common.FAILURE)) {
                    logData.setStatusCode(500);
                } else {
                    logData.setStatusCode(200);
                }
                if(StringUtils.containsIgnoreCase(message,Constants.Common.AlreadyExist) && (orderType.equals(Constants.MovementJournal.RETURN_ORDER_TYPE) && !StringUtils.containsIgnoreCase(message,Constants.Common.AlreadyExistPhysically))){
                    logData.setStatus(Constants.Common.SUCCESS);
                }
                else if(StringUtils.containsIgnoreCase(message,Constants.Common.AlreadyExist) && !orderType.equals(Constants.MovementJournal.RETURN_ORDER_TYPE)){
                    logData.setStatus(Constants.Common.SUCCESS);
                }
                financeConsumerLogsRepository.save(logData);

            } else if (!logList.isEmpty()) {
                for (FinanceConsumerLogs logData : logList) {
                    log.info("[GenericClientServiceImpl][saveLog] Updating existing log - entityId: {} | url: {} | orderType: {} | status: {} | retryCount: {}",
                            entityId, url, orderType, status, logData.getRetry_count() + 1);
                    logData.setApiUrl(url);
                    if(StringUtils.containsIgnoreCase(message,Constants.Common.AlreadyExist) && (orderType.equals(Constants.MovementJournal.RETURN_ORDER_TYPE) && !StringUtils.containsIgnoreCase(message,Constants.Common.AlreadyExistPhysically))){
                        logData.setStatus(Constants.Common.SUCCESS);
                    }
                    else if(StringUtils.containsIgnoreCase(message,Constants.Common.AlreadyExist) && !orderType.equals(Constants.MovementJournal.RETURN_ORDER_TYPE)){
                        logData.setStatus(Constants.Common.SUCCESS);
                    }
                    else {
                        logData.setMessage(message);
                    }
                    logData.setEntityId(entityId);
                    logData.setRequest(payload);
                    logData.setCreatedAt(logData.getCreatedAt());
                    logData.setUpdatedAt(new Date());
                    logData.setStatus(status);
                    logData.setOrderType(orderType);
                    if (status.equals(Constants.Common.FAILURE)) {
                        logData.setStatusCode(500);
                    } else {
                        logData.setStatusCode(200);
                    }
                    logData.setRetry_count(logData.getRetry_count() + 1);
                }
                financeConsumerLogsRepository.saveAll(logList);
            }
        } catch (Exception e) {
            log.error("[GenericClientServiceImpl][saveLog] entityId {} error {}", entityId ,getErrorMessage(e));
        }
    }

    public String getErrorMessage(Exception e) {
        StackTraceElement[] elements = e.getStackTrace();
        StringBuffer ex = new StringBuffer();
        ex.append(e.getMessage());
        ex.append(System.getProperty("line.separator"));
        for (int i = 0; i < 6 && i < elements.length; i++) {
            ex.append(elements[i]);
            ex.append(System.getProperty("line.separator"));
        }
        return String.valueOf(ex);
    }

}