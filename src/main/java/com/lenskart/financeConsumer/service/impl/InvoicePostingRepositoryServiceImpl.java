package com.lenskart.financeConsumer.service.impl;

import com.lenskart.financeConsumer.financeDb.writeRepository.InvoicePostingRepository;
import com.lenskart.financeConsumer.exceptions.DatabaseException;
import com.lenskart.financeConsumer.exceptions.InvoiceNotFoundException;
import com.lenskart.financeConsumer.model.enums.InvoicePostingStatus;
import com.lenskart.financeConsumer.model.enums.InvoicePostingSubStatus;
import com.lenskart.financeConsumer.model.financeDb.Invoice;
import com.lenskart.financeConsumer.service.InvoicePostingRepositoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class InvoicePostingRepositoryServiceImpl implements InvoicePostingRepositoryService {

    @Autowired
    InvoicePostingRepository invoicePostingRepository;

    @Qualifier("financeDbEntityManagerFactory")
    @PersistenceContext
    private EntityManager em;

    @Override
    public Page<Invoice> getAllInvoice(Pageable pageable) {
        try {
            return invoicePostingRepository.findAll(pageable);
        } catch (DataAccessException e) {
            log.error("[InvoicePostingRepositoryServiceImpl][getAllInvoice] Error while fetching all invoices for request {}", pageable, e);
            throw new DatabaseException("Error while fetching all invoices");
        }
    }

    @Override
    public Invoice getInvoiceById(long id) {
        try {
            return invoicePostingRepository.findById(id).orElseThrow(() -> {
                log.error("[InvoicePostingRepositoryServiceImpl][getInvoiceById] Error while fetching invoice. No invoice found for id {}", id);
                return new InvoiceNotFoundException("Invoice not found for id " + id + ". Please check the request.");
            });
        } catch (DataAccessException e) {
            log.error("[InvoicePostingRepositoryServiceImpl][getInvoiceById] Error while fetching invoice with id {}", id, e);
            throw new DatabaseException("Error while fetching invoice. Please try again.");
        }
    }

    @Override
    public void deleteInvoice(long id) {
        try {
            invoicePostingRepository.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            log.error("[InvoicePostingRepositoryServiceImpl][deleteInvoice] Error while deleting invoice. Invoice does not exists for given id {}.", id, e);
            throw new InvoiceNotFoundException("Invoice not found for id " + id + ". Please check the request.");
        } catch (Exception e) {
            log.error("[InvoicePostingRepositoryServiceImpl][deleteInvoice] Error while deleting invoice with id {}", id, e);
            throw new DatabaseException("Error while deleting invoice. Please try again.");
        }
    }

    @Override
    public Invoice getInvoiceByVendorInvoiceNumber(String invoiceNumber) {
        try {
            return invoicePostingRepository.findByVendorInvoiceNumber(invoiceNumber);
        } catch (DataAccessException e) {
            log.error("[InvoicePostingRepositoryServiceImpl][getInvoiceByInvoiceNumber] Error while fetching invoice for invoice number : {}", invoiceNumber, e);
            throw new DatabaseException("Error while fetching invoice. Please try again.");
        }
    }

    @Override
    public Invoice saveInvoice(Invoice invoice) {
        try {
            return invoicePostingRepository.save(invoice);
        } catch (DataAccessException e) {
            log.error("[InvoicePostingRepositoryServiceImpl][saveInvoice] Error while saving invoice for invoice number : {}", invoice.getVendorInvoiceNumber(), e);
            throw new DatabaseException("Error while saving invoice. Please try again.");
        }
    }

    @Override
    public List<Invoice> getInvoiceByStatus(List<String> status) {
        try {
            return invoicePostingRepository.findByInvoiceStatusIn(status);
        } catch (DataAccessException e) {
            log.error("[InvoicePostingRepositoryServiceImpl][getInvoiceByStatus] Error while finding invoices for status in {}", status.toString(), e);
            throw new DatabaseException("Error while fetching invoice. Please try again.");
        }
    }


    @Override
    public Invoice updateInvoiceStatus(Invoice invoice, InvoicePostingStatus status) {
        invoice.setInvoiceStatus(status);
        return saveInvoice(invoice);
    }

    @Override
    public Invoice updateInvoiceSubStatus(Invoice invoice, InvoicePostingSubStatus status) {
        invoice.setInvoiceSubStatus(status);
        return saveInvoice(invoice);
    }

    @Override
    public Invoice updateInvoiceNexsDetails(Invoice invoice, String nexsAmount) {
        invoice.setInvoiceSubStatus(InvoicePostingSubStatus.INVOICE_CLOSED);
        invoice.setNexsInvoiceAmount(new BigDecimal(nexsAmount).setScale(4));
        return saveInvoice(invoice);
    }

    @Override
    public Invoice updateInvoiceState(Invoice invoice, String errorMessage, InvoicePostingSubStatus status) {
        invoice.setErrorMessage(errorMessage);
        return updateInvoiceSubStatus(invoice, status);
    }

}
