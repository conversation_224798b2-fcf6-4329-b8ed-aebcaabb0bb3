package com.lenskart.financeConsumer.service;

import com.lenskart.orderops.dao.SbrtOrderItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SBRTOrderItemService {

    @Autowired
    private SbrtOrderItemRepository sbrtOrderItemReadRepository;

    public List<Integer> fetchUwItemIdsFromSBRTOrderItem(List<Integer> uwItemIdsToSearch) {
        return sbrtOrderItemReadRepository.fetchUwItemIdsFromSBRTOrderItem(uwItemIdsToSearch);
    }
}
