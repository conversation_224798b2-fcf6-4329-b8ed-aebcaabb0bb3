package com.lenskart.financeConsumer.service;

import com.lenskart.financeConsumer.dto.d365requests.SchedulerConfigUpdateRequestDto;
import com.lenskart.financeConsumer.exceptions.EntityNotFoundException;
import com.lenskart.financeConsumer.financeDb.writeRepository.SchedulerConfigWriteRepository;
import com.lenskart.financeConsumer.model.financeDb.SchedulerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SchedulerConfigService {
    @Autowired
    private SchedulerConfigWriteRepository schedulerConfigWriteRepository;

    /***
     * @param lastProcessed String that contains either lastProcessedId or lastProcessedDate
     */
    public void updateLastProcessedForSchedulerConfig(SchedulerConfig schedulerConfig, String lastProcessed) {
        schedulerConfig.setLastProcessed(lastProcessed);
        schedulerConfigWriteRepository.save(schedulerConfig);
    }

    public SchedulerConfig getSchedulerConfigByName(String cronName) {
        return schedulerConfigWriteRepository.findByName(cronName)
                .orElseThrow(() -> new EntityNotFoundException("Scheduler Config not found for the mentioned cron : "
                        + cronName));
    }

    public void updateSchedulerConfigById(Integer id, SchedulerConfigUpdateRequestDto schedulerConfigUpdateRequestDto) {
        log.info("SchedulerConfigService : update scheduler config request received for id : {} , request : {}",
                id, schedulerConfigUpdateRequestDto.toString());
        SchedulerConfig schedulerConfig= schedulerConfigWriteRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Scheduler Config not found for the mentioned cron  with id : "
                        + id));
        schedulerConfig.setLastProcessed(schedulerConfigUpdateRequestDto.getLastProcessed());
        schedulerConfig.setEnabled(schedulerConfigUpdateRequestDto.isEnabled());
        schedulerConfig.setRecordLimit(schedulerConfigUpdateRequestDto.getRecordLimit());
        schedulerConfig.setRecordInterval(schedulerConfigUpdateRequestDto.getRecordInterval());
        schedulerConfigWriteRepository.save(schedulerConfig);
    }
}
