package com.lenskart.financeConsumer.service;

import com.lenskart.core.model.ExchangeOrder;
import com.lenskart.core.model.Order;
import com.lenskart.financeConsumer.exceptions.RecordNotFoundException;

import java.util.List;


public interface OrderService {
    public List<Order> getOrders(Integer incrementId) throws Exception;
    public Order getOrderBasedOnIncrementIdAndFacilityCodeAndType(Integer incrementId,String facilityCode,String productDeliveryType) throws RecordNotFoundException;

    Order getOrder(Integer incrementId) throws Exception;
    Order findOrderByItemId(Integer itemId);

    ExchangeOrder getExchangeOrder(Integer exchangeIncrementId);
}
