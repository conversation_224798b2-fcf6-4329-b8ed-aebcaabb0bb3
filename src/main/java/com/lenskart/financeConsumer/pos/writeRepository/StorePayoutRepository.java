package com.lenskart.financeConsumer.pos.writeRepository;

import com.lenskart.financeConsumer.model.pos.StorePayoutEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

public interface StorePayoutRepository extends CrudRepository<StorePayoutEntity, Long> {

    @Modifying
    @Transactional(value = "posTransactionManager")
    @Query(value = "UPDATE store_payout SET request= ?2, d365_status= ?3, response= ?4 WHERE id= ?1", nativeQuery = true)
    void updateD365SyncStatusWithFailureResponse(Long id, String request, String d365Status, String response);

    @Modifying
    @Transactional(value = "posTransactionManager")
    @Query(value = "UPDATE store_payout SET d365_status= ?2, journal_batch_number= ?3 WHERE id= ?1", nativeQuery = true)
    void updateD365SyncStatusWithSuccessResponse(Long id, String d365Status, String journalBatchNumber);
}