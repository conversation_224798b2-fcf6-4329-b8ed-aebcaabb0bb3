package com.lenskart.financeConsumer.pos.writeRepository;

import com.lenskart.financeConsumer.model.pos.CommissionModel;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

public interface POSCommissionPayoutRepository extends CrudRepository<CommissionModel, String> {

    @Modifying
    @Transactional(value = "posTransactionManager")
    @Query(value="UPDATE commission_payout SET d365_sync= ?2,response= ?3 WHERE commission_invoice_code= ?1 and is_deleted=0", nativeQuery = true)
    void updateD365SyncStatusWithResponse(String invoiceNumber,String d365Status, String response);

}
