package com.lenskart.financeConsumer.facade;

import com.google.gson.Gson;
import com.lenskart.core.model.Product;
import com.lenskart.core.model.ShippingStatus;
import com.lenskart.core.model.UwOrder;
import com.lenskart.financeConsumer.constant.EventErrorType;
import com.lenskart.financeConsumer.constant.KafkaConstants;
import com.lenskart.financeConsumer.dao.FinanceSystemSyncRepository;
import com.lenskart.financeConsumer.dao.ShippingStatusRepository;
import com.lenskart.financeConsumer.dao.UwOrdersRepository;
import com.lenskart.financeConsumer.dto.d365requests.FinanceConsumerLogsSummaryDto;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemRetryRequest;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemSyncDiscrepancy;
import com.lenskart.financeConsumer.dto.d365requests.FinanceSourceSystemSyncDto;
import com.lenskart.financeConsumer.erpEvents.util.CommonUtils;
import com.lenskart.financeConsumer.financeDb.inventory.read.FinanceSystemSyncReadRepository;
import com.lenskart.financeConsumer.handler.FinanceSourceSyncEmailHandler;
import com.lenskart.financeConsumer.handler.FinanceSummaryReportEmailHandler;
import com.lenskart.financeConsumer.model.enums.FinanceServiceEventTypes;
import com.lenskart.financeConsumer.model.mongo.FinanceConsumerLogs;
import com.lenskart.financeConsumer.service.InventoryCorrectionService;
import com.lenskart.financeConsumer.service.ItemMasterService;
import com.lenskart.financeConsumer.service.ProductService;
import com.lenskart.financeConsumer.strategy.FailureEventHelper;
import com.lenskart.financeConsumer.strategy.SerialNumberErrorStrategy;
import com.lenskart.financeConsumer.util.Constants;
import com.lenskart.financeConsumer.util.SaleOrderUtil;
import com.lenskart.wm.model.FinanceSourceSystemSync;
import com.lenskart.wm.types.D365SyncSubStatusEnum;
import com.lenskart.wm.types.FinanceSourceSystemSyncEntityType;
import com.lenskart.wm.types.FinanceSourceSystemSyncEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Slf4j
@Component
public class FinanceSourceSystemSyncFacade {

    private static final Map<FinanceSourceSystemSyncEvent, String> FINANCE_SYSTEM_SYNC_EVENT_TO_KAFKA_TOPIC_MAP = new HashMap<>();
    private static final String FINANCE_SOURCE_SYSTEM_SYNC_MAX_RETRY_COUNT = "finance.source.system.sync.max.retry.count";
    private static final String FINANCE_SOURCE_SYSTEM_SYNC_ALLOWED_EVENTS = "finance.source.system.sync.allowed.events";
    private static final String FINANCE_SOURCE_SYSTEM_SYNC_ALLOWED_SIZE = "finance.source.system.sync.allowed.size";
    private static final String FINANCE_SOURCE_SYSTEM_SYNC_INVOICE_ALLOWED_SIZE = "finance.source.system.sync.invoice.allowed.size";
    private static final String FINANCE_SOURCE_SYSTEM_SYNC_INVOICE_ALLOWED_HOUR = "finance.source.system.sync.invoice" +
            ".allowed.hour";
    private static final String FINANCE_BLOCK_ACK_ERROR_MESSAGE = "finance.blocked.ack.error.message";

    @Autowired
    private FinanceSystemSyncRepository financeSystemSyncRepository;

    @Autowired
    private ShippingStatusRepository shippingStatusRepository;

    @Autowired
    private ProductService productService;

    @Autowired
    private FinanceSourceSyncEmailHandler financeSourceSyncEmailHandler;

    @Autowired
    private FinanceSummaryReportEmailHandler financeSummaryReportEmailHandler;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private FailureEventHelper failureEventHelper;

    @Value("#{'${finance.blocked.pid.list}'.split(',')}")
    private List<String> pidList;

    @Value("${finance.source.ack.consumer.enable:false}")
    private boolean isConsumerEnable;

    @Value("${finance.source.failed.sync.retry.enable:true}")
    private boolean isFailedSyncedEnable;

    @Value("${is.process.serial.number.packing.slip:false}")
    private boolean isProcessSerialNumberPackingSlip;

    @Autowired
    UwOrdersRepository uwOrdersRepository;

    @Autowired
    private Environment environment;

    @Qualifier("inventorySlaveTransactionManager")
    @PersistenceContext
    private EntityManager inventorySlaveTransactionManager;

    @Autowired
    private FailureEventSyncFacade failureEventSyncFacade;

    private List<EventErrorType> blockErrorMessageAckType;

    @Autowired
    ItemMasterService itemMasterService;

    @Autowired
    private SaleOrderUtil saleOrderUtil;

    @Autowired
    private SerialNumberErrorStrategy serialNumberErrorStrategy;

    @Autowired
    private InventoryCorrectionService inventoryCorrectionService;

    @Autowired
    private FinanceSystemSyncReadRepository financeSystemSyncReadRepository;

    @Autowired
    private CommonUtils commonUtils;

    @Value("${soPs.d365.failure.report.email.from}")
    private String mailSendFrom;
    @Value("${soPs.d365.failure.report.email.sendto}")
    private String[] mailSendTo;
    @Value("${soPs.d365.failure.report.email.cc}")
    private String[] mailCC;
    @Value("${soPs.d365.failure.report.page.size:10}")
    private int pageLimit;

    @PostConstruct
    public void init() {
        FINANCE_SYSTEM_SYNC_EVENT_TO_KAFKA_TOPIC_MAP.put(FinanceSourceSystemSyncEvent.SALE_ORDER, KafkaConstants.SALES_ORDER_TOPIC);
        FINANCE_SYSTEM_SYNC_EVENT_TO_KAFKA_TOPIC_MAP.put(FinanceSourceSystemSyncEvent.PACKING_SLIP, KafkaConstants.PACKING_ORDER_TOPIC);
        FINANCE_SYSTEM_SYNC_EVENT_TO_KAFKA_TOPIC_MAP.put(FinanceSourceSystemSyncEvent.ITEM_MASTER, KafkaConstants.ITEM_MASTER_TOPIC);
        populateErrorType();
    }

    private void populateErrorType() {
        String blockMessage = environment.getProperty(FINANCE_BLOCK_ACK_ERROR_MESSAGE);
        log.info("blockMessage: {}",blockMessage);
        List<String> blockErrorMessageAck = Arrays.asList(blockMessage.split(","));
        blockErrorMessageAckType = new ArrayList<>();
        log.info("blockErrorMessageAck:{}",blockErrorMessageAck);
        blockErrorMessageAck.stream().forEach(message -> {
            EventErrorType eventErrorType = failureEventHelper.findEventErrorType(message);
            if (eventErrorType != EventErrorType.NONE) {
                blockErrorMessageAckType.add(eventErrorType);
            }
        });
        log.info("blockErrorMessageAckType: {}",blockErrorMessageAckType);
    }

    @Transactional(value = "inventoryTransactionManager", rollbackFor = Exception.class)
    public void saveToFinanceSourceSystemSyncIfApplicable(String entityId, Object payload, FinanceSourceSystemSyncEvent eventName,
                                                          FinanceSourceSystemSyncEntityType entityType,
                                                          String navChannel, Integer incrementId, String facilityCode) {
        try {
            if (!StringUtils.hasLength(entityId)) {
                log.info("skipping save due to null or empty entityId: {}", entityId);
                return;
            }
            FinanceSourceSystemSync financeSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(
                    eventName,
                    entityType,
                    entityId,
                    facilityCode
            );
            if (financeSystemSync == null) {
                financeSystemSync = new FinanceSourceSystemSync();
                financeSystemSync.setEvent(eventName);
                financeSystemSync.setEntityType(entityType);
                financeSystemSync.setEntityId(entityId);
                financeSystemSync.setPayload(new Gson().toJson(payload));
                financeSystemSync.setSyncedToFinanceSystem(Boolean.FALSE);
                financeSystemSync.setRetryCount(0);
                financeSystemSync.setCreatedAt(new Date());
                financeSystemSync.setUpdatedAt(new Date());
                financeSystemSync.setCreatedBy("finance-consumer");
                financeSystemSync.setUpdatedBy("finance-consumer");
                financeSystemSync.setFacilityCode(facilityCode);
                if (incrementId != null) {
                    financeSystemSync.setIncrementId(incrementId.longValue());
                }
                if (StringUtils.hasLength(navChannel)) {
                    financeSystemSync.setDescription("navChannel: " + navChannel);
                }
                updateEventTime(eventName,entityType,entityId,financeSystemSync);
                financeSystemSyncRepository.save(financeSystemSync);
            } else {
                if (financeSystemSync.getEventTime() == null) {
                    updateEventTime(eventName, entityType, entityId, financeSystemSync);
                    financeSystemSync.setUpdatedBy("finance-consumer");
                    financeSystemSyncRepository.save(financeSystemSync);
                }
            }
        } catch (Exception ex) {
            log.error("saveToFinanceSourceSystemSyncIfApplicable Error for entityId:{} | message:{} ", entityId,
                    ex.getMessage(), ex);
        }
    }

    private void updateEventTime(FinanceSourceSystemSyncEvent eventName, FinanceSourceSystemSyncEntityType entityType, String entityId, FinanceSourceSystemSync financeSystemSync){
        if((eventName.equals(FinanceSourceSystemSyncEvent.SALE_ORDER) || eventName.equals(FinanceSourceSystemSyncEvent.PACKING_SLIP)) && entityType.equals(FinanceSourceSystemSyncEntityType.SHIPMENT_ID)){
            ShippingStatus shippingStatus = shippingStatusRepository.findByShippingPackageId(entityId);
            if(shippingStatus!= null && StringUtils.hasLength(shippingStatus.getShipping_time())){
                try {
                    Date eventTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(shippingStatus.getShipping_time());
                    financeSystemSync.setEventTime(eventTime);
                }
                catch (Exception e){
                    log.error("[FinanceSourceSystemSyncFacade][saveToFinanceSourceSystemSyncIfApplicable] error while parsing shipping_time in shipping_status {} error {}",entityId,e.getMessage());
                }
            }
        }
        else if(eventName.equals(FinanceSourceSystemSyncEvent.ITEM_MASTER) && entityType.equals(FinanceSourceSystemSyncEntityType.PRODUCT_ID)){
            try{
                Integer productId = Integer.parseInt(entityId);
                Product product = productService.getProduct(productId);
                if(product!=null && product.getCreatedAt()!=null){
                    financeSystemSync.setEventTime(product.getCreatedAt());
                }
            }
            catch (Exception e){
                log.error("[FinanceSourceSystemSyncFacade][saveToFinanceSourceSystemSyncIfApplicable] error setting event time for product {} error {}",entityId,e.getMessage());
            }
        }
    }

    @Transactional(value = "inventoryTransactionManager", rollbackFor = Exception.class)
    public void processAckEvent(FinanceSourceSystemSyncDto financeSystemSyncDto) throws Exception {
        if(Objects.isNull(financeSystemSyncDto)){
            log.info("FinanceSourceSystemSyncDto is null!");
            return;
        }
        if(!isConsumerEnable){
            log.info("isConsumerEnable:{}",isConsumerEnable);
            return;
        }
        Assert.notNull(financeSystemSyncDto.getEvent(), "event can not be null");
        Assert.notNull(financeSystemSyncDto.getEntityType(), "entity type can not be null");
        if (!StringUtils.hasLength(financeSystemSyncDto.getEntityId())) {
            log.error("skipping ack processing due to null or empty entityId for : {}", financeSystemSyncDto);
            return;
        }
        Assert.hasLength(financeSystemSyncDto.getEntityId(), "entity id can not be null");

        FinanceSourceSystemSync financeSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(
                financeSystemSyncDto.getEvent(),
                financeSystemSyncDto.getEntityType(),
                financeSystemSyncDto.getEntityId(),
                financeSystemSyncDto.getFacilityCode()
        );


        boolean saveEntryInFinanceSourceSystemSyncFacade = false;
        if(FinanceSourceSystemSyncEvent.ITEM_MASTER.equals( financeSystemSyncDto.getEvent())){
            itemMasterService.saveD365ResponseToItemD365Sync(financeSystemSyncDto);
            return;
        }

        if(!saveEntryInFinanceSourceSystemSyncFacade) {
            log.info("[FinanceSourceSystemSyncFacade][processAckEvent] financeSystemSyncDto: {}", financeSystemSyncDto);
            if (financeSystemSync == null) {
                financeSystemSync = new FinanceSourceSystemSync();
                log.info("processAckEvent creating new entry in financeSystemSync for " + financeSystemSyncDto.getEntityId());
                if (financeSystemSyncDto.getEvent().equals(FinanceSourceSystemSyncEvent.SALE_ORDER) || financeSystemSyncDto.getEvent().equals(FinanceSourceSystemSyncEvent.PACKING_SLIP)) {
                    List<UwOrder> uwOrderList = uwOrdersRepository.findByShippingPackageIdAndFacilityCode(financeSystemSyncDto.getEntityId(),financeSystemSyncDto.getFacilityCode());
                    if (!CollectionUtils.isEmpty(uwOrderList)) {
                        UwOrder uwOrder=uwOrderList.get(0);
                        financeSystemSync.setIncrementId((long) uwOrder.getIncrementId());
                        if (StringUtils.hasLength(uwOrder.getNavChannel())) {
                            financeSystemSync.setDescription("navChannel: " + uwOrder.getNavChannel());
                        }
                    }
                }
                if (!org.apache.commons.lang3.StringUtils.isBlank(financeSystemSyncDto.getPayload())) {
                    financeSystemSync.setPayload(new Gson().toJson(financeSystemSyncDto.getPayload()));
                }else{
                    List<Integer> uwItemIds=saleOrderUtil.getUwItemIds(financeSystemSyncDto.getEntityId(),financeSystemSyncDto.getFacilityCode());
                    if(!CollectionUtils.isEmpty(uwItemIds)){
                        financeSystemSync.setPayload(new Gson().toJson(uwItemIds));
                    }
                }

                financeSystemSync.setEvent(financeSystemSyncDto.getEvent());
                financeSystemSync.setEntityType(financeSystemSyncDto.getEntityType());
                financeSystemSync.setEntityId(financeSystemSyncDto.getEntityId());
                financeSystemSync.setSyncedToFinanceSystem(Boolean.FALSE);
                financeSystemSync.setRetryCount(0);
                financeSystemSync.setCreatedAt(new Date());
                financeSystemSync.setUpdatedAt(new Date());
                financeSystemSync.setCreatedBy("finance-consumer");
                financeSystemSync.setUpdatedBy("finance-consumer");
                financeSystemSync.setFacilityCode(financeSystemSyncDto.getFacilityCode());
                updateEventTime(financeSystemSyncDto.getEvent(), financeSystemSyncDto.getEntityType(), financeSystemSyncDto.getEntityId(), financeSystemSync);
                financeSystemSyncRepository.save(financeSystemSync);
                log.info("processAckEvent creating new entry created in financeSystemSync for " + financeSystemSyncDto.getEntityId());
            }
            if (financeSystemSyncDto.getUpdatedBy() == null) {
                financeSystemSyncDto.setUpdatedBy("finance-consumer");
            }
            if (!isEligibleForAck(financeSystemSyncDto.getEntityId(), financeSystemSyncDto.getErrorMessage())) {
                log.error("isEligibleForAck ack event for: {} ", financeSystemSyncDto);
                if (Objects.nonNull(financeSystemSyncDto.getErrorMessage())) {
                    financeSystemSync.setErrorMessage(financeSystemSyncDto.getErrorMessage());
                    financeSystemSync.setSyncedToFinanceSystem(false);
                }
                financeSystemSync.setUpdatedAt(new Date());
                financeSystemSyncRepository.save(financeSystemSync);
                return;
            }
            if ("finance-consumer".equals(financeSystemSyncDto.getUpdatedBy())
                    && Boolean.TRUE.equals(financeSystemSync.getSyncedToFinanceSystem())) {
                log.error("duplicate ack event for: {} ", financeSystemSyncDto);
                return;
            }
            if ("finance-adaptor".equals(financeSystemSyncDto.getUpdatedBy()) || "finance-consumer-payload-builder".equals(financeSystemSyncDto.getUpdatedBy())) {
                if (SaleOrderUtil.isSaleOrderSynced(financeSystemSyncDto)) {
                    financeSystemSync.setD365SyncStatus(Constants.Common.SUCCESS);
                } else {
                    financeSystemSync.setD365SyncStatus(financeSystemSyncDto.getD365SyncStatus());
                }
                if (financeSystemSyncDto.getD365SyncStatus() != null) {
                    financeSystemSync.setErrorMessage(financeSystemSyncDto.getErrorMessage());
                }
            }
            log.info("------Finance System D365 Status is: {} and retryCount is : {} ",financeSystemSyncDto.getD365SyncStatus(),financeSystemSyncDto.getRetryCount());
            if(Constants.Common.FAILURE.equals(financeSystemSyncDto.getD365SyncStatus()))
                financeSystemSync.setD365SyncSubStatus(D365SyncSubStatusEnum.FAILED);
            if(Constants.Common.SUCCESS.equals(financeSystemSyncDto.getD365SyncStatus()))
                financeSystemSync.setD365SyncSubStatus(D365SyncSubStatusEnum.COMPLETED);
            financeSystemSync.setSyncedToFinanceSystem(true);
            financeSystemSync.setUpdatedAt(new Date());
            financeSystemSyncRepository.save(financeSystemSync);
        }
    }

    public boolean isEligibleForAck(String entityId,String message) {
        if (Objects.nonNull(message)) {
            if (blockErrorMessageAckType.contains(failureEventHelper.findEventErrorType(message))) {
                log.info("isEligibleForAck {} skipping akc step : {}", false, entityId);
                return false;
            }
        }
        log.info("isEligibleForAck {} | financeSystemSyncDto: {}", true, entityId);
        return true;
    }

    public void publishDailyReport() {
        Map<FinanceSourceSystemSyncEvent, List<FinanceSourceSystemSyncDiscrepancy>> discrepanciesMap = new HashMap<>();
        Calendar fromDateCalendar = getPastCalendarDate(Calendar.HOUR, 48);
        Calendar toDateCalendar = getPastCalendarDate(Calendar.MINUTE, 60);
        FinanceSourceSystemSyncEvent[] events = FinanceSourceSystemSyncEvent.values();
        for (FinanceSourceSystemSyncEvent event : events) {
            int page = 0;
            while (true) {
                List<FinanceSourceSystemSync> nonSyncedFinanceSystemSyncDetails = getNonSyncedFinanceSystemSyncDetails(fromDateCalendar, toDateCalendar, event, page);
                if (CollectionUtils.isEmpty(nonSyncedFinanceSystemSyncDetails)) {
                    break;
                }
                processNonSyncedFinanceSystemSyncDetails(discrepanciesMap, event, nonSyncedFinanceSystemSyncDetails, true);
                page = page + nonSyncedFinanceSystemSyncDetails.size();
            }
        }
        financeSourceSyncEmailHandler.sendMail(discrepanciesMap);
    }

    public void publishDailySummaryReport() {

        Calendar fromDateCalendar = getPastCalendarDate(Calendar.HOUR, 48);
        Calendar toDateCalendar = getPastCalendarDate(Calendar.MINUTE, 30);

        List<FinanceConsumerLogsSummaryDto> bodyResult = getSummaryMailBodyDetails(fromDateCalendar, toDateCalendar);

        List<FinanceConsumerLogs> errorFinanceConsumerLogs = getSummaryMailAttachmentDetails(fromDateCalendar, toDateCalendar, bodyResult);

        financeSummaryReportEmailHandler.sendMail(bodyResult, errorFinanceConsumerLogs);
    }

    private List<FinanceConsumerLogs> getSummaryMailAttachmentDetails(Calendar fromDateCalendar, Calendar toDateCalendar, List<FinanceConsumerLogsSummaryDto> bodyResult) {
        List<String> orderTypes = bodyResult.stream()
                .map(FinanceConsumerLogsSummaryDto::getOrderType)
                .collect(Collectors.toList());

        Criteria criteriaQuery = Criteria.where("updatedAt").gte(fromDateCalendar.getTime()).lte(toDateCalendar.getTime())
                .and("orderType").in(orderTypes)
                .and("status").is("Failure");
        Query query = new Query();
        query.addCriteria(criteriaQuery);
        return mongoTemplate.find(query, FinanceConsumerLogs.class);
    }

    private List<FinanceConsumerLogsSummaryDto> getSummaryMailBodyDetails(Calendar fromDateCalendar, Calendar toDateCalendar) {
        Aggregation agg = newAggregation(
                match(
                        Criteria.where("updatedAt").gte(fromDateCalendar.getTime()).lte(toDateCalendar.getTime())
                                .and("orderType").in("Sales", "PackingSlip", "Item")
                ),
                group("status", "apiUrl", "orderType").count().as("count"),
                project("status", "apiUrl", "orderType", "count")
        );

        AggregationResults<FinanceConsumerLogsSummaryDto> groupResults
                = mongoTemplate.aggregate(agg, FinanceConsumerLogs.class, FinanceConsumerLogsSummaryDto.class);

        return groupResults.getMappedResults();
    }

    private void processNonSyncedFinanceSystemSyncDetails(Map<FinanceSourceSystemSyncEvent, List<FinanceSourceSystemSyncDiscrepancy>> discrepanciesMap,
                                                          FinanceSourceSystemSyncEvent event,
                                                          List<FinanceSourceSystemSync> nonSyncedFinanceSystemSyncDetails, boolean addToDiscrepancyList) {
        for (FinanceSourceSystemSync financeSystemSync : nonSyncedFinanceSystemSyncDetails) {
            if (rePushEventIfApplicable(event, financeSystemSync)) {
                continue;
            }
            if (addToDiscrepancyList) {
                addToDiscrepancies(discrepanciesMap, event, financeSystemSync);
            }
        }
    }

    private void addToDiscrepancies(Map<FinanceSourceSystemSyncEvent, List<FinanceSourceSystemSyncDiscrepancy>> discrepanciesMap, FinanceSourceSystemSyncEvent event, FinanceSourceSystemSync financeSystemSync) {
        FinanceSourceSystemSyncDiscrepancy discrepancy = new FinanceSourceSystemSyncDiscrepancy(
                financeSystemSync.getEvent(),
                financeSystemSync.getEntityType(),
                financeSystemSync.getEntityId(),
                financeSystemSync.getErrorMessage()
        );
        List<FinanceSourceSystemSyncDiscrepancy> discrepancies = discrepanciesMap.getOrDefault(event, new ArrayList<>());
        discrepancies.add(discrepancy);
        discrepanciesMap.put(event, discrepancies);
    }

    private boolean rePushEventIfApplicable(FinanceSourceSystemSyncEvent event, FinanceSourceSystemSync financeSystemSync) {
        String maxRetryCount = environment.getProperty(FINANCE_SOURCE_SYSTEM_SYNC_MAX_RETRY_COUNT);
        Integer maxRetryCountInt = maxRetryCount == null ? 5 : Integer.parseInt(maxRetryCount);
        if (financeSystemSync.getRetryCount() < maxRetryCountInt) {
            try {
                log.info("[FinanceSourceSystemSyncFacade][rePushEventIfApplicable] re-pushing source sync back to topic: {} for payload: {}", FINANCE_SYSTEM_SYNC_EVENT_TO_KAFKA_TOPIC_MAP.get(event), financeSystemSync.getPayload());
                financeSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(event,
                                                                                                                   financeSystemSync.getEntityType(),
                                                                                                                   financeSystemSync.getEntityId(),
                                                                                                                   financeSystemSync.getFacilityCode());
                financeSystemSync.setUpdatedBy("finance-consumer");
                financeSystemSync.setUpdatedAt(new Date());
                financeSystemSync.setRetryCount(financeSystemSync.getRetryCount() + 1);
                financeSystemSyncRepository.save(financeSystemSync);

                kafkaTemplate.send(
                        FINANCE_SYSTEM_SYNC_EVENT_TO_KAFKA_TOPIC_MAP.get(event),
                        financeSystemSync.getPayload()
                );
                return true;
            } catch (Exception e) {
                log.error("error while updating retry count or pushing event again for failed cases for {}", financeSystemSync);
                return true;
            }
        }
        return false;
    }

    public List<FinanceSourceSystemSync> getNonSyncedFinanceSystemSyncDetails(Calendar fromDateCalendar,
                                                                              Calendar toDateCalendar, FinanceSourceSystemSyncEvent event, int page) {
//        PageRequest pageRequest = PageRequest.of(page, 1000);
        // String fromDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(fromDateCalendar.getTime());
        // String toDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(toDateCalendar.getTime());
        log.info("getNonSyncedFinanceSystemSyncDetails");
        try {
            CriteriaBuilder criteriaBuilder = inventorySlaveTransactionManager.getCriteriaBuilder();
            CriteriaQuery<FinanceSourceSystemSync> cq = criteriaBuilder.createQuery(FinanceSourceSystemSync.class);
            Root<FinanceSourceSystemSync> financeSourceSystemSync = cq.from(FinanceSourceSystemSync.class);
            Predicate eventPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("event"), event);
            Predicate updatedAtPredicate = criteriaBuilder.between(financeSourceSystemSync.get("updatedAt"),
                    fromDateCalendar.getTime(), toDateCalendar.getTime());
            Predicate notSyncedToD365FinanceSystemPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("syncedToFinanceSystem"), 0);
            Predicate syncedToFinanceSystemPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("syncedToFinanceSystem"), 1);
            Predicate statusPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("d365SyncStatus"), "Failure");
            Predicate failedAtD365Predicate = criteriaBuilder.and(syncedToFinanceSystemPredicate, statusPredicate);
            Predicate failedPredicate = criteriaBuilder.or(notSyncedToD365FinanceSystemPredicate, failedAtD365Predicate);
            cq.where(eventPredicate, updatedAtPredicate, failedPredicate);
            //log.info("getNonSyncedFinanceSystemSyncDetails cq"+new Gson().toJson(cq));
            return inventorySlaveTransactionManager.createQuery(cq).getResultList();
        }
        catch (Exception e){
            log.error("getNonSyncedFinanceSystemSyncDetails error ",e);
            return new ArrayList<>();
        }
    }


    public List<FinanceSourceSystemSync> getOnlyNonSyncedFinanceSystemSyncDetails(Calendar fromDateCalendar,
                                                                                  Calendar toDateCalendar, FinanceSourceSystemSyncEvent event, int page, int allowedSize) {
//        PageRequest pageRequest = PageRequest.of(page, 1000);
        // String fromDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(fromDateCalendar.getTime());
        // String toDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(toDateCalendar.getTime());
        log.info("getOnlyNonSyncedFinanceSystemSyncDetails");
        try {
            CriteriaBuilder criteriaBuilder = inventorySlaveTransactionManager.getCriteriaBuilder();
            CriteriaQuery<FinanceSourceSystemSync> cq = criteriaBuilder.createQuery(FinanceSourceSystemSync.class);
            Root<FinanceSourceSystemSync> financeSourceSystemSync = cq.from(FinanceSourceSystemSync.class);
            Predicate eventPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("event"), event);
            Predicate updatedAtPredicate = criteriaBuilder.between(financeSourceSystemSync.get("updatedAt"),
                    fromDateCalendar.getTime(), toDateCalendar.getTime());
            Predicate notSyncedToD365FinanceSystemPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("syncedToFinanceSystem"), 0);
//            Predicate syncedToFinanceSystemPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("syncedToFinanceSystem"), 1);
//            Predicate statusPredicate = criteriaBuilder.equal(financeSourceSystemSync.get("d365SyncStatus"), "Failure");
//            Predicate failedAtD365Predicate = criteriaBuilder.and(syncedToFinanceSystemPredicate, statusPredicate);
//            Predicate failedPredicate = criteriaBuilder.or(notSyncedToD365FinanceSystemPredicate, failedAtD365Predicate);
            cq.where(eventPredicate, updatedAtPredicate, notSyncedToD365FinanceSystemPredicate);
//            log.info("getOnlyNonSyncedFinanceSystemSyncDetails cq"+new Gson().toJson(cq));
            return inventorySlaveTransactionManager.createQuery(cq).setMaxResults(allowedSize).getResultList();
        }
        catch (Exception e){
            log.error("getOnlyNonSyncedFinanceSystemSyncDetails error ",e);
            return new ArrayList<>();
        }
    }

    public Calendar getPastCalendarDate(int hour, int x) {
        Calendar fromDateCalendar = Calendar.getInstance();
        fromDateCalendar.setTime(new Date());
        fromDateCalendar.add(hour, -x);
        return fromDateCalendar;
    }

    public void retryNonSyncedRecords(FinanceSourceSystemRetryRequest financeSourceSystemRetryRequest) {
        String allowedSizeString = environment.getProperty(FINANCE_SOURCE_SYSTEM_SYNC_ALLOWED_SIZE);
        int allowedSize = financeSourceSystemRetryRequest.getMaxSize()!= 0 ? financeSourceSystemRetryRequest.getMaxSize() : (allowedSizeString == null ? 1 : Integer.parseInt(allowedSizeString));
        String allowedEventsString = environment.getProperty(FINANCE_SOURCE_SYSTEM_SYNC_ALLOWED_EVENTS);
        List<String> allowedEvents = allowedEventsString == null ? Collections.emptyList() : Arrays.asList(allowedEventsString.split(","));
        Map<FinanceSourceSystemSyncEvent, List<FinanceSourceSystemSyncDiscrepancy>> discrepanciesMap = new HashMap<>();
        Calendar fromDateCalendar = null;
        Calendar toDateCalendar =null;
        if(financeSourceSystemRetryRequest.getFromDate()!= null){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(financeSourceSystemRetryRequest.getFromDate());
            fromDateCalendar = calendar;
        }
        if(financeSourceSystemRetryRequest.getToDate()!= null){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(financeSourceSystemRetryRequest.getToDate());
            toDateCalendar = calendar;
        }
        fromDateCalendar = fromDateCalendar == null ? getPastCalendarDate(Calendar.HOUR, 48) : fromDateCalendar;
        toDateCalendar = toDateCalendar == null ? getPastCalendarDate(Calendar.MINUTE, 30) :toDateCalendar;


        FinanceSourceSystemSyncEvent[] events = FinanceSourceSystemSyncEvent.values();
        for (FinanceSourceSystemSyncEvent event : events) {
            if (!allowedEvents.contains(event.name())) {
                log.info("skipping retry of event for: {}", event.name());
                continue;
            }
            int page = 0;
            while (true) {
                log.info("nonSyncedFinanceSystemSyncDetails page: {}",page);
                List<FinanceSourceSystemSync> nonSyncedFinanceSystemSyncDetails;
                if(financeSourceSystemRetryRequest.isSynced()){
                    nonSyncedFinanceSystemSyncDetails = getNonSyncedFinanceSystemSyncDetails(fromDateCalendar, toDateCalendar, event, page);
                }else {
                    nonSyncedFinanceSystemSyncDetails = getOnlyNonSyncedFinanceSystemSyncDetails(fromDateCalendar, toDateCalendar, event, page, allowedSize);
                }
                log.info("nonSyncedFinanceSystemSyncDetails "+new Gson().toJson(nonSyncedFinanceSystemSyncDetails));
                log.info("nonSyncedFinanceSystemSyncDetails size "+nonSyncedFinanceSystemSyncDetails.size());
                if (CollectionUtils.isEmpty(nonSyncedFinanceSystemSyncDetails) || page >= allowedSize) {
                    log.info("skipping retry of event for: {} due to no record or page limit reached page: {} limit: {}",
                            event.name(), page, allowedSize);
                    break;
                }
                nonSyncedFinanceSystemSyncDetails=
                        nonSyncedFinanceSystemSyncDetails.stream().filter(item-> this.isEligibleForAck(item.getEntityId(),
                                item.getErrorMessage())).collect(Collectors.toList());
                processNonSyncedFinanceSystemSyncDetails(discrepanciesMap, event, nonSyncedFinanceSystemSyncDetails, false);
                page = page + nonSyncedFinanceSystemSyncDetails.size();
            }
        }
    }

    public void missingInvoicefailureEventRetryJob() {
        try {
            String allowedSizeString =
                    environment.getProperty(FinanceSourceSystemSyncFacade.FINANCE_SOURCE_SYSTEM_SYNC_INVOICE_ALLOWED_SIZE);
            String allowedHourString =
                    environment.getProperty(FinanceSourceSystemSyncFacade.FINANCE_SOURCE_SYSTEM_SYNC_INVOICE_ALLOWED_HOUR);
            int allowedSize = Integer.parseInt(allowedSizeString);
            int allowedHour = Integer.parseInt(allowedHourString);
            FinanceSourceSystemRetryRequest financeSourceSystemRetryRequest = new FinanceSourceSystemRetryRequest();
            Calendar fromDateCalendar = null;
            Calendar toDateCalendar = null;
            fromDateCalendar = this.getPastCalendarDate(Calendar.HOUR, allowedHour);
            toDateCalendar = this.getPastCalendarDate(Calendar.MINUTE, 30);
            int page = 0;
            while (true) {
                log.info("missingInvoicefailureEventRetryJob page: {}", page);
                List<FinanceSourceSystemSync> nonSyncedFinanceSystemSyncDetails =
                        this.getOnlyNonSyncedFinanceSystemSyncDetails(fromDateCalendar,
                                toDateCalendar, FinanceSourceSystemSyncEvent.PACKING_SLIP, page, allowedSize);
                log.info("missingInvoicefailureEventRetryJob size :{}", nonSyncedFinanceSystemSyncDetails.size());
                nonSyncedFinanceSystemSyncDetails = nonSyncedFinanceSystemSyncDetails.stream().filter(item ->
                        failureEventHelper.findEventErrorType(item.getErrorMessage()) == EventErrorType.INVOICE_NOT_PRESENT).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(nonSyncedFinanceSystemSyncDetails) || page >= allowedSize) {
                    log.info("missingInvoicefailureEventRetryJob skip {}", page);
                    break;
                }
                page = page + allowedSize;
                log.info("missingInvoicefailureEventRetryJob after filter size :{}",
                        nonSyncedFinanceSystemSyncDetails.size());
                log.info("missingInvoicefailureEventRetryJob :{}", nonSyncedFinanceSystemSyncDetails);
                failureEventSyncFacade.processFailureFinanceSystem(nonSyncedFinanceSystemSyncDetails);
            }
        } catch (Exception ex) {
            log.info("missingInvoicefailureEventRetryJob error:{}", ex.getMessage(), ex);
        }
    }

    public void processNonSyncedMovementJournalPackingSlip(FinanceSourceSystemSyncDto financeSourceSystemSyncDto) {
        log.error("[FinanceSourceSystemSyncFacade][processNonSyncedMovementJournalPackingSlip] processing non-synced movement journal packing slip dto {}",financeSourceSystemSyncDto);
        try {
            if (isProcessSerialNumberPackingSlip && isEligibleForProcessing(financeSourceSystemSyncDto)) {

                FinanceSourceSystemSync financeSystemSync = financeSystemSyncRepository.findByEventAndEntityTypeAndEntityIdAndFacilityCode(
                        financeSourceSystemSyncDto.getEvent(),
                        financeSourceSystemSyncDto.getEntityType(),
                        financeSourceSystemSyncDto.getEntityId(),
                        financeSourceSystemSyncDto.getFacilityCode()
                );

                if (Objects.nonNull(financeSystemSync) && hasValidPayloadAndErrorMessage(financeSystemSync)) {
                        executeSerialNumberErrorStrategy(financeSystemSync);
                }
            }
        } catch (Exception e) {
            log.error("[FinanceSourceSystemSyncFacade][processNonSyncedMovementJournalPackingSlip] Error processing non-synced movement journal packing slip dto {}",financeSourceSystemSyncDto, e);
        }
    }
    public boolean isEligibleForProcessing(FinanceSourceSystemSyncDto financeSourceSystemSyncDto) {
        return Constants.Common.FAILURE.equals(financeSourceSystemSyncDto.getD365SyncStatus()) &&
                FinanceSourceSystemSyncEvent.PACKING_SLIP.equals(financeSourceSystemSyncDto.getEvent()) &&
                !StringUtils.isEmpty(financeSourceSystemSyncDto.getEntityId()) &&
                !StringUtils.isEmpty(financeSourceSystemSyncDto.getFacilityCode()) &&
                inventoryCorrectionService.isSerialNumberError(financeSourceSystemSyncDto.getErrorMessage());
    }
    public boolean hasValidPayloadAndErrorMessage(FinanceSourceSystemSync financeSystemSync) {
        return !StringUtils.isEmpty(financeSystemSync.getPayload()) &&
                !StringUtils.isEmpty(financeSystemSync.getErrorMessage());
    }
    public void executeSerialNumberErrorStrategy(FinanceSourceSystemSync financeSystemSync) {
        FinanceSourceSystemSyncDto financeSourceSystemSyncDto = new FinanceSourceSystemSyncDto();
        BeanUtils.copyProperties(financeSystemSync, financeSourceSystemSyncDto);
        serialNumberErrorStrategy.execute(financeSourceSystemSyncDto);
    }
    public void sendD365FailureReport(FinanceServiceEventTypes financeServiceEventTypes){
        try {
            LocalDate startDate = LocalDate.now().minusDays(1).atStartOfDay().toLocalDate();
            LocalDate endDate=LocalDate.now().atStartOfDay().toLocalDate();

            StringBuilder sb = new StringBuilder();
            sb = addD365FailedCSVHeader(sb);
            int page = 0;
            List<FinanceSourceSystemSync> d365FailedList;
            Pageable pageable;
            do {
                pageable = PageRequest.of(page, pageLimit);

                d365FailedList = financeSystemSyncReadRepository.findAllByEventTimeBetweenAndEventAndD365SyncStatus(Constants.Common.FAILURE, startDate, endDate, financeServiceEventTypes.name(), pageable);

                if (!CollectionUtils.isEmpty(d365FailedList)){
                    sb = addD365FailedLines(sb, d365FailedList, financeServiceEventTypes);
                }

                page++;
            } while (!CollectionUtils.isEmpty(d365FailedList));

            commonUtils.sendReport(sb.toString().getBytes(StandardCharsets.UTF_8),financeServiceEventTypes,mailSendFrom,mailSendTo,mailCC);

        }catch (Exception exception){
            log.error("[FinanceSourceSystemSyncFacade][sendD365FailureReport]Exception Occurred While preparing the sops report failure event {}",financeServiceEventTypes,exception);
        }
    }
    public StringBuilder addD365FailedCSVHeader(StringBuilder sb) {
        String[] headers = new String[]{"Event", "EntityId", "FacilityCode", "OrderId", "Creation Date", "D365 Status","D365 Response"};
        for (int i = 0; i < headers.length; i++) {
            sb.append(headers[i]);
            sb.append(i == headers.length - 1 ? "\n" : ",");
        }
        return sb;
    }
    public StringBuilder addD365FailedLines(StringBuilder sb, List<FinanceSourceSystemSync> d365FailedList,FinanceServiceEventTypes financeServiceEventTypes) {
        for (FinanceSourceSystemSync financeSourceSystemSync : d365FailedList) {
            sb.append(financeServiceEventTypes.name()).append(",");
            sb.append(financeSourceSystemSync.getEntityId()).append(",");
            sb.append(financeSourceSystemSync.getFacilityCode()).append(",");
            sb.append(financeSourceSystemSync.getIncrementId()).append(",");
            sb.append(financeSourceSystemSync.getEventTime()).append(",");
            sb.append(financeSourceSystemSync.getD365SyncStatus()).append(",");
            sb.append(cleanValueForCsv(financeSourceSystemSync.getErrorMessage())).append(",");
            sb.append("\n");
        }
        return sb;
    }
    private String cleanValueForCsv(String value) {
        if(org.apache.commons.lang3.StringUtils.isBlank(value))
            return null;
        return value.replaceAll("[^a-zA-Z0-9]", " ").trim();
    }
}
