package com.lenskart.financeConsumer.facade;

import com.lenskart.financeConsumer.model.financeDb.GrnReport;
import com.lenskart.financeConsumer.model.financeDb.GrnPidReport;
import com.lenskart.financeConsumer.v2.dto.GenericResponseDto;
import com.lenskart.financeConsumer.v2.dto.MetaDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Component
public class GrnReportFacade {

    private static final String dateFormatPattern = "yyyy-MM-dd HH:mm:ss.S";

    public GrnReport convertToGrnLevelReport(Object[] object) {
        return GrnReport.builder()
                .grnCode(String.valueOf(object[0]))
                .grnDate(LocalDateTime.parse(String.valueOf(object[1]), DateTimeFormatter.ofPattern(dateFormatPattern)))
                .grnCount(Long.parseLong(String.valueOf(object[2])))
                .grnVendorCost(new BigDecimal(String.valueOf(object[3])).setScale(4, RoundingMode.CEILING))
                .grnCostWithTaxes(new BigDecimal(String.valueOf(object[4])).setScale(4, RoundingMode.CEILING))
                .poNum(String.valueOf(object[5]))
                .poDate(LocalDateTime.parse(String.valueOf(object[6]), DateTimeFormatter.ofPattern(dateFormatPattern)))
                .vendorInvoiceNumber(String.valueOf(object[7]))
                .vendorName(String.valueOf(object[8]))
                .vendorId(String.valueOf(object[9]))
                .status(String.valueOf(object[10]))
                .currency(String.valueOf(object[11]))
                .currencyConvRate(new BigDecimal(String.valueOf(object[12])).setScale(4, RoundingMode.CEILING))
                .grnFailureReason(String.valueOf(object[13]))
                .build();
    }

    public GrnPidReport convertToPidLevelReport(Object[] object) {
        return GrnPidReport.builder()
                .grnCode(String.valueOf(object[0]))
                .grnDate(LocalDateTime.parse(String.valueOf(object[1]), DateTimeFormatter.ofPattern(dateFormatPattern)))
                .pid(Long.parseLong(String.valueOf(object[2])))
                .grnPidCount(Long.parseLong(String.valueOf(object[3])))
                .customsCharges(new BigDecimal(String.valueOf(object[4])).setScale(4, RoundingMode.CEILING))
                .freightCharges(new BigDecimal(String.valueOf(object[5])).setScale(4, RoundingMode.CEILING))
                .chaCharges(new BigDecimal(String.valueOf(object[6])).setScale(4, RoundingMode.CEILING))
                .insuranceCost(new BigDecimal(String.valueOf(object[7])).setScale(4, RoundingMode.CEILING))
                .freightAndFwCost(new BigDecimal(String.valueOf(object[8])).setScale(4, RoundingMode.CEILING))
                .grnPidVendorCost(new BigDecimal(String.valueOf(object[9])).setScale(4, RoundingMode.CEILING))
                .grnPidCostWithTaxes(new BigDecimal(String.valueOf(object[10])).setScale(4, RoundingMode.CEILING))
                .poNum(String.valueOf(object[11]))
                .vendorInvoiceNumber(String.valueOf(object[12]))
                .vendorName(String.valueOf(object[13]))
                .vendorId(String.valueOf(object[14]))
                .currency(String.valueOf(object[15]))
                .currencyConvRate(new BigDecimal(String.valueOf(object[16])).setScale(4, RoundingMode.CEILING))
                .status(String.valueOf(object[17]))
                .build();
    }

    public StringBuilder addGrnLevelReportLines(StringBuilder sb, List<GrnReport> grnLevelReportList) {
        for (int i = 0; i < grnLevelReportList.size(); i++) {
            GrnReport reportLine = grnLevelReportList.get(i);
            sb.append(cleanCommaForCsv(reportLine.getGrnCode())).append(",");
            sb.append(reportLine.getGrnDate()).append(",");
            sb.append(reportLine.getGrnCount()).append(",");
            sb.append(reportLine.getGrnVendorCost()).append(",");
            sb.append(reportLine.getGrnCostWithTaxes()).append(",");
            sb.append(cleanCommaForCsv(reportLine.getPoNum())).append(",");
            sb.append(reportLine.getPoDate()).append(",");
            sb.append(cleanCommaForCsv(reportLine.getVendorInvoiceNumber())).append(",");
            sb.append(cleanValueForCsv(reportLine.getVendorName())).append(",");
            sb.append(cleanCommaForCsv(reportLine.getVendorId())).append(",");
            sb.append(cleanCommaForCsv(reportLine.getStatus())).append(",");
            sb.append(cleanCommaForCsv(reportLine.getCurrency())).append(",");
            sb.append(reportLine.getCurrencyConvRate()).append(",");
            sb.append(cleanValueForCsv(reportLine.getGrnFailureReason())).append(",");
            sb.append("\n");
        }
        return sb;
    }

    public StringBuilder addPidLevelReportLines(StringBuilder sb, List<GrnPidReport> pidLevelReportList) {
        for (int i = 0; i < pidLevelReportList.size(); i++) {
            GrnPidReport reportLine = pidLevelReportList.get(i);
            sb.append(cleanCommaForCsv(reportLine.getGrnCode())).append(",");
            sb.append(reportLine.getGrnDate()).append(",");
            sb.append(reportLine.getPid()).append(",");
            sb.append(reportLine.getGrnPidCount()).append(",");
            sb.append(reportLine.getCustomsCharges()).append(",");
            sb.append(reportLine.getFreightCharges()).append(",");
            sb.append(reportLine.getChaCharges()).append(",");
            sb.append(reportLine.getInsuranceCost()).append(",");
            sb.append(reportLine.getFreightAndFwCost()).append(",");
            sb.append(reportLine.getGrnPidVendorCost()).append(",");
            sb.append(reportLine.getGrnPidCostWithTaxes()).append(",");
            sb.append(cleanCommaForCsv(reportLine.getPoNum())).append(",");
            sb.append(cleanCommaForCsv(reportLine.getVendorInvoiceNumber())).append(",");
            sb.append(cleanValueForCsv(reportLine.getVendorId())).append(",");
            sb.append(cleanValueForCsv(reportLine.getVendorName())).append(",");
            sb.append(cleanCommaForCsv(reportLine.getCurrency())).append(",");
            sb.append(reportLine.getCurrencyConvRate()).append(",");
            sb.append(cleanCommaForCsv(reportLine.getStatus())).append(",");
            sb.append("\n");
        }
        return sb;
    }

    public StringBuilder addGrnLevelHeaders(StringBuilder sb) {
        String[] grnHeaders = new String[]{"grn_code", "grn_date", "grn_count", "grn_vendor_cost", "grn_vendor_cost_with_tax", "po_num", "po_date", "vendor_invoice_number", "vendor_name", "vendor_id", "status", "currency", "currency_conv_rate", "grn_failure_reason"};
        for (int i = 0; i < grnHeaders.length; i++) {
            sb.append(grnHeaders[i]);
            sb.append(i == grnHeaders.length - 1 ? "\n" : ",");
        }
        return sb;
    }

    public StringBuilder addPidLevelHeaders(StringBuilder sb) {
        String[] pidHeaders = new String[]{"grn_code", "grn_date", "pid", "grn_pid_count", "customs_charges", "freight_charges", "cha_charges", "insurance_cost", "freight_and_fw_cost", "grn_pid_vendor_cost", "grn_pid_cost_with_taxes", "po_num", "vendor_invoice_number", "vendor_id", "vendor_name", "currency", "currency_conv_rate", "status"};
        for (int i = 0; i < pidHeaders.length; i++) {
            sb.append(pidHeaders[i]);
            sb.append(i == pidHeaders.length - 1 ? "\n" : ",");
        }
        return sb;
    }

    public GenericResponseDto getReportGenerationResponse(String message, String displayMessage) {
        return GenericResponseDto.builder()
                .meta(MetaDto.builder()
                              .message(message)
                              .displayMessage(displayMessage)
                              .build())
                .build();
    }

    public String buildMailBody(String grnReportUrl, String pidReportUrl) {
        StringBuilder html = new StringBuilder("<html><body><h5>Hi team, sharing NEXS GRN and PID level weekly report</h5><table border='1' width='700'><tr><th style='background-color:yellow'>Report Name</th><th style='background-color:yellow'>Url</th></tr>");
        html.append("<tr><td align='middle'>")
                .append("GRN Level Report")
                .append("</td><td align='middle'>")
                .append(grnReportUrl)
                .append("</td></tr>");
        html.append("<tr><td align='middle'>")
                .append("GRN PID Level Report")
                .append("</td><td align='middle'>")
                .append(pidReportUrl)
                .append("</td></tr>");
        html.append("</table>");
        html.append("<br></br>");
        html.append("<br></br>");
        html.append("<h5>Note : Please connect to Lenskart VPN before downloading.</h5>");
        html.append("<br></br>");
        html.append("<p>Thanks and Regards</p>");
        html.append("<p>Finance Tech Team</p></body></html>");

        return html.toString();
    }

    private String cleanValueForCsv(String value) {
        return value.replaceAll("[^a-zA-Z0-9]", " ").trim();
    }

    private String cleanCommaForCsv(String value) {
        return value.replaceAll(",", "").trim();
    }

}
