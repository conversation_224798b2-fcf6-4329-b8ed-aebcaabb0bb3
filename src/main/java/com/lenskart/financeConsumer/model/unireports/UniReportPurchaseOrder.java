package com.lenskart.financeConsumer.model.unireports;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.IdClass;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@Table(name ="uniReport_purchase_order", catalog = "unireports")
@AllArgsConstructor
@NoArgsConstructor
@IdClass(UnireportPurchaseOrderPkey.class)
public class UniReportPurchaseOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "purchaseOrderCode")
    private String purchaseOrderCode;

    @Id
    @Column(name = "itemtypeSku")
    private Integer itemTypeSku;

    @Column(name = "vendorCode")
    private String vendorCode;

    @Column(name = "deliveryDate")
    private Date deliveryDate;

    @Column(name = "facilityCode")
    private String facilityCode;

    @Column(name = "created")
    private Date created;

    @Column(name = "purchaseOrderStatus")
    private String purchaseOrderStatus;

    @Column(name = "purchaseOrderCreatedBy")
    private String purchaseOrderCreatedBy;

    @Column(name = "fld_row_last_update_on")
    private Date lastUpdated;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "unitPrice")
    private Double unitPrice;

    @Column(name = "recieveQuantity")
    private Integer recieveQuantity;

    @Column(name = "rejectedQuantity")
    private Integer rejectedQuantity;

    @Column(name = "pendingQuantity")
    private Integer pendingQuantity;


    @Override
    public String toString()
    {
        return "PO{" +
                "purchaseOrderCode=" + purchaseOrderCode +
                ", vendorCode='" + vendorCode + '\'' +
                ", facilityCode=" + facilityCode +
                ", created='" + created + '\'' +
                ", purchaseOrderStatus=" + purchaseOrderStatus +
                ", purchaseOrderCreatedBy='" + purchaseOrderCreatedBy + '\'' +
                ", lastUpdated='" + lastUpdated + '\'' +
                ", itemTypeSku='" + itemTypeSku + '\'' +
                ", quantity='" + quantity + '\'' +
                ", unitPrice='" + unitPrice + '\'' +
                ", recieveQuantity='" + recieveQuantity + '\'' +
                ", rejectedQuantity='" + rejectedQuantity + '\'' +
                ", pendingQuantity='" + pendingQuantity + '\'' +
                '}';
    }
}
