package com.lenskart.financeConsumer.model.financeDb;



import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

@ToString
@AllArgsConstructor
@Getter
@Setter
@Builder
@Entity
@Table(name = "sales_order_line")
public class SalesOrderLine {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private long id;
    @Column(name = "line_number")
    public Long lineNumber;
    @Column(name = "item_number")
    public String itemNumber;
    @Column(name = "qty_ordered")
    public Integer qtyOrdered;
    @Column(name = "sales_price")
    public Double salesPrice;
    @ManyToOne
    @JoinColumn(name = "sales_order_header_id")
    public SalesOrderHeaderEntity salesOrderHeader;
    @Column(name = "line_discount_amount")
    public Double lineDiscountAmount;
    @Column(name = "delivery_type")
    public String deliveryType;
    @Column(name = "sourcing_vendor")
    public String sourcingVendor;
    @Column(name = "delivery_mode_code")
    public String deliveryModeCode;
    @Column(name = "invent_location_id")
    public String inventLocationId;
    @Column(name = "invent_site_id")
    public String inventSiteId;
    @Column(name = "confirmed_receipt_date")
    public LocalDateTime confirmedReceiptDate;
    @Column(name = "confirmed_ship_date")
    public LocalDateTime confirmedShipDate;
    @Column(name = "units")
    public String units;
    @Column(name = "cost_center")
    public String costCenter;
    @Column(name = "stores")
    public String stores;
    @Column(name = "sales_channel")
    public String salesChannel;
    @Column(name = "sub_channel")
    public String subChannel;
    @Column(name = "partner_type")
    public String partnerType;
    @Column(name = "item_classification")
    public String itemClassification;
    @Column(name = "brand")
    public String brand;
    @Column(name = "employee")
    public String employee;
    @Column(name = "magento_item_id")
    public Integer magentoItemId;
    @Column(name = "purch_price")
    public Double purchPrice;
    @Column(name = "hsn_code")
    public String hsnCode;
    @Column(name = "sales_order_item_code")
    public String salesOrderItemCode;
    @Column(name = "tax_rate_type")
    public Double taxRateType;
    @Column(name = "item_sales_tax_grp")
    public Double itemSalesTaxGrp;
    @Column(name = "sales_tax_grp")
    public Double salesTaxGrp;
    @Column(name = "item_template_name")
    public String itemTemplateName;
    @Column(name = "sac_code")
    public String sacCode;
    @Column(name = "sales_pool")
    public String salesPool;
    @Column(name = "returned_order_no")
    public String returnedOrderNo;
    @Column(name = "original_sale_order_no")
    public Integer originalSaleOrderNo;
    @Column(name = "original_sale_order_line_no")
    public Integer originalSaleOrderLineNo;
    @Column(name = "return_cost_price")
    public Double returnCostPrice;
    @Column(name = "lk_reference_wh")
    public String lkReferenceWh;
    @Column(name = "barcode")
    private String barcode;
    @Column(name = "lk_purchase_price")
    private Double lkPurchasePrice;
    @Column(name = "exempt")
    private String exempt;
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    @Version
    @Column(name = "version")
    private long version;

    public SalesOrderLine(){
        this.lineNumber = 0L;
        this.qtyOrdered = 0;
        this.salesPrice = 0.00d;
        this.lineDiscountAmount = 0.00d;
        this.originalSaleOrderNo = 0;
        this.originalSaleOrderLineNo = 0;
    }


}
