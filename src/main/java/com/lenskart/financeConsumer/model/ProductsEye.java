package com.lenskart.financeConsumer.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "products_eye", catalog = "inventory")
public class ProductsEye implements Serializable {
    private static final long serialVersionUID = 7153465824760053247L;

    @Id
    @Column(name = "product_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer productId;

    @Column(name = "sph")
    private BigDecimal sph;

    @Column(name = "cyl")
    private BigDecimal cyl;

    @Column(name = "ap")
    private String ap;

    @Column(name = "lens_id")
    private Integer lensId;

    @Column(name = "shell_id")
    private Integer shellId;

    @Column(name = "pd")
    private Double pd;

    @Column(name = "bottom_distance")
    private Integer bottomDistance;

    @Column(name = "top_distance")
    private Integer topDistance;

    @Column(name = "edge_distance")
    private Integer edgeDistance;

    @Column(name = "near_pd")
    private Double nearPd;

    @Column(name = "effective_dia")
    private Integer effectiveDia;

    @Column(name = "axis")
    private String axis;

    @Column(name = "axis_type")
    private String axisType;

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public BigDecimal getSph() {
        return sph;
    }

    public void setSph(BigDecimal sph) {
        this.sph = sph;
    }

    public BigDecimal getCyl() {
        return cyl;
    }

    public void setCyl(BigDecimal cyl) {
        this.cyl = cyl;
    }

    public String getAp() {
        return ap;
    }

    public void setAp(String ap) {
        this.ap = ap;
    }

    public Integer getLensId() {
        return lensId;
    }

    public void setLensId(Integer lensId) {
        this.lensId = lensId;
    }

    public Integer getShellId() {
        return shellId;
    }

    public void setShellId(Integer shellId) {
        this.shellId = shellId;
    }

    public Double getPd() {
        return pd;
    }

    public void setPd(Double pd) {
        this.pd = pd;
    }

    public Integer getBottomDistance() {
        return bottomDistance;
    }

    public void setBottomDistance(Integer bottomDistance) {
        this.bottomDistance = bottomDistance;
    }

    public Integer getTopDistance() {
        return topDistance;
    }

    public void setTopDistance(Integer topDistance) {
        this.topDistance = topDistance;
    }

    public Integer getEdgeDistance() {
        return edgeDistance;
    }

    public void setEdgeDistance(Integer edgeDistance) {
        this.edgeDistance = edgeDistance;
    }

    public Double getNearPd() {
        return nearPd;
    }

    public void setNearPd(Double nearPd) {
        this.nearPd = nearPd;
    }

    public Integer getEffectiveDia() {
        return effectiveDia;
    }

    public void setEffectiveDia(Integer effectiveDia) {
        this.effectiveDia = effectiveDia;
    }

    public String getAxis() {
        return axis;
    }

    public void setAxis(String axis) {
        this.axis = axis;
    }

    public String getAxisType() {
        return axisType;
    }

    public void setAxisType(String axisType) {
        this.axisType = axisType;
    }

    @Override
    public String toString() {
        return "ProductsEye [productId=" + productId + ", sph=" + sph + ", cyl=" + cyl + ", ap=" + ap + ", lensId="
                + lensId + ", shellId=" + shellId + ", pd=" + pd + ", bottomDistance=" + bottomDistance
                + ", topDistance=" + topDistance + ", edgeDistance=" + edgeDistance + ", nearPd=" + nearPd
                + ", effectiveDia=" + effectiveDia + ", axis=" + axis + ", axisType=" + axisType + "]";
    }

}
