package com.lenskart.financeConsumer.model.pos;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name ="store_payout", catalog = "POS")
public class StorePayoutEntity implements Serializable {

    private static final long serialVersionUID = -378757034525451521L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name="facility_code", nullable = false, length = 10)
    private String facilityCode;

    @Column(name="reference_number", nullable = false, length = 128)
    private String referenceNumber;

    @Column(name="amount")
    private double amount;

    @Column(name="payout_type", nullable = false, length = 128)
    private String payoutType;


}

