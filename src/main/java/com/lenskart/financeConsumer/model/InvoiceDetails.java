package com.lenskart.financeConsumer.model;

import com.lenskart.core.model.HubMaster;
import com.lenskart.core.model.Product;
import com.lenskart.orderops.model.OrderAddressUpdate;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "invoice_details")
public class InvoiceDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "shipping_package_id", nullable = false)
    private String shippingPackageId;

    @Column(name = "invoice_code", nullable = false)
    private String invoiceCode;

    @Column(name = "increment_id", nullable = false)
    private Integer incrementId;

    @OneToOne
    @JoinColumn(name = "uw_item_id", referencedColumnName = "uw_item_id")
    private OrderItemGSTDetail uwItemId;

    @OneToOne
    @JoinColumn(name = "seller_id", referencedColumnName = "id")
    private HubMaster sellerId;

    @OneToOne
    @JoinColumn(name = "customer_id", referencedColumnName = "entity_id")
    private OrderAddressUpdate customerId;
    /*
    @Column(name = "shipper_address", nullable = false)
    private HubMaster shipperAddress;

    @Column(name = "billing_address", nullable = false)
    private OrderAddressUpdate billingAddress;*/

    @Column(name = "awb_number", nullable = false)
    private String awbNumber;

    @Column(name = "export_date", nullable = false)
    private Date exportDate;
/*
    // same as invoice_code
    @Column(name = "invoice_no", nullable = false)
    private String invoiceNo;
*/

    @Column(name = "invoice_date", nullable = false)
    private Date invoiceDate;

/*
    to be added in hub_master table
    @Column(name = "shipper_tax_number", nullable = false)
    private String shipperTaxNumber;

    to be added in order_address_update table
    @Column(name = "consignee_tax_number", nullable = false)
    private String consigneeTaxNumber;*/

    @Column(name = "place_of_supply", nullable = false)
    private String placeOfSupply;

//    private OrderAddressUpdate billingAddress;

    @OneToOne
    @JoinColumn(name = "product_id", referencedColumnName = "product_id")
    private Product product;

/*
    can be referenced from sellerId object
    @Column(name = "gstin", nullable = false)
    private String gstin;
*/

    @Column(name = "tax_label")
    private String taxLabel;

    @Column(name = "tax_number")
    private String taxNumber;

    @ManyToOne
    @JoinColumn(name = "einvoice_details_id", referencedColumnName = "id")
    private Einvoicing einvoicing;

    private transient Double totalAmount = 0.0;
    private transient Double totalSubTotal = 0.0;
    private transient Double totalIgst = 0.0;
    private transient Double totalCgst = 0.0;
    private transient Double totalSgst = 0.0;
    private transient String qrCode;
    private transient String irnNo;
    private transient String shippingPackageIdQr;
    private transient String incrementIdQr;


    @Column(name = "total_weight", nullable = false)
    private Double totalWeight;
}
