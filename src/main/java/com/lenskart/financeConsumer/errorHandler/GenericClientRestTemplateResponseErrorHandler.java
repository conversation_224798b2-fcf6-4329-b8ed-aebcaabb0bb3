package com.lenskart.financeConsumer.errorHandler;

import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;

@Component
public class GenericClientRestTemplateResponseErrorHandler implements ResponseErrorHandler {
    @Override
    public boolean hasError(ClientHttpResponse httpResponse) throws IOException {
        return false;
    }

    @Override
    public void handleError(ClientHttpResponse httpResponse) throws IOException {
    }
}
