package com.lenskart.financeConsumer.exceptions;
public class UniCommerceException extends Exception {

    /**
     *
     */
    private static final long serialVersionUID = 5267836349040279893L;

    public UniCommerceException() {
    }

    public UniCommerceException(String message) {
        super(message);
    }

    public UniCommerceException(Throwable cause) {
        super(cause);
    }

    public UniCommerceException(String message, Throwable cause) {
        super(message, cause);
    }

    public UniCommerceException(String message, Throwable cause,
                                boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

