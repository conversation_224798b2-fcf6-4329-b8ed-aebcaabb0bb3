FROM public.ecr.aws/z5y1f1y8/maven:3.6-amazoncorretto-8 as builder

ARG TARGETPLATFORM
ARG BUILDPLATFORM
RUN echo "Building on $BUILDPLATFORM, for $TARGETPLATFORM"

COPY ./pom.xml /finance-consumer/
WORKDIR /finance-consumer/
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/
RUN --mount=type=cache,target=/root/.m2 mvn -e -B dependency:resolve

RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java.zip .
RUN unzip newrelic-java.zip

COPY . .
RUN --mount=type=cache,target=/root/.m2 mvn clean package -DskipTests
FROM public.ecr.aws/z5y1f1y8/maven:3.6-amazoncorretto-8

COPY --from=builder /finance-consumer/target/*.jar /finance-consumer/
COPY --from=builder /finance-consumer/newrelic/ /finance-consumer/newrelic/
RUN chown -R 1000 /finance-consumer/ && chown -R 1000 /var

EXPOSE 8080
WORKDIR /finance-consumer/
ENTRYPOINT ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom -javaagent:/finance-consumer/newrelic/newrelic.jar -Dspring.profiles.active=$PROFILE $JVM_ARGS -jar /finance-consumer/*.jar"]
